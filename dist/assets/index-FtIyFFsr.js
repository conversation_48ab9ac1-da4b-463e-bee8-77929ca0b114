import{r as f,b as It,d as Ot,a as Ut,R as Sr}from"./vendor-C11UhA-F.js";import{u as Oe,L as R,a as js,N as Ke,b as Qr,c as $t,R as mr,d as M,B as Mt}from"./router-xiJnVC2z.js";import{c as ur}from"./supabase-k9PC1rwV.js";import{S as ue,a as ge,U as Ee,C as L,W as K,X as oe,M as xr,F as Lt,T as qt,I as Bt,Y as zt,P as Bs,b as Ie,c as rs,d as Wt,e as Me,f as ws,g as re,h as ae,A as Is,L as Os,i as Ce,E as ne,j as Se,Z as Te,k as De,l as Je,m as H,n as sr,o as Yt,p as Vt,q as Gt,r as Ht,s as Kt,t as Zr,u as Jt,v as Z,w as hr,G as Xt,x as Qt,H as Zt,y as zs,z as gr,B as xs,D as hs,J as Us,K as V,R as le,N as he,O as qe,Q as ea,V as Re,_ as we,$ as sa,a0 as ra,a1 as ce,a2 as et,a3 as Be,a4 as rr,a5 as tr,a6 as Ns,a7 as Ne,a8 as ta,a9 as cs,aa as fr,ab as ze,ac as Xs,ad as pr,ae as Cr,af as st,ag as aa,ah as br,ai as la,aj as na,ak as rt,al as gs,am as Er,an as Pr,ao as ia,ap as oa,aq as Rr,ar,as as os,at as $s,au as ca,av as da,aw as ma,ax as ua,ay as xa}from"./ui-dhpS7OUc.js";import{_ as ha}from"./charts-DX-vEWWF.js";(function(){const s=document.createElement("link").relList;if(s&&s.supports&&s.supports("modulepreload"))return;for(const l of document.querySelectorAll('link[rel="modulepreload"]'))a(l);new MutationObserver(l=>{for(const n of l)if(n.type==="childList")for(const i of n.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&a(i)}).observe(document,{childList:!0,subtree:!0});function r(l){const n={};return l.integrity&&(n.integrity=l.integrity),l.referrerPolicy&&(n.referrerPolicy=l.referrerPolicy),l.crossOrigin==="use-credentials"?n.credentials="include":l.crossOrigin==="anonymous"?n.credentials="omit":n.credentials="same-origin",n}function a(l){if(l.ep)return;l.ep=!0;const n=r(l);fetch(l.href,n)}})();var tt={exports:{}},Ws={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ga=f,fa=Symbol.for("react.element"),pa=Symbol.for("react.fragment"),ba=Object.prototype.hasOwnProperty,ya=ga.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,ja={key:!0,ref:!0,__self:!0,__source:!0};function at(t,s,r){var a,l={},n=null,i=null;r!==void 0&&(n=""+r),s.key!==void 0&&(n=""+s.key),s.ref!==void 0&&(i=s.ref);for(a in s)ba.call(s,a)&&!ja.hasOwnProperty(a)&&(l[a]=s[a]);if(t&&t.defaultProps)for(a in s=t.defaultProps,s)l[a]===void 0&&(l[a]=s[a]);return{$$typeof:fa,type:t,key:n,ref:i,props:l,_owner:ya.current}}Ws.Fragment=pa;Ws.jsx=at;Ws.jsxs=at;tt.exports=Ws;var e=tt.exports,lt,Ar=It;lt=Ar.createRoot,Ar.hydrateRoot;let wa={data:""},Na=t=>typeof window=="object"?((t?t.querySelector("#_goober"):window._goober)||Object.assign((t||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:t||wa,va=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,_a=/\/\*[^]*?\*\/|  +/g,Tr=/\n+/g,Ae=(t,s)=>{let r="",a="",l="";for(let n in t){let i=t[n];n[0]=="@"?n[1]=="i"?r=n+" "+i+";":a+=n[1]=="f"?Ae(i,n):n+"{"+Ae(i,n[1]=="k"?"":s)+"}":typeof i=="object"?a+=Ae(i,s?s.replace(/([^,])+/g,c=>n.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,o=>/&/.test(o)?o.replace(/&/g,c):c?c+" "+o:o)):n):i!=null&&(n=/^--/.test(n)?n:n.replace(/[A-Z]/g,"-$&").toLowerCase(),l+=Ae.p?Ae.p(n,i):n+":"+i+";")}return r+(s&&l?s+"{"+l+"}":l)+a},_e={},nt=t=>{if(typeof t=="object"){let s="";for(let r in t)s+=r+nt(t[r]);return s}return t},ka=(t,s,r,a,l)=>{let n=nt(t),i=_e[n]||(_e[n]=(o=>{let d=0,m=11;for(;d<o.length;)m=101*m+o.charCodeAt(d++)>>>0;return"go"+m})(n));if(!_e[i]){let o=n!==t?t:(d=>{let m,h,p=[{}];for(;m=va.exec(d.replace(_a,""));)m[4]?p.shift():m[3]?(h=m[3].replace(Tr," ").trim(),p.unshift(p[0][h]=p[0][h]||{})):p[0][m[1]]=m[2].replace(Tr," ").trim();return p[0]})(t);_e[i]=Ae(l?{["@keyframes "+i]:o}:o,r?"":"."+i)}let c=r&&_e.g?_e.g:null;return r&&(_e.g=_e[i]),((o,d,m,h)=>{h?d.data=d.data.replace(h,o):d.data.indexOf(o)===-1&&(d.data=m?o+d.data:d.data+o)})(_e[i],s,a,c),i},Sa=(t,s,r)=>t.reduce((a,l,n)=>{let i=s[n];if(i&&i.call){let c=i(r),o=c&&c.props&&c.props.className||/^go/.test(c)&&c;i=o?"."+o:c&&typeof c=="object"?c.props?"":Ae(c,""):c===!1?"":c}return a+l+(i??"")},"");function Ys(t){let s=this||{},r=t.call?t(s.p):t;return ka(r.unshift?r.raw?Sa(r,[].slice.call(arguments,1),s.p):r.reduce((a,l)=>Object.assign(a,l&&l.call?l(s.p):l),{}):r,Na(s.target),s.g,s.o,s.k)}let it,lr,nr;Ys.bind({g:1});let Pe=Ys.bind({k:1});function Ca(t,s,r,a){Ae.p=s,it=t,lr=r,nr=a}function Ue(t,s){let r=this||{};return function(){let a=arguments;function l(n,i){let c=Object.assign({},n),o=c.className||l.className;r.p=Object.assign({theme:lr&&lr()},c),r.o=/ *go\d+/.test(o),c.className=Ys.apply(r,a)+(o?" "+o:"");let d=t;return t[0]&&(d=c.as||t,delete c.as),nr&&d[0]&&nr(c),it(d,c)}return l}}var Ea=t=>typeof t=="function",Ms=(t,s)=>Ea(t)?t(s):t,Pa=(()=>{let t=0;return()=>(++t).toString()})(),ot=(()=>{let t;return()=>{if(t===void 0&&typeof window<"u"){let s=matchMedia("(prefers-reduced-motion: reduce)");t=!s||s.matches}return t}})(),Ra=20,ct=(t,s)=>{switch(s.type){case 0:return{...t,toasts:[s.toast,...t.toasts].slice(0,Ra)};case 1:return{...t,toasts:t.toasts.map(n=>n.id===s.toast.id?{...n,...s.toast}:n)};case 2:let{toast:r}=s;return ct(t,{type:t.toasts.find(n=>n.id===r.id)?1:0,toast:r});case 3:let{toastId:a}=s;return{...t,toasts:t.toasts.map(n=>n.id===a||a===void 0?{...n,dismissed:!0,visible:!1}:n)};case 4:return s.toastId===void 0?{...t,toasts:[]}:{...t,toasts:t.toasts.filter(n=>n.id!==s.toastId)};case 5:return{...t,pausedAt:s.time};case 6:let l=s.time-(t.pausedAt||0);return{...t,pausedAt:void 0,toasts:t.toasts.map(n=>({...n,pauseDuration:n.pauseDuration+l}))}}},As=[],Le={toasts:[],pausedAt:void 0},Ve=t=>{Le=ct(Le,t),As.forEach(s=>{s(Le)})},Aa={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},Ta=(t={})=>{let[s,r]=f.useState(Le),a=f.useRef(Le);f.useEffect(()=>(a.current!==Le&&r(Le),As.push(r),()=>{let n=As.indexOf(r);n>-1&&As.splice(n,1)}),[]);let l=s.toasts.map(n=>{var i,c,o;return{...t,...t[n.type],...n,removeDelay:n.removeDelay||((i=t[n.type])==null?void 0:i.removeDelay)||t?.removeDelay,duration:n.duration||((c=t[n.type])==null?void 0:c.duration)||t?.duration||Aa[n.type],style:{...t.style,...(o=t[n.type])==null?void 0:o.style,...n.style}}});return{...s,toasts:l}},Da=(t,s="blank",r)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:s,ariaProps:{role:"status","aria-live":"polite"},message:t,pauseDuration:0,...r,id:r?.id||Pa()}),vs=t=>(s,r)=>{let a=Da(s,t,r);return Ve({type:2,toast:a}),a.id},B=(t,s)=>vs("blank")(t,s);B.error=vs("error");B.success=vs("success");B.loading=vs("loading");B.custom=vs("custom");B.dismiss=t=>{Ve({type:3,toastId:t})};B.remove=t=>Ve({type:4,toastId:t});B.promise=(t,s,r)=>{let a=B.loading(s.loading,{...r,...r?.loading});return typeof t=="function"&&(t=t()),t.then(l=>{let n=s.success?Ms(s.success,l):void 0;return n?B.success(n,{id:a,...r,...r?.success}):B.dismiss(a),l}).catch(l=>{let n=s.error?Ms(s.error,l):void 0;n?B.error(n,{id:a,...r,...r?.error}):B.dismiss(a)}),t};var Fa=(t,s)=>{Ve({type:1,toast:{id:t,height:s}})},Ia=()=>{Ve({type:5,time:Date.now()})},ds=new Map,Oa=1e3,Ua=(t,s=Oa)=>{if(ds.has(t))return;let r=setTimeout(()=>{ds.delete(t),Ve({type:4,toastId:t})},s);ds.set(t,r)},$a=t=>{let{toasts:s,pausedAt:r}=Ta(t);f.useEffect(()=>{if(r)return;let n=Date.now(),i=s.map(c=>{if(c.duration===1/0)return;let o=(c.duration||0)+c.pauseDuration-(n-c.createdAt);if(o<0){c.visible&&B.dismiss(c.id);return}return setTimeout(()=>B.dismiss(c.id),o)});return()=>{i.forEach(c=>c&&clearTimeout(c))}},[s,r]);let a=f.useCallback(()=>{r&&Ve({type:6,time:Date.now()})},[r]),l=f.useCallback((n,i)=>{let{reverseOrder:c=!1,gutter:o=8,defaultPosition:d}=i||{},m=s.filter(x=>(x.position||d)===(n.position||d)&&x.height),h=m.findIndex(x=>x.id===n.id),p=m.filter((x,g)=>g<h&&x.visible).length;return m.filter(x=>x.visible).slice(...c?[p+1]:[0,p]).reduce((x,g)=>x+(g.height||0)+o,0)},[s]);return f.useEffect(()=>{s.forEach(n=>{if(n.dismissed)Ua(n.id,n.removeDelay);else{let i=ds.get(n.id);i&&(clearTimeout(i),ds.delete(n.id))}})},[s]),{toasts:s,handlers:{updateHeight:Fa,startPause:Ia,endPause:a,calculateOffset:l}}},Ma=Pe`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,La=Pe`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,qa=Pe`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,Ba=Ue("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${t=>t.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${Ma} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${La} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${t=>t.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${qa} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,za=Pe`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,Wa=Ue("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${t=>t.secondary||"#e0e0e0"};
  border-right-color: ${t=>t.primary||"#616161"};
  animation: ${za} 1s linear infinite;
`,Ya=Pe`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,Va=Pe`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,Ga=Ue("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${t=>t.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${Ya} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${Va} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${t=>t.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,Ha=Ue("div")`
  position: absolute;
`,Ka=Ue("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,Ja=Pe`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,Xa=Ue("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${Ja} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,Qa=({toast:t})=>{let{icon:s,type:r,iconTheme:a}=t;return s!==void 0?typeof s=="string"?f.createElement(Xa,null,s):s:r==="blank"?null:f.createElement(Ka,null,f.createElement(Wa,{...a}),r!=="loading"&&f.createElement(Ha,null,r==="error"?f.createElement(Ba,{...a}):f.createElement(Ga,{...a})))},Za=t=>`
0% {transform: translate3d(0,${t*-200}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,el=t=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${t*-150}%,-1px) scale(.6); opacity:0;}
`,sl="0%{opacity:0;} 100%{opacity:1;}",rl="0%{opacity:1;} 100%{opacity:0;}",tl=Ue("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,al=Ue("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,ll=(t,s)=>{let r=t.includes("top")?1:-1,[a,l]=ot()?[sl,rl]:[Za(r),el(r)];return{animation:s?`${Pe(a)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${Pe(l)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},nl=f.memo(({toast:t,position:s,style:r,children:a})=>{let l=t.height?ll(t.position||s||"top-center",t.visible):{opacity:0},n=f.createElement(Qa,{toast:t}),i=f.createElement(al,{...t.ariaProps},Ms(t.message,t));return f.createElement(tl,{className:t.className,style:{...l,...r,...t.style}},typeof a=="function"?a({icon:n,message:i}):f.createElement(f.Fragment,null,n,i))});Ca(f.createElement);var il=({id:t,className:s,style:r,onHeightUpdate:a,children:l})=>{let n=f.useCallback(i=>{if(i){let c=()=>{let o=i.getBoundingClientRect().height;a(t,o)};c(),new MutationObserver(c).observe(i,{subtree:!0,childList:!0,characterData:!0})}},[t,a]);return f.createElement("div",{ref:n,className:s,style:r},l)},ol=(t,s)=>{let r=t.includes("top"),a=r?{top:0}:{bottom:0},l=t.includes("center")?{justifyContent:"center"}:t.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:ot()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${s*(r?1:-1)}px)`,...a,...l}},cl=Ys`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,Ss=16,dl=({reverseOrder:t,position:s="top-center",toastOptions:r,gutter:a,children:l,containerStyle:n,containerClassName:i})=>{let{toasts:c,handlers:o}=$a(r);return f.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:Ss,left:Ss,right:Ss,bottom:Ss,pointerEvents:"none",...n},className:i,onMouseEnter:o.startPause,onMouseLeave:o.endPause},c.map(d=>{let m=d.position||s,h=o.calculateOffset(d,{reverseOrder:t,gutter:a,defaultPosition:s}),p=ol(m,h);return f.createElement(il,{id:d.id,key:d.id,onHeightUpdate:o.updateHeight,className:d.visible?cl:"",style:p},d.type==="custom"?Ms(d.message,d):l?l(d):f.createElement(nl,{toast:d,position:m}))}))},k=B;const ml={BASE_URL:"/",DEV:!1,MODE:"production",PROD:!0,SSR:!1,VITE_SUPABASE_ANON_KEY:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp2c3lvenNsdHBjenp1d29uY3pzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk3NDIxMDEsImV4cCI6MjA2NTMxODEwMX0.fjnDFYSyWl3lzm8WM0EU2Gq5EgE5WkdnGO9pUKAlnzQ",VITE_SUPABASE_URL:"https://zvsyozsltpczzuwonczs.supabase.co"},Dr=t=>{let s;const r=new Set,a=(m,h)=>{const p=typeof m=="function"?m(s):m;if(!Object.is(p,s)){const x=s;s=h??(typeof p!="object"||p===null)?p:Object.assign({},s,p),r.forEach(g=>g(s,x))}},l=()=>s,o={setState:a,getState:l,getInitialState:()=>d,subscribe:m=>(r.add(m),()=>r.delete(m)),destroy:()=>{(ml?"production":void 0)!=="production"&&console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),r.clear()}},d=s=t(a,l,o);return o},ul=t=>t?Dr(t):Dr;var dt={exports:{}},mt={},ut={exports:{}},xt={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var es=f;function xl(t,s){return t===s&&(t!==0||1/t===1/s)||t!==t&&s!==s}var hl=typeof Object.is=="function"?Object.is:xl,gl=es.useState,fl=es.useEffect,pl=es.useLayoutEffect,bl=es.useDebugValue;function yl(t,s){var r=s(),a=gl({inst:{value:r,getSnapshot:s}}),l=a[0].inst,n=a[1];return pl(function(){l.value=r,l.getSnapshot=s,Qs(l)&&n({inst:l})},[t,r,s]),fl(function(){return Qs(l)&&n({inst:l}),t(function(){Qs(l)&&n({inst:l})})},[t]),bl(r),r}function Qs(t){var s=t.getSnapshot;t=t.value;try{var r=s();return!hl(t,r)}catch{return!0}}function jl(t,s){return s()}var wl=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?jl:yl;xt.useSyncExternalStore=es.useSyncExternalStore!==void 0?es.useSyncExternalStore:wl;ut.exports=xt;var Nl=ut.exports;/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Vs=f,vl=Nl;function _l(t,s){return t===s&&(t!==0||1/t===1/s)||t!==t&&s!==s}var kl=typeof Object.is=="function"?Object.is:_l,Sl=vl.useSyncExternalStore,Cl=Vs.useRef,El=Vs.useEffect,Pl=Vs.useMemo,Rl=Vs.useDebugValue;mt.useSyncExternalStoreWithSelector=function(t,s,r,a,l){var n=Cl(null);if(n.current===null){var i={hasValue:!1,value:null};n.current=i}else i=n.current;n=Pl(function(){function o(x){if(!d){if(d=!0,m=x,x=a(x),l!==void 0&&i.hasValue){var g=i.value;if(l(g,x))return h=g}return h=x}if(g=h,kl(m,x))return g;var u=a(x);return l!==void 0&&l(g,u)?(m=x,g):(m=x,h=u)}var d=!1,m,h,p=r===void 0?null:r;return[function(){return o(s())},p===null?void 0:function(){return o(p())}]},[s,r,a,l]);var c=Sl(t,n[0],n[1]);return El(function(){i.hasValue=!0,i.value=c},[c]),Rl(c),c};dt.exports=mt;var Al=dt.exports;const Tl=Ot(Al),ht={BASE_URL:"/",DEV:!1,MODE:"production",PROD:!0,SSR:!1,VITE_SUPABASE_ANON_KEY:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp2c3lvenNsdHBjenp1d29uY3pzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk3NDIxMDEsImV4cCI6MjA2NTMxODEwMX0.fjnDFYSyWl3lzm8WM0EU2Gq5EgE5WkdnGO9pUKAlnzQ",VITE_SUPABASE_URL:"https://zvsyozsltpczzuwonczs.supabase.co"},{useDebugValue:Dl}=Ut,{useSyncExternalStoreWithSelector:Fl}=Tl;let Fr=!1;const Il=t=>t;function Ol(t,s=Il,r){(ht?"production":void 0)!=="production"&&r&&!Fr&&(console.warn("[DEPRECATED] Use `createWithEqualityFn` instead of `create` or use `useStoreWithEqualityFn` instead of `useStore`. They can be imported from 'zustand/traditional'. https://github.com/pmndrs/zustand/discussions/1937"),Fr=!0);const a=Fl(t.subscribe,t.getState,t.getServerState||t.getInitialState,s,r);return Dl(a),a}const Ir=t=>{(ht?"production":void 0)!=="production"&&typeof t!="function"&&console.warn("[DEPRECATED] Passing a vanilla store will be unsupported in a future version. Instead use `import { useStore } from 'zustand'`.");const s=typeof t=="function"?ul(t):t,r=(a,l)=>Ol(s,a,l);return Object.assign(r,s),r},Gs=t=>t?Ir(t):Ir,Ul="https://zvsyozsltpczzuwonczs.supabase.co",Or="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp2c3lvenNsdHBjenp1d29uY3pzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk3NDIxMDEsImV4cCI6MjA2NTMxODEwMX0.fjnDFYSyWl3lzm8WM0EU2Gq5EgE5WkdnGO9pUKAlnzQ";let $e=null;const y=(()=>{if($e)return $e;if(typeof window<"u"&&window.__supabase_client__&&window.__supabase_client_initialized__)return $e=window.__supabase_client__,$e;if(typeof global<"u"&&global.__supabase_client__&&global.__supabase_client_initialized__)return $e=global.__supabase_client__,$e;const t=ur(Ul,Or,{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0},realtime:{params:{eventsPerSecond:2}},global:{headers:{apikey:Or,"X-Client-Info":"herbal-store@1.0.0","Content-Type":"application/json",Accept:"application/json",Prefer:"return=representation"}},db:{schema:"public"}});return $e=t,typeof window<"u"?(window.__supabase_client__=t,window.__supabase_client_initialized__=!0):typeof global<"u"&&(global.__supabase_client__=t,global.__supabase_client_initialized__=!0),t})();let Ur=!1;!Ur&&typeof window<"u"&&(y.auth.onAuthStateChange((t,s)=>{switch(t){case"SIGNED_IN":break;case"SIGNED_OUT":sessionStorage.removeItem("lastLoggedUser"),localStorage.removeItem("sb-zvsyozsltpczzuwonczs-auth-token");break}}),Ur=!0);const Ge=Gs((t,s)=>({products:[],categories:[],featuredProducts:[],isLoading:!1,filters:{category:"",subcategory:"",priceRange:[0,1e3],rating:0,inStock:!1,isPremiumOnly:!1},searchQuery:"",sortBy:"name",fetchProducts:async()=>{t({isLoading:!0});try{const{data:r,error:a}=await y.from("products").select("*").order("created_at",{ascending:!1});if(a)throw a;t({products:r||[],isLoading:!1})}catch(r){console.error("Error fetching products:",r),t({isLoading:!1})}},fetchCategories:async()=>{try{const{data:r,error:a}=await y.from("categories").select("*").order("name");if(a)throw a;t({categories:r||[]})}catch(r){console.error("Error fetching categories:",r)}},fetchFeaturedProducts:async()=>{try{const{data:r,error:a}=await y.from("products").select("*").eq("is_featured",!0).order("created_at",{ascending:!1}).limit(6);if(a)throw a;t({featuredProducts:r||[]})}catch(r){console.error("Error fetching featured products:",r)}},setFilters:r=>{t({filters:{...s().filters,...r}})},setSearchQuery:r=>{t({searchQuery:r})},setSortBy:r=>{t({sortBy:r})},getFilteredProducts:()=>{const{products:r,filters:a,searchQuery:l,sortBy:n}=s();let i=[...r];if(l){const c=l.toLowerCase();i=i.filter(o=>o.name.toLowerCase().includes(c)||o.description.toLowerCase().includes(c))}switch(a.category&&(i=i.filter(c=>c.category===a.category)),i=i.filter(c=>c.price>=a.priceRange[0]&&c.price<=a.priceRange[1]),a.rating>0&&(i=i.filter(c=>c.rating>=a.rating)),a.inStock&&(i=i.filter(c=>c.stock_quantity>0)),a.isPremiumOnly&&(i=i.filter(c=>c.is_premium_only)),n){case"price-low":i.sort((c,o)=>c.price-o.price);break;case"price-high":i.sort((c,o)=>o.price-c.price);break;case"rating":i.sort((c,o)=>o.rating-c.rating);break;case"newest":i.sort((c,o)=>new Date(o.created_at).getTime()-new Date(c.created_at).getTime());break;default:i.sort((c,o)=>c.name.localeCompare(o.name))}return i},addProduct:async r=>{try{const{data:a,error:l}=await y.from("products").insert([r]).select().single();if(l)throw l;return t(n=>({products:[a,...n.products]})),{success:!0}}catch(a){return console.error("Error adding product:",a),{success:!1,error:a.message}}}})),ms=async()=>{try{await y.auth.signOut();const t=[];for(let r=0;r<localStorage.length;r++){const a=localStorage.key(r);a&&(a.includes("supabase")||a.includes("sb-"))&&t.push(a)}t.forEach(r=>localStorage.removeItem(r));const s=[];for(let r=0;r<sessionStorage.length;r++){const a=sessionStorage.key(r);a&&(a.includes("supabase")||a.includes("sb-")||a.includes("lastLoggedUser"))&&s.push(a)}s.forEach(r=>sessionStorage.removeItem(r)),console.log("✅ All authentication data cleared")}catch(t){console.error("Error clearing auth data:",t)}},$l=async()=>{try{const{data:{session:t},error:s}=await y.auth.getSession();return s?(console.error("Session check error:",s),!1):!!t}catch(t){return console.error("Error checking session:",t),!1}},Ml=async()=>{try{const{data:{session:t},error:s}=await y.auth.getSession();if(s)return console.error("Session error:",s),await ms(),!1;if(!t)return!1;const r=t.expires_at,a=Math.floor(Date.now()/1e3);if(r-a<300){const{data:n,error:i}=await y.auth.refreshSession();return i?(console.error("Token refresh error:",i),await ms(),!1):!!n.session}return!0}catch(t){return console.error("Error refreshing session:",t),await ms(),!1}},Ll=async()=>{try{await $l()?await Ml():await ms()}catch(t){console.error("Error initializing auth:",t),await ms()}},Cs={1:250,2:100,3:50,4:25,5:10,6:5,7:2,8:1,9:.5,10:.25},He=10;class ql{async processMultiLevelReferral(s,r,a){const l=Date.now(),n={success:!1,totalBonusDistributed:0,levelsProcessed:0,chainUsers:[],errors:[],warnings:[],processingTime:0},i=await this.startAuditLog(s,r,a);n.auditLogId=i;try{if(!s||!r||!a)return n.errors.push("Missing required parameters"),await this.logAuditError(i,"VALIDATION_ERROR","Missing required parameters"),n;if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(r))return n.errors.push("Invalid email format"),await this.logAuditError(i,"VALIDATION_ERROR","Invalid email format"),n;if(a.length<6||a.length>20)return n.errors.push("Invalid referral code format"),await this.logAuditError(i,"VALIDATION_ERROR","Invalid referral code format"),n;const{data:o,error:d}=await y.from("users").select("id, email, full_name, is_premium, referred_by").eq("id",s).single();if(d||!o)return n.errors.push("New premium user not found"),await this.logAuditError(i,"USER_NOT_FOUND",`User ${s} not found: ${d?.message}`),n;if(!o.is_premium)return n.errors.push("User is not premium - no bonuses will be distributed"),await this.logAuditError(i,"PREMIUM_VALIDATION_FAILED",`User ${s} is not premium`),n;const{data:m}=await y.from("referrals").select("id, status").eq("referred_user_id",s).single();if(m&&m.status==="completed")return n.errors.push("Referral already processed for this user"),n.warnings.push("Duplicate processing attempt detected"),await this.logAuditError(i,"DUPLICATE_PROCESSING",`Referral already exists for user ${s}`),n;const{data:h,error:p}=await y.from("users").select("id, email, full_name, referral_code, referred_by, is_premium, wallet_balance").eq("referral_code",a).single();if(p||!h)return n.errors.push("Invalid referral code - direct referrer not found"),await this.logAuditError(i,"REFERRER_NOT_FOUND",`Referrer with code ${a} not found: ${p?.message}`),n;if(!h.is_premium)return n.errors.push("Direct referrer is not premium - no bonuses will be distributed"),await this.logAuditError(i,"REFERRER_NOT_PREMIUM",`Referrer ${h.id} is not premium`),n;if(h.id===s)return n.errors.push("Self-referral is not allowed"),await this.logAuditError(i,"SELF_REFERRAL",`User ${s} attempted self-referral`),n;const x=await this.buildReferralChain(h);n.chainUsers=x;const g=await this.getConfigurableBonusAmounts(),u=await this.distributeBonusesWithErrorHandling(x,g,s,r,i),j=await this.createReferralRecord(h.id,s,g[1]||Cs[1]);return n.referralId=j,await this.logMultiLevelReferralActivity(s,u,x),n.success=!0,n.totalBonusDistributed=u.reduce((b,N)=>b+N.amount,0),n.levelsProcessed=u.length,n.processingTime=Date.now()-l,await this.logAuditSuccess(i,{totalBonusDistributed:n.totalBonusDistributed,levelsProcessed:n.levelsProcessed,processingTime:n.processingTime,chainLength:x.length,distributions:u}),n}catch(c){return console.error("Multi-level referral processing error:",c),n.errors.push(`Processing failed: ${c.message}`),n.processingTime=Date.now()-l,await this.logAuditError(i,"CRITICAL_ERROR",`Processing failed: ${c.message}`,{stack:c.stack,processingTime:n.processingTime}),n}}async buildReferralChain(s){const r=[],a=new Set;let l=s,n=1;for(;l&&n<=He;){if(a.has(l.id)){console.warn(`Circular reference detected at level ${n}, stopping chain traversal`);break}if(a.add(l.id),l.is_premium)r.push({id:l.id,email:l.email,full_name:l.full_name,referral_code:l.referral_code,referred_by:l.referred_by,is_premium:l.is_premium,wallet_balance:l.wallet_balance,level:n});else{console.log(`User at level ${n} is not premium, stopping chain traversal`);break}if(!l.referred_by)break;const{data:i,error:c}=await y.from("users").select("id, email, full_name, referral_code, referred_by, is_premium, wallet_balance").eq("referral_code",l.referred_by).single();if(c||!i){console.log(`No referrer found for level ${n+1}, stopping chain traversal`);break}l=i,n++}return r}async getConfigurableBonusAmounts(){try{const{data:s,error:r}=await y.from("admin_settings").select("setting_key, setting_value").like("setting_key","referral_level_%_bonus");if(r)return console.warn("Failed to load configurable bonus amounts, using defaults:",r),Cs;const a={};s?.forEach(l=>{const n=l.setting_key.match(/referral_level_(\d+)_bonus/);if(n){const i=parseInt(n[1]),c=parseFloat(l.setting_value);!isNaN(i)&&!isNaN(c)&&i>=1&&i<=He&&(a[i]=c)}});for(let l=1;l<=He;l++)a[l]||(a[l]=Cs[l]);return a}catch(s){return console.warn("Error loading configurable bonus amounts, using defaults:",s),Cs}}async distributeBonuses(s,r,a,l){const n=[];for(const i of s){const c=r[i.level];if(!(!c||c<=0))try{const{error:o}=await y.from("users").update({wallet_balance:y.sql`wallet_balance + ${c}`,updated_at:new Date().toISOString()}).eq("id",i.id);if(o){console.error(`Failed to update wallet for user ${i.id} at level ${i.level}:`,o);continue}const{error:d}=await y.from("wallet_transactions").insert({user_id:i.id,type:"credit",amount:c,description:`Level ${i.level} referral bonus for ${l} becoming premium`,reference_type:"multi_level_referral",reference_id:a,metadata:{referral_level:i.level,referred_user_id:a,referred_user_email:l,bonus_type:"multi_level_referral"}});d&&console.error(`Failed to create transaction for user ${i.id} at level ${i.level}:`,d),n.push({userId:i.id,level:i.level,amount:c,userName:i.full_name,userEmail:i.email}),console.log(`Level ${i.level} bonus of ₹${c} credited to ${i.email}`)}catch(o){console.error(`Error distributing bonus to user ${i.id} at level ${i.level}:`,o)}}return n}async createReferralRecord(s,r,a){try{const{data:l,error:n}=await y.from("referrals").insert({referrer_id:s,referred_user_id:r,bonus_amount:a,status:"completed",created_at:new Date().toISOString()}).select("id").single();return n?(console.error("Failed to create referral record:",n),null):l.id}catch(l){return console.error("Error creating referral record:",l),null}}async logMultiLevelReferralActivity(s,r,a){try{const l={new_premium_user_id:s,total_levels_processed:r.length,total_bonus_distributed:r.reduce((n,i)=>n+i.amount,0),distributions:r,referral_chain:a.map(n=>({user_id:n.id,level:n.level,email:n.email,full_name:n.full_name}))};await y.from("referral_audit_log").insert({user_id:s,action:"multi_level_referral_processed",details:l,created_at:new Date().toISOString()})}catch(l){console.error("Error logging multi-level referral activity:",l)}}async isMultiLevelReferralEnabled(){try{const{data:s,error:r}=await y.from("admin_settings").select("setting_value").eq("setting_key","multi_level_referral_enabled").single();return r||!s?!0:s.setting_value==="true"}catch(s){return console.warn("Error checking multi-level referral status, defaulting to enabled:",s),!0}}async getMaxReferralLevels(){try{const{data:s,error:r}=await y.from("admin_settings").select("setting_value").eq("setting_key","max_referral_levels").single();if(r||!s)return He;const a=parseInt(s.setting_value);return isNaN(a)?He:Math.min(a,20)}catch(s){return console.warn("Error getting max referral levels, using default:",s),He}}async startAuditLog(s,r,a){try{const{data:l,error:n}=await y.from("referral_audit_log").insert({user_id:s,action:"multi_level_referral_started",details:{new_premium_user_id:s,new_premium_user_email:r,referral_code:a,started_at:new Date().toISOString(),status:"processing"},created_at:new Date().toISOString()}).select("id").single();return n?(console.error("Failed to start audit log:",n),null):l.id}catch(l){return console.error("Error starting audit log:",l),null}}async logAuditError(s,r,a,l){if(s)try{await y.from("referral_audit_log").insert({user_id:null,action:"multi_level_referral_error",details:{audit_log_id:s,error_type:r,error_message:a,additional_data:l,occurred_at:new Date().toISOString()},created_at:new Date().toISOString()})}catch(n){console.error("Failed to log audit error:",n)}}async logAuditSuccess(s,r){if(s)try{await y.from("referral_audit_log").update({details:y.sql`details || ${JSON.stringify({...r,completed_at:new Date().toISOString(),status:"completed"})}::jsonb`,updated_at:new Date().toISOString()}).eq("id",s)}catch(a){console.error("Failed to log audit success:",a)}}async distributeBonusesWithErrorHandling(s,r,a,l,n){const i=[];try{const c=s.filter(m=>r[m.level]&&r[m.level]>0).map(m=>({user_id:m.id,amount:r[m.level],description:`Level ${m.level} referral bonus for ${l} becoming premium`,reference_type:"multi_level_referral",reference_id:a,metadata:{referral_level:m.level,referred_user_id:a,referred_user_email:l,bonus_type:"multi_level_referral",audit_log_id:n}}));if(c.length===0)return console.warn("No valid bonus distributions to process"),i;const{data:o,error:d}=await y.rpc("batch_wallet_updates",{p_updates:c});if(d)throw new Error(`Batch processing failed: ${d.message}`);if(o?.results)for(const m of o.results){const h=m.user_id,p=s.find(x=>x.id===h);p&&m.result?.success?(i.push({userId:p.id,level:p.level,amount:r[p.level],userName:p.full_name,userEmail:p.email}),console.log(`Level ${p.level} bonus of ₹${r[p.level]} credited to ${p.email}`)):p&&!m.result?.success&&await this.logAuditError(n,"DISTRIBUTION_ERROR",`Failed to distribute bonus to user ${p.id} at level ${p.level}: ${m.result?.error}`,{user_id:p.id,level:p.level,amount:r[p.level]})}return await this.logAuditSuccess(n,{batch_processing:{total_attempted:c.length,successful:o?.successful_updates||0,failed:o?.failed_updates||0,total_amount_distributed:o?.total_amount_distributed||0}}),i}catch(c){return console.error("Error in batch bonus distribution:",c),console.log("Falling back to individual processing..."),await this.distributeBonusesIndividually(s,r,a,l,n)}}async distributeBonusesIndividually(s,r,a,l,n){const i=[],c=[];for(const o of s){const d=r[o.level];if(!(!d||d<=0))try{const{data:m,error:h}=await y.rpc("safe_wallet_update",{p_user_id:o.id,p_amount:d,p_description:`Level ${o.level} referral bonus for ${l} becoming premium`,p_reference_type:"multi_level_referral",p_reference_id:a,p_metadata:{referral_level:o.level,referred_user_id:a,referred_user_email:l,bonus_type:"multi_level_referral",audit_log_id:n}});if(h||!m?.success)throw new Error(m?.error||h?.message||"Unknown error");i.push({userId:o.id,level:o.level,amount:d,userName:o.full_name,userEmail:o.email}),console.log(`Level ${o.level} bonus of ₹${d} credited to ${o.email}`)}catch(m){console.error(`Error distributing bonus to user ${o.id} at level ${o.level}:`,m),c.push({user:o,error:m.message}),await this.logAuditError(n,"DISTRIBUTION_ERROR",`Failed to distribute bonus to user ${o.id} at level ${o.level}: ${m.message}`,{user_id:o.id,level:o.level,amount:d})}}return c.length>0&&await this.logAuditError(n,"PARTIAL_DISTRIBUTION_FAILURE",`${c.length} distributions failed out of ${s.length}`,{failed_distributions:c}),i}}const Fe=new ql;class Xe{constructor(){this.monitoringInterval=null,this.isMonitoring=!1}static getInstance(){return Xe.instance||(Xe.instance=new Xe),Xe.instance}startMonitoring(){if(this.isMonitoring){console.log("Referral monitoring already active");return}console.log("Starting automatic referral monitoring..."),this.isMonitoring=!0,this.monitoringInterval=setInterval(async()=>{try{await this.runConsistencyCheck()}catch(s){console.error("Monitoring consistency check failed:",s)}},5*60*1e3),setTimeout(()=>{this.runConsistencyCheck()},3e3)}stopMonitoring(){this.monitoringInterval&&(clearInterval(this.monitoringInterval),this.monitoringInterval=null),this.isMonitoring=!1,console.log("Stopped automatic referral monitoring")}async runConsistencyCheck(){try{console.log("Running referral consistency check...");const s=await this.checkMissingReferrals();if(s.length>0){console.log(`Found ${s.length} missing referrals, processing...`);const r=s.slice(0,5);for(const a of r)try{await this.processUserReferrals(a.id,a.email)}catch(l){console.error(`Failed to process referral for user ${a.email}:`,l)}this.notifyDataUpdate()}localStorage.setItem("last_referral_check",new Date().toISOString())}catch(s){console.error("Consistency check failed:",s)}}async triggerConsistencyCheck(){try{console.log("Starting manual consistency check...");const s=await this.fixAllMissingReferrals();return console.log("Manual consistency check completed:",s),this.notifyDataUpdate(),s}catch(s){throw console.error("Manual consistency check failed:",s),s}}async processUserReferrals(s,r){try{if(console.log("Multi-level referral processing for user:",{userId:s,userEmail:r}),!await Fe.isMultiLevelReferralEnabled())return{success:!1,message:"Multi-level referral system is disabled"};const{data:l}=await y.from("users").select("id, email, full_name, is_premium, referred_by").eq("id",s).single();if(!l)return{success:!1,message:"User not found"};if(!l.is_premium)return{success:!1,message:"User is not premium - no bonuses will be processed"};if(!l.referred_by)return{success:!1,message:"No referral code found for user"};const{data:n}=await y.from("referrals").select("id, status").eq("referred_user_id",s).single();if(n&&n.status==="completed")return{success:!1,message:"Referral already processed for this user"};const i=await Fe.processMultiLevelReferral(s,r,l.referred_by);return i.success?(console.log("Multi-level referral processed successfully:",{userId:s,totalBonusDistributed:i.totalBonusDistributed,levelsProcessed:i.levelsProcessed}),this.notifyDataUpdate(),{success:!0,message:"Multi-level referral processed successfully",totalBonusDistributed:i.totalBonusDistributed,levelsProcessed:i.levelsProcessed,referralId:i.referralId}):(console.error("Multi-level referral processing failed:",i.errors),{success:!1,message:`Referral processing failed: ${i.errors.join(", ")}`,errors:i.errors})}catch(a){return console.error("User referral processing failed:",a),{success:!1,message:`Processing failed: ${a.message}`,error:a.message}}}async checkMissingReferrals(){try{const{data:s,error:r}=await y.from("users").select("id, email, full_name, referred_by, created_at, is_premium").not("referred_by","is",null).neq("referred_by","").eq("is_premium",!0);if(r)throw r;if(!s||s.length===0)return[];const a=[];for(const l of s){const{data:n}=await y.from("referrals").select("id").eq("referred_user_id",l.id).single();n||a.push(l)}return a}catch(s){return console.error("Failed to check missing referrals:",s),[]}}async fixAllMissingReferrals(){try{const s=await this.checkMissingReferrals();if(s.length===0)return{success:!0,message:"No missing referrals found",fixed:0};let r=0;const a=[];for(const l of s)try{await this.processUserReferrals(l.id,l.email),r++}catch(n){a.push({userId:l.id,email:l.email,error:n.message})}return this.notifyDataUpdate(),{success:!0,message:`Fixed ${r} missing referrals`,fixed:r,total:s.length,errors:a}}catch(s){throw console.error("Failed to fix missing referrals:",s),s}}notifyDataUpdate(){localStorage.setItem("referral_data_updated",Date.now().toString()),window.dispatchEvent(new CustomEvent("referralDataUpdated",{detail:{timestamp:Date.now()}}))}getStatus(){return{isMonitoring:this.isMonitoring,lastCheck:localStorage.getItem("last_referral_check")||void 0}}}const gt=Xe.getInstance();typeof window<"u"&&gt.startMonitoring();const J=Gs(t=>({user:null,isAdmin:!1,isLoading:!0,error:null,signIn:async(s,r)=>{try{t({isLoading:!0,error:null});const{data:a,error:l}=await y.auth.signInWithPassword({email:s,password:r});if(l)throw l;const{data:n,error:i}=await y.from("users").select("*").eq("id",a.user?.id).single();if(i)throw i;t({user:a.user,isAdmin:a.user?.email==="<EMAIL>"||a.user?.app_metadata?.is_admin===!0,isLoading:!1})}catch(a){throw t({error:a.message,isLoading:!1}),a}},signUp:async(s,r,a)=>{try{t({isLoading:!0,error:null});const{data:l,error:n}=await y.auth.signUp({email:s,password:r});if(n)throw n;if(l.user){const{error:i}=await y.from("users").insert([{id:l.user.id,email:s,full_name:a,username:s.split("@")[0],is_premium:!1,wallet_balance:0}]);if(i)throw i}t({user:l.user,isAdmin:l.user?.email==="<EMAIL>"||l.user?.app_metadata?.is_admin===!0,isLoading:!1})}catch(l){throw t({error:l.message,isLoading:!1}),l}},signOut:async()=>{try{t({isLoading:!0,error:null});const{error:s}=await y.auth.signOut();if(s)throw s;t({user:null,isAdmin:!1,isLoading:!1})}catch(s){throw t({error:s.message,isLoading:!1}),s}},checkUser:async()=>{try{t({isLoading:!0,error:null});const{data:{user:s},error:r}=await y.auth.getUser();if(r)throw r;if(s){const{data:a,error:l}=await y.from("users").select("*").eq("id",s.id).single();if(l&&l.code!=="PGRST116")throw l;t({user:s,isAdmin:s.email==="<EMAIL>"||s.app_metadata?.is_admin===!0,isLoading:!1})}else t({user:null,isAdmin:!1,isLoading:!1})}catch(s){t({error:s.message,isLoading:!1})}}})),Bl={BASE_URL:"/",DEV:!1,MODE:"production",PROD:!0,SSR:!1,VITE_SUPABASE_ANON_KEY:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp2c3lvenNsdHBjenp1d29uY3pzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk3NDIxMDEsImV4cCI6MjA2NTMxODEwMX0.fjnDFYSyWl3lzm8WM0EU2Gq5EgE5WkdnGO9pUKAlnzQ",VITE_SUPABASE_URL:"https://zvsyozsltpczzuwonczs.supabase.co"};function zl(t,s){let r;try{r=t()}catch{return}return{getItem:l=>{var n;const i=o=>o===null?null:JSON.parse(o,void 0),c=(n=r.getItem(l))!=null?n:null;return c instanceof Promise?c.then(i):i(c)},setItem:(l,n)=>r.setItem(l,JSON.stringify(n,void 0)),removeItem:l=>r.removeItem(l)}}const fs=t=>s=>{try{const r=t(s);return r instanceof Promise?r:{then(a){return fs(a)(r)},catch(a){return this}}}catch(r){return{then(a){return this},catch(a){return fs(a)(r)}}}},Wl=(t,s)=>(r,a,l)=>{let n={getStorage:()=>localStorage,serialize:JSON.stringify,deserialize:JSON.parse,partialize:j=>j,version:0,merge:(j,b)=>({...b,...j}),...s},i=!1;const c=new Set,o=new Set;let d;try{d=n.getStorage()}catch{}if(!d)return t((...j)=>{console.warn(`[zustand persist middleware] Unable to update item '${n.name}', the given storage is currently unavailable.`),r(...j)},a,l);const m=fs(n.serialize),h=()=>{const j=n.partialize({...a()});let b;const N=m({state:j,version:n.version}).then(P=>d.setItem(n.name,P)).catch(P=>{b=P});if(b)throw b;return N},p=l.setState;l.setState=(j,b)=>{p(j,b),h()};const x=t((...j)=>{r(...j),h()},a,l);let g;const u=()=>{var j;if(!d)return;i=!1,c.forEach(N=>N(a()));const b=((j=n.onRehydrateStorage)==null?void 0:j.call(n,a()))||void 0;return fs(d.getItem.bind(d))(n.name).then(N=>{if(N)return n.deserialize(N)}).then(N=>{if(N)if(typeof N.version=="number"&&N.version!==n.version){if(n.migrate)return n.migrate(N.state,N.version);console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return N.state}).then(N=>{var P;return g=n.merge(N,(P=a())!=null?P:x),r(g,!0),h()}).then(()=>{b?.(g,void 0),i=!0,o.forEach(N=>N(g))}).catch(N=>{b?.(void 0,N)})};return l.persist={setOptions:j=>{n={...n,...j},j.getStorage&&(d=j.getStorage())},clearStorage:()=>{d?.removeItem(n.name)},getOptions:()=>n,rehydrate:()=>u(),hasHydrated:()=>i,onHydrate:j=>(c.add(j),()=>{c.delete(j)}),onFinishHydration:j=>(o.add(j),()=>{o.delete(j)})},u(),g||x},Yl=(t,s)=>(r,a,l)=>{let n={storage:zl(()=>localStorage),partialize:u=>u,version:0,merge:(u,j)=>({...j,...u}),...s},i=!1;const c=new Set,o=new Set;let d=n.storage;if(!d)return t((...u)=>{console.warn(`[zustand persist middleware] Unable to update item '${n.name}', the given storage is currently unavailable.`),r(...u)},a,l);const m=()=>{const u=n.partialize({...a()});return d.setItem(n.name,{state:u,version:n.version})},h=l.setState;l.setState=(u,j)=>{h(u,j),m()};const p=t((...u)=>{r(...u),m()},a,l);l.getInitialState=()=>p;let x;const g=()=>{var u,j;if(!d)return;i=!1,c.forEach(N=>{var P;return N((P=a())!=null?P:p)});const b=((j=n.onRehydrateStorage)==null?void 0:j.call(n,(u=a())!=null?u:p))||void 0;return fs(d.getItem.bind(d))(n.name).then(N=>{if(N)if(typeof N.version=="number"&&N.version!==n.version){if(n.migrate)return[!0,n.migrate(N.state,N.version)];console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return[!1,N.state];return[!1,void 0]}).then(N=>{var P;const[A,E]=N;if(x=n.merge(E,(P=a())!=null?P:p),r(x,!0),A)return m()}).then(()=>{b?.(x,void 0),x=a(),i=!0,o.forEach(N=>N(x))}).catch(N=>{b?.(void 0,N)})};return l.persist={setOptions:u=>{n={...n,...u},u.storage&&(d=u.storage)},clearStorage:()=>{d?.removeItem(n.name)},getOptions:()=>n,rehydrate:()=>g(),hasHydrated:()=>i,onHydrate:u=>(c.add(u),()=>{c.delete(u)}),onFinishHydration:u=>(o.add(u),()=>{o.delete(u)})},n.skipHydration||g(),x||p},Vl=(t,s)=>"getStorage"in s||"serialize"in s||"deserialize"in s?((Bl?"production":void 0)!=="production"&&console.warn("[DEPRECATED] `getStorage`, `serialize` and `deserialize` options are deprecated. Use `storage` option instead."),Wl(t,s)):Yl(t,s),Gl=Vl,ts=Gs()(Gl((t,s)=>({items:[],isOpen:!1,total:0,addItem:(r,a=1)=>{const l=s().items;if(l.find(c=>c.id===r.id))t({items:l.map(c=>c.id===r.id?{...c,quantity:c.quantity+a}:c)});else{const c={id:r.id,product:r,quantity:a,productId:r.id};t({items:[...l,c]})}const i=s().getTotalPrice();t({total:i})},removeItem:r=>{t({items:s().items.filter(l=>l.id!==r)});const a=s().getTotalPrice();t({total:a})},updateQuantity:(r,a)=>{if(a<=0){s().removeItem(r);return}t({items:s().items.map(n=>n.id===r?{...n,quantity:a}:n)});const l=s().getTotalPrice();t({total:l})},clearCart:()=>{t({items:[],total:0})},toggleCart:()=>{t({isOpen:!s().isOpen})},getTotalItems:()=>s().items.reduce((r,a)=>r+a.quantity,0),getTotalPrice:()=>s().items.reduce((r,a)=>r+a.product.price*a.quantity,0)}),{name:"cart-storage"})),Hl=()=>{const[t,s]=f.useState(!1),[r,a]=f.useState(!1),{user:l,isAuthenticated:n,logout:i}=J(),{items:c,toggleCart:o,getTotalItems:d}=ts(),{searchQuery:m,setSearchQuery:h}=Ge(),p=Oe(),x=u=>{u.preventDefault(),m.trim()&&p(`/products?search=${encodeURIComponent(m)}`)},g=async()=>{await i(),a(!1),p("/")};return e.jsx("header",{className:"bg-white shadow-lg border-b border-green-100 sticky top-0 z-40",children:e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[e.jsxs("div",{className:"flex items-center justify-between h-16",children:[e.jsxs(R,{to:"/",className:"flex items-center space-x-2 flex-shrink-0",children:[e.jsx("div",{className:"w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center",children:e.jsx("span",{className:"text-white font-bold text-sm sm:text-lg",children:"🌿"})}),e.jsx("span",{className:"text-lg sm:text-xl font-bold text-gray-900",children:"Start Juicce"})]}),e.jsx("form",{onSubmit:x,className:"flex-1 max-w-lg mx-4 lg:mx-8 hidden md:block",children:e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"text",placeholder:"Search products...",value:m,onChange:u=>h(u.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"}),e.jsx(ue,{className:"absolute left-3 top-2.5 h-5 w-5 text-gray-400"})]})}),e.jsxs("div",{className:"flex items-center space-x-2 sm:space-x-4",children:[e.jsxs("button",{onClick:o,className:"relative p-2 text-gray-600 hover:text-green-600 transition-colors touch-target",children:[e.jsx(ge,{className:"h-5 w-5 sm:h-6 sm:w-6"}),d()>0&&e.jsx("span",{className:"absolute -top-1 -right-1 bg-green-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center min-w-[20px]",children:d()})]}),n?e.jsxs("div",{className:"relative hidden sm:block",children:[e.jsxs("button",{onClick:()=>a(!r),className:"flex items-center space-x-2 p-2 text-gray-600 hover:text-green-600 transition-colors touch-target",children:[e.jsx(Ee,{className:"h-5 w-5 sm:h-6 sm:w-6"}),l?.is_premium&&e.jsx(L,{className:"h-4 w-4 text-yellow-500"})]}),r&&e.jsxs("div",{className:"absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50",children:[e.jsxs("div",{className:"px-4 py-2 border-b border-gray-100",children:[e.jsxs("p",{className:"font-medium text-gray-900 flex items-center",children:[l?.full_name,l?.is_premium&&e.jsx(L,{className:"h-4 w-4 text-yellow-500 ml-2"})]}),e.jsx("p",{className:"text-sm text-gray-600",children:l?.email}),e.jsxs("div",{className:"flex items-center mt-1 text-sm text-green-600",children:[e.jsx(K,{className:"h-4 w-4 mr-1"}),"₹",l?.wallet_balance?.toFixed(2)||"0.00"]})]}),e.jsx(R,{to:"/dashboard",className:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 touch-target",onClick:()=>a(!1),children:"Dashboard"}),e.jsx(R,{to:"/orders",className:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 touch-target",onClick:()=>a(!1),children:"Orders"}),e.jsx(R,{to:"/wallet",className:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 touch-target",onClick:()=>a(!1),children:"Wallet"}),l?.is_premium&&e.jsx(R,{to:"/referrals",className:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 touch-target",onClick:()=>a(!1),children:"Referrals"}),e.jsx("button",{onClick:g,className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 touch-target",children:"Logout"})]})]}):e.jsxs("div",{className:"hidden sm:flex items-center space-x-2",children:[e.jsx(R,{to:"/login",className:"px-3 py-2 text-green-600 hover:text-green-700 font-medium text-sm touch-target",children:"Login"}),e.jsx(R,{to:"/register",className:"px-3 py-2 bg-green-600 text-white rounded-full hover:bg-green-700 transition-colors text-sm touch-target",children:"Sign Up"})]}),e.jsx("button",{onClick:()=>s(!t),className:"sm:hidden p-2 text-gray-600 hover:text-green-600 touch-target",children:t?e.jsx(oe,{className:"h-6 w-6"}):e.jsx(xr,{className:"h-6 w-6"})})]})]}),t&&e.jsxs("div",{className:"sm:hidden py-4 border-t border-gray-200 bg-white",children:[e.jsx("form",{onSubmit:x,className:"mb-6 px-4",children:e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"text",placeholder:"Search products...",value:m,onChange:u=>h(u.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 text-base"}),e.jsx(ue,{className:"absolute left-3 top-3.5 h-5 w-5 text-gray-400"})]})}),e.jsxs("nav",{className:"space-y-1 px-4",children:[e.jsx(R,{to:"/products",className:"block px-4 py-3 text-gray-700 hover:bg-gray-50 rounded-lg font-medium touch-target",onClick:()=>s(!1),children:"Products"}),e.jsx(R,{to:"/about",className:"block px-4 py-3 text-gray-700 hover:bg-gray-50 rounded-lg font-medium touch-target",onClick:()=>s(!1),children:"About"}),e.jsx(R,{to:"/premium",className:"block px-4 py-3 text-gray-700 hover:bg-gray-50 rounded-lg font-medium touch-target",onClick:()=>s(!1),children:"Premium"})]}),n?e.jsxs("div",{className:"mt-6 pt-6 border-t border-gray-200 px-4",children:[e.jsxs("div",{className:"mb-4 p-4 bg-gray-50 rounded-lg",children:[e.jsxs("p",{className:"font-medium text-gray-900 flex items-center",children:[l?.full_name,l?.is_premium&&e.jsx(L,{className:"h-4 w-4 text-yellow-500 ml-2"})]}),e.jsx("p",{className:"text-sm text-gray-600",children:l?.email}),e.jsxs("div",{className:"flex items-center mt-2 text-sm text-green-600",children:[e.jsx(K,{className:"h-4 w-4 mr-1"}),"₹",l?.wallet_balance?.toFixed(2)||"0.00"]})]}),e.jsxs("nav",{className:"space-y-1",children:[e.jsx(R,{to:"/dashboard",className:"block px-4 py-3 text-gray-700 hover:bg-gray-50 rounded-lg font-medium touch-target",onClick:()=>s(!1),children:"Dashboard"}),e.jsx(R,{to:"/dashboard/orders",className:"block px-4 py-3 text-gray-700 hover:bg-gray-50 rounded-lg font-medium touch-target",onClick:()=>s(!1),children:"Orders"}),e.jsx(R,{to:"/dashboard/wallet",className:"block px-4 py-3 text-gray-700 hover:bg-gray-50 rounded-lg font-medium touch-target",onClick:()=>s(!1),children:"Wallet"}),l?.is_premium&&e.jsx(R,{to:"/dashboard/referrals",className:"block px-4 py-3 text-gray-700 hover:bg-gray-50 rounded-lg font-medium touch-target",onClick:()=>s(!1),children:"Referrals"}),e.jsx("button",{onClick:g,className:"block w-full text-left px-4 py-3 text-red-600 hover:bg-red-50 rounded-lg font-medium touch-target",children:"Logout"})]})]}):e.jsxs("div",{className:"mt-6 pt-6 border-t border-gray-200 px-4 space-y-3",children:[e.jsx(R,{to:"/login",className:"block w-full px-4 py-3 text-center text-green-600 border border-green-600 rounded-lg font-medium hover:bg-green-50 transition-colors touch-target",onClick:()=>s(!1),children:"Login"}),e.jsx(R,{to:"/register",className:"block w-full px-4 py-3 text-center bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 transition-colors touch-target",onClick:()=>s(!1),children:"Sign Up"})]})]})]})})},Kl=()=>e.jsx("footer",{className:"bg-gray-900 text-white",children:e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{className:"w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center",children:e.jsx("span",{className:"text-white font-bold text-lg",children:"🌿"})}),e.jsx("span",{className:"text-xl font-bold",children:"Start Juicce"})]}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Your trusted source for premium herbal products and natural wellness solutions."}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:e.jsx(Lt,{className:"h-5 w-5"})}),e.jsx("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:e.jsx(qt,{className:"h-5 w-5"})}),e.jsx("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:e.jsx(Bt,{className:"h-5 w-5"})}),e.jsx("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:e.jsx(zt,{className:"h-5 w-5"})})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Quick Links"}),e.jsxs("ul",{className:"space-y-2",children:[e.jsx("li",{children:e.jsx(R,{to:"/products",className:"text-gray-400 hover:text-white transition-colors",children:"All Products"})}),e.jsx("li",{children:e.jsx(R,{to:"/categories",className:"text-gray-400 hover:text-white transition-colors",children:"Categories"})}),e.jsx("li",{children:e.jsx(R,{to:"/premium",className:"text-gray-400 hover:text-white transition-colors",children:"Premium Membership"})}),e.jsx("li",{children:e.jsx(R,{to:"/about",className:"text-gray-400 hover:text-white transition-colors",children:"About Us"})}),e.jsx("li",{children:e.jsx(R,{to:"/contact",className:"text-gray-400 hover:text-white transition-colors",children:"Contact"})})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Customer Service"}),e.jsxs("ul",{className:"space-y-2",children:[e.jsx("li",{children:e.jsx(R,{to:"/help",className:"text-gray-400 hover:text-white transition-colors",children:"Help Center"})}),e.jsx("li",{children:e.jsx(R,{to:"/shipping",className:"text-gray-400 hover:text-white transition-colors",children:"Shipping Info"})}),e.jsx("li",{children:e.jsx(R,{to:"/returns",className:"text-gray-400 hover:text-white transition-colors",children:"Returns & Exchanges"})}),e.jsx("li",{children:e.jsx(R,{to:"/privacy",className:"text-gray-400 hover:text-white transition-colors",children:"Privacy Policy"})}),e.jsx("li",{children:e.jsx(R,{to:"/terms",className:"text-gray-400 hover:text-white transition-colors",children:"Terms of Service"})})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Contact Info"}),e.jsxs("ul",{className:"space-y-2",children:[e.jsxs("li",{className:"flex items-center space-x-2 text-gray-400",children:[e.jsx(Bs,{className:"h-4 w-4"}),e.jsx("span",{children:"+1 (555) 123-4567"})]}),e.jsxs("li",{className:"flex items-center space-x-2 text-gray-400",children:[e.jsx(Ie,{className:"h-4 w-4"}),e.jsx("span",{children:"<EMAIL>"})]}),e.jsxs("li",{className:"flex items-start space-x-2 text-gray-400",children:[e.jsx(rs,{className:"h-4 w-4 mt-1"}),e.jsxs("span",{children:["123 Wellness Ave",e.jsx("br",{}),"Natural City, NC 12345"]})]})]})]})]}),e.jsxs("div",{className:"border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center",children:[e.jsx("p",{className:"text-gray-400 text-sm",children:"© 2024 Start Juicce. All rights reserved."}),e.jsxs("div",{className:"flex space-x-6 mt-4 md:mt-0",children:[e.jsx(R,{to:"/privacy",className:"text-gray-400 hover:text-white text-sm transition-colors",children:"Privacy Policy"}),e.jsx(R,{to:"/terms",className:"text-gray-400 hover:text-white text-sm transition-colors",children:"Terms of Service"}),e.jsx(R,{to:"/sitemap",className:"text-gray-400 hover:text-white text-sm transition-colors",children:"Sitemap"})]})]})]})}),Jl=({children:t})=>{const s=js(),r=s.pathname.startsWith("/dashboard"),a=s.pathname.startsWith("/admin"),l=r||a;return e.jsxs("div",{className:"min-h-screen bg-gray-50 flex flex-col",children:[!l&&e.jsx(Hl,{}),e.jsx("main",{className:"flex-1",children:t}),!l&&e.jsx(Kl,{})]})},Xl=({src:t,alt:s,className:r="",fallbackClassName:a="",size:l="md"})=>{const[n,i]=f.useState(!1),[c,o]=f.useState(!0),d={sm:"w-12 h-12",md:"w-16 h-16",lg:"w-24 h-24"},m={sm:"w-6 h-6",md:"w-8 h-8",lg:"w-12 h-12"},h=()=>{o(!1)},p=()=>{i(!0),o(!1)};return e.jsx("div",{className:`${d[l]} bg-gray-100 rounded-md flex items-center justify-center overflow-hidden relative ${r}`,children:t&&!n?e.jsxs(e.Fragment,{children:[c&&e.jsx("div",{className:"absolute inset-0 flex items-center justify-center bg-gray-100",children:e.jsx("div",{className:"animate-pulse",children:e.jsx(Wt,{className:`${m[l]} text-gray-400`})})}),e.jsx("img",{src:t,alt:s,className:`w-full h-full object-cover ${c?"opacity-0":"opacity-100"} transition-opacity duration-200`,onLoad:h,onError:p})]}):e.jsx("div",{className:`w-full h-full flex items-center justify-center ${a}`,children:e.jsx(Me,{className:`${m[l]} text-gray-400`})})})},Ql=()=>{const{items:t,isOpen:s,toggleCart:r,updateQuantity:a,removeItem:l,getTotalPrice:n}=ts(),{isAuthenticated:i}=J();return s?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-40",onClick:r}),e.jsx("div",{className:"fixed right-0 top-0 h-full w-full max-w-md bg-white shadow-2xl z-50 transform transition-transform duration-300",children:e.jsxs("div",{className:"flex flex-col h-full",children:[e.jsxs("div",{className:"flex items-center justify-between p-4 border-b border-gray-200",children:[e.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"Shopping Cart"}),e.jsx("button",{onClick:r,className:"p-2 text-gray-500 hover:text-gray-700 rounded-full hover:bg-gray-100",children:e.jsx(oe,{className:"h-5 w-5"})})]}),e.jsx("div",{className:"flex-1 overflow-y-auto p-4",children:t.length===0?e.jsxs("div",{className:"flex flex-col items-center justify-center h-full text-center",children:[e.jsx(Me,{className:"h-16 w-16 text-gray-300 mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Your cart is empty"}),e.jsx("p",{className:"text-gray-500 mb-4",children:"Add some products to get started"}),e.jsx(R,{to:"/products",onClick:r,className:"px-6 py-2 bg-green-600 text-white rounded-full hover:bg-green-700 transition-colors",children:"Shop Now"})]}):e.jsx("div",{className:"space-y-4",children:t.map(c=>e.jsxs("div",{className:"flex items-center space-x-3 p-3 border border-gray-200 rounded-lg",children:[e.jsx(Xl,{src:c.product.image_url,alt:c.product.name,size:"md"}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-900 truncate",children:c.product.name}),e.jsxs("p",{className:"text-sm text-gray-500",children:["₹",c.product.price]}),e.jsxs("div",{className:"flex items-center space-x-2 mt-2",children:[e.jsx("button",{onClick:()=>a(c.productId,c.quantity-1),className:"p-1 text-gray-500 hover:text-gray-700 border border-gray-300 rounded-full",children:e.jsx(ws,{className:"h-3 w-3"})}),e.jsx("span",{className:"text-sm font-medium text-gray-900 w-8 text-center",children:c.quantity}),e.jsx("button",{onClick:()=>a(c.productId,c.quantity+1),className:"p-1 text-gray-500 hover:text-gray-700 border border-gray-300 rounded-full",children:e.jsx(re,{className:"h-3 w-3"})})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("p",{className:"text-sm font-medium text-gray-900",children:["₹",(c.product.price*c.quantity).toFixed(2)]}),e.jsx("button",{onClick:()=>l(c.productId),className:"text-xs text-red-500 hover:text-red-700 mt-1",children:"Remove"})]})]},c.productId))})}),t.length>0&&e.jsxs("div",{className:"border-t border-gray-200 p-4 space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-lg font-medium text-gray-900",children:"Total:"}),e.jsxs("span",{className:"text-lg font-bold text-green-600",children:["₹",n().toFixed(2)]})]}),i?e.jsxs("div",{className:"space-y-2",children:[e.jsx(R,{to:"/checkout",onClick:r,className:"w-full bg-green-600 text-white py-3 rounded-full hover:bg-green-700 transition-colors text-center block",children:"Checkout"}),e.jsx(R,{to:"/cart",onClick:r,className:"w-full border border-gray-300 text-gray-700 py-2 rounded-full hover:bg-gray-50 transition-colors text-center block",children:"View Cart"})]}):e.jsxs("div",{className:"space-y-2",children:[e.jsx(R,{to:"/login",onClick:r,className:"w-full bg-green-600 text-white py-3 rounded-full hover:bg-green-700 transition-colors text-center block",children:"Login to Checkout"}),e.jsx("p",{className:"text-xs text-gray-500 text-center",children:"Please login to continue with your purchase"})]})]})]})})]}):null};var Zl=!1;function en(t){if(t.sheet)return t.sheet;for(var s=0;s<document.styleSheets.length;s++)if(document.styleSheets[s].ownerNode===t)return document.styleSheets[s]}function sn(t){var s=document.createElement("style");return s.setAttribute("data-emotion",t.key),t.nonce!==void 0&&s.setAttribute("nonce",t.nonce),s.appendChild(document.createTextNode("")),s.setAttribute("data-s",""),s}var rn=function(){function t(r){var a=this;this._insertTag=function(l){var n;a.tags.length===0?a.insertionPoint?n=a.insertionPoint.nextSibling:a.prepend?n=a.container.firstChild:n=a.before:n=a.tags[a.tags.length-1].nextSibling,a.container.insertBefore(l,n),a.tags.push(l)},this.isSpeedy=r.speedy===void 0?!Zl:r.speedy,this.tags=[],this.ctr=0,this.nonce=r.nonce,this.key=r.key,this.container=r.container,this.prepend=r.prepend,this.insertionPoint=r.insertionPoint,this.before=null}var s=t.prototype;return s.hydrate=function(a){a.forEach(this._insertTag)},s.insert=function(a){this.ctr%(this.isSpeedy?65e3:1)===0&&this._insertTag(sn(this));var l=this.tags[this.tags.length-1];if(this.isSpeedy){var n=en(l);try{n.insertRule(a,n.cssRules.length)}catch{}}else l.appendChild(document.createTextNode(a));this.ctr++},s.flush=function(){this.tags.forEach(function(a){var l;return(l=a.parentNode)==null?void 0:l.removeChild(a)}),this.tags=[],this.ctr=0},t}(),te="-ms-",Ls="-moz-",W="-webkit-",ft="comm",yr="rule",jr="decl",tn="@import",pt="@keyframes",an="@layer",ln=Math.abs,Hs=String.fromCharCode,nn=Object.assign;function on(t,s){return se(t,0)^45?(((s<<2^se(t,0))<<2^se(t,1))<<2^se(t,2))<<2^se(t,3):0}function bt(t){return t.trim()}function cn(t,s){return(t=s.exec(t))?t[0]:t}function Y(t,s,r){return t.replace(s,r)}function ir(t,s){return t.indexOf(s)}function se(t,s){return t.charCodeAt(s)|0}function ps(t,s,r){return t.slice(s,r)}function be(t){return t.length}function wr(t){return t.length}function Es(t,s){return s.push(t),t}function dn(t,s){return t.map(s).join("")}var Ks=1,ss=1,yt=0,ie=0,X=0,as="";function Js(t,s,r,a,l,n,i){return{value:t,root:s,parent:r,type:a,props:l,children:n,line:Ks,column:ss,length:i,return:""}}function ls(t,s){return nn(Js("",null,null,"",null,null,0),t,{length:-t.length},s)}function mn(){return X}function un(){return X=ie>0?se(as,--ie):0,ss--,X===10&&(ss=1,Ks--),X}function de(){return X=ie<yt?se(as,ie++):0,ss++,X===10&&(ss=1,Ks++),X}function je(){return se(as,ie)}function Ts(){return ie}function _s(t,s){return ps(as,t,s)}function bs(t){switch(t){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function jt(t){return Ks=ss=1,yt=be(as=t),ie=0,[]}function wt(t){return as="",t}function Ds(t){return bt(_s(ie-1,or(t===91?t+2:t===40?t+1:t)))}function xn(t){for(;(X=je())&&X<33;)de();return bs(t)>2||bs(X)>3?"":" "}function hn(t,s){for(;--s&&de()&&!(X<48||X>102||X>57&&X<65||X>70&&X<97););return _s(t,Ts()+(s<6&&je()==32&&de()==32))}function or(t){for(;de();)switch(X){case t:return ie;case 34:case 39:t!==34&&t!==39&&or(X);break;case 40:t===41&&or(t);break;case 92:de();break}return ie}function gn(t,s){for(;de()&&t+X!==57;)if(t+X===84&&je()===47)break;return"/*"+_s(s,ie-1)+"*"+Hs(t===47?t:de())}function fn(t){for(;!bs(je());)de();return _s(t,ie)}function pn(t){return wt(Fs("",null,null,null,[""],t=jt(t),0,[0],t))}function Fs(t,s,r,a,l,n,i,c,o){for(var d=0,m=0,h=i,p=0,x=0,g=0,u=1,j=1,b=1,N=0,P="",A=l,E=n,_=a,w=P;j;)switch(g=N,N=de()){case 40:if(g!=108&&se(w,h-1)==58){ir(w+=Y(Ds(N),"&","&\f"),"&\f")!=-1&&(b=-1);break}case 34:case 39:case 91:w+=Ds(N);break;case 9:case 10:case 13:case 32:w+=xn(g);break;case 92:w+=hn(Ts()-1,7);continue;case 47:switch(je()){case 42:case 47:Es(bn(gn(de(),Ts()),s,r),o);break;default:w+="/"}break;case 123*u:c[d++]=be(w)*b;case 125*u:case 59:case 0:switch(N){case 0:case 125:j=0;case 59+m:b==-1&&(w=Y(w,/\f/g,"")),x>0&&be(w)-h&&Es(x>32?Mr(w+";",a,r,h-1):Mr(Y(w," ","")+";",a,r,h-2),o);break;case 59:w+=";";default:if(Es(_=$r(w,s,r,d,m,l,c,P,A=[],E=[],h),n),N===123)if(m===0)Fs(w,s,_,_,A,n,h,c,E);else switch(p===99&&se(w,3)===110?100:p){case 100:case 108:case 109:case 115:Fs(t,_,_,a&&Es($r(t,_,_,0,0,l,c,P,l,A=[],h),E),l,E,h,c,a?A:E);break;default:Fs(w,_,_,_,[""],E,0,c,E)}}d=m=x=0,u=b=1,P=w="",h=i;break;case 58:h=1+be(w),x=g;default:if(u<1){if(N==123)--u;else if(N==125&&u++==0&&un()==125)continue}switch(w+=Hs(N),N*u){case 38:b=m>0?1:(w+="\f",-1);break;case 44:c[d++]=(be(w)-1)*b,b=1;break;case 64:je()===45&&(w+=Ds(de())),p=je(),m=h=be(P=w+=fn(Ts())),N++;break;case 45:g===45&&be(w)==2&&(u=0)}}return n}function $r(t,s,r,a,l,n,i,c,o,d,m){for(var h=l-1,p=l===0?n:[""],x=wr(p),g=0,u=0,j=0;g<a;++g)for(var b=0,N=ps(t,h+1,h=ln(u=i[g])),P=t;b<x;++b)(P=bt(u>0?p[b]+" "+N:Y(N,/&\f/g,p[b])))&&(o[j++]=P);return Js(t,s,r,l===0?yr:c,o,d,m)}function bn(t,s,r){return Js(t,s,r,ft,Hs(mn()),ps(t,2,-2),0)}function Mr(t,s,r,a){return Js(t,s,r,jr,ps(t,0,a),ps(t,a+1,-1),a)}function Qe(t,s){for(var r="",a=wr(t),l=0;l<a;l++)r+=s(t[l],l,t,s)||"";return r}function yn(t,s,r,a){switch(t.type){case an:if(t.children.length)break;case tn:case jr:return t.return=t.return||t.value;case ft:return"";case pt:return t.return=t.value+"{"+Qe(t.children,a)+"}";case yr:t.value=t.props.join(",")}return be(r=Qe(t.children,a))?t.return=t.value+"{"+r+"}":""}function jn(t){var s=wr(t);return function(r,a,l,n){for(var i="",c=0;c<s;c++)i+=t[c](r,a,l,n)||"";return i}}function wn(t){return function(s){s.root||(s=s.return)&&t(s)}}var Nn=function(s,r,a){for(var l=0,n=0;l=n,n=je(),l===38&&n===12&&(r[a]=1),!bs(n);)de();return _s(s,ie)},vn=function(s,r){var a=-1,l=44;do switch(bs(l)){case 0:l===38&&je()===12&&(r[a]=1),s[a]+=Nn(ie-1,r,a);break;case 2:s[a]+=Ds(l);break;case 4:if(l===44){s[++a]=je()===58?"&\f":"",r[a]=s[a].length;break}default:s[a]+=Hs(l)}while(l=de());return s},_n=function(s,r){return wt(vn(jt(s),r))},Lr=new WeakMap,kn=function(s){if(!(s.type!=="rule"||!s.parent||s.length<1)){for(var r=s.value,a=s.parent,l=s.column===a.column&&s.line===a.line;a.type!=="rule";)if(a=a.parent,!a)return;if(!(s.props.length===1&&r.charCodeAt(0)!==58&&!Lr.get(a))&&!l){Lr.set(s,!0);for(var n=[],i=_n(r,n),c=a.props,o=0,d=0;o<i.length;o++)for(var m=0;m<c.length;m++,d++)s.props[d]=n[o]?i[o].replace(/&\f/g,c[m]):c[m]+" "+i[o]}}},Sn=function(s){if(s.type==="decl"){var r=s.value;r.charCodeAt(0)===108&&r.charCodeAt(2)===98&&(s.return="",s.value="")}};function Nt(t,s){switch(on(t,s)){case 5103:return W+"print-"+t+t;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return W+t+t;case 5349:case 4246:case 4810:case 6968:case 2756:return W+t+Ls+t+te+t+t;case 6828:case 4268:return W+t+te+t+t;case 6165:return W+t+te+"flex-"+t+t;case 5187:return W+t+Y(t,/(\w+).+(:[^]+)/,W+"box-$1$2"+te+"flex-$1$2")+t;case 5443:return W+t+te+"flex-item-"+Y(t,/flex-|-self/,"")+t;case 4675:return W+t+te+"flex-line-pack"+Y(t,/align-content|flex-|-self/,"")+t;case 5548:return W+t+te+Y(t,"shrink","negative")+t;case 5292:return W+t+te+Y(t,"basis","preferred-size")+t;case 6060:return W+"box-"+Y(t,"-grow","")+W+t+te+Y(t,"grow","positive")+t;case 4554:return W+Y(t,/([^-])(transform)/g,"$1"+W+"$2")+t;case 6187:return Y(Y(Y(t,/(zoom-|grab)/,W+"$1"),/(image-set)/,W+"$1"),t,"")+t;case 5495:case 3959:return Y(t,/(image-set\([^]*)/,W+"$1$`$1");case 4968:return Y(Y(t,/(.+:)(flex-)?(.*)/,W+"box-pack:$3"+te+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+W+t+t;case 4095:case 3583:case 4068:case 2532:return Y(t,/(.+)-inline(.+)/,W+"$1$2")+t;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(be(t)-1-s>6)switch(se(t,s+1)){case 109:if(se(t,s+4)!==45)break;case 102:return Y(t,/(.+:)(.+)-([^]+)/,"$1"+W+"$2-$3$1"+Ls+(se(t,s+3)==108?"$3":"$2-$3"))+t;case 115:return~ir(t,"stretch")?Nt(Y(t,"stretch","fill-available"),s)+t:t}break;case 4949:if(se(t,s+1)!==115)break;case 6444:switch(se(t,be(t)-3-(~ir(t,"!important")&&10))){case 107:return Y(t,":",":"+W)+t;case 101:return Y(t,/(.+:)([^;!]+)(;|!.+)?/,"$1"+W+(se(t,14)===45?"inline-":"")+"box$3$1"+W+"$2$3$1"+te+"$2box$3")+t}break;case 5936:switch(se(t,s+11)){case 114:return W+t+te+Y(t,/[svh]\w+-[tblr]{2}/,"tb")+t;case 108:return W+t+te+Y(t,/[svh]\w+-[tblr]{2}/,"tb-rl")+t;case 45:return W+t+te+Y(t,/[svh]\w+-[tblr]{2}/,"lr")+t}return W+t+te+t+t}return t}var Cn=function(s,r,a,l){if(s.length>-1&&!s.return)switch(s.type){case jr:s.return=Nt(s.value,s.length);break;case pt:return Qe([ls(s,{value:Y(s.value,"@","@"+W)})],l);case yr:if(s.length)return dn(s.props,function(n){switch(cn(n,/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":return Qe([ls(s,{props:[Y(n,/:(read-\w+)/,":"+Ls+"$1")]})],l);case"::placeholder":return Qe([ls(s,{props:[Y(n,/:(plac\w+)/,":"+W+"input-$1")]}),ls(s,{props:[Y(n,/:(plac\w+)/,":"+Ls+"$1")]}),ls(s,{props:[Y(n,/:(plac\w+)/,te+"input-$1")]})],l)}return""})}},En=[Cn],Pn=function(s){var r=s.key;if(r==="css"){var a=document.querySelectorAll("style[data-emotion]:not([data-s])");Array.prototype.forEach.call(a,function(u){var j=u.getAttribute("data-emotion");j.indexOf(" ")!==-1&&(document.head.appendChild(u),u.setAttribute("data-s",""))})}var l=s.stylisPlugins||En,n={},i,c=[];i=s.container||document.head,Array.prototype.forEach.call(document.querySelectorAll('style[data-emotion^="'+r+' "]'),function(u){for(var j=u.getAttribute("data-emotion").split(" "),b=1;b<j.length;b++)n[j[b]]=!0;c.push(u)});var o,d=[kn,Sn];{var m,h=[yn,wn(function(u){m.insert(u)})],p=jn(d.concat(l,h)),x=function(j){return Qe(pn(j),p)};o=function(j,b,N,P){m=N,x(j?j+"{"+b.styles+"}":b.styles),P&&(g.inserted[b.name]=!0)}}var g={key:r,sheet:new rn({key:r,container:i,nonce:s.nonce,speedy:s.speedy,prepend:s.prepend,insertionPoint:s.insertionPoint}),nonce:s.nonce,inserted:n,registered:{},insert:o};return g.sheet.hydrate(c),g},Rn=!0;function An(t,s,r){var a="";return r.split(" ").forEach(function(l){t[l]!==void 0?s.push(t[l]+";"):l&&(a+=l+" ")}),a}var vt=function(s,r,a){var l=s.key+"-"+r.name;(a===!1||Rn===!1)&&s.registered[l]===void 0&&(s.registered[l]=r.styles)},Tn=function(s,r,a){vt(s,r,a);var l=s.key+"-"+r.name;if(s.inserted[r.name]===void 0){var n=r;do s.insert(r===n?"."+l:"",n,s.sheet,!0),n=n.next;while(n!==void 0)}};function Dn(t){for(var s=0,r,a=0,l=t.length;l>=4;++a,l-=4)r=t.charCodeAt(a)&255|(t.charCodeAt(++a)&255)<<8|(t.charCodeAt(++a)&255)<<16|(t.charCodeAt(++a)&255)<<24,r=(r&65535)*1540483477+((r>>>16)*59797<<16),r^=r>>>24,s=(r&65535)*1540483477+((r>>>16)*59797<<16)^(s&65535)*1540483477+((s>>>16)*59797<<16);switch(l){case 3:s^=(t.charCodeAt(a+2)&255)<<16;case 2:s^=(t.charCodeAt(a+1)&255)<<8;case 1:s^=t.charCodeAt(a)&255,s=(s&65535)*1540483477+((s>>>16)*59797<<16)}return s^=s>>>13,s=(s&65535)*1540483477+((s>>>16)*59797<<16),((s^s>>>15)>>>0).toString(36)}var Fn={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,scale:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1};function In(t){var s=Object.create(null);return function(r){return s[r]===void 0&&(s[r]=t(r)),s[r]}}var On=!1,Un=/[A-Z]|^ms/g,$n=/_EMO_([^_]+?)_([^]*?)_EMO_/g,_t=function(s){return s.charCodeAt(1)===45},qr=function(s){return s!=null&&typeof s!="boolean"},Zs=In(function(t){return _t(t)?t:t.replace(Un,"-$&").toLowerCase()}),Br=function(s,r){switch(s){case"animation":case"animationName":if(typeof r=="string")return r.replace($n,function(a,l,n){return ye={name:l,styles:n,next:ye},l})}return Fn[s]!==1&&!_t(s)&&typeof r=="number"&&r!==0?r+"px":r},Mn="Component selectors can only be used in conjunction with @emotion/babel-plugin, the swc Emotion plugin, or another Emotion-aware compiler transform.";function ys(t,s,r){if(r==null)return"";var a=r;if(a.__emotion_styles!==void 0)return a;switch(typeof r){case"boolean":return"";case"object":{var l=r;if(l.anim===1)return ye={name:l.name,styles:l.styles,next:ye},l.name;var n=r;if(n.styles!==void 0){var i=n.next;if(i!==void 0)for(;i!==void 0;)ye={name:i.name,styles:i.styles,next:ye},i=i.next;var c=n.styles+";";return c}return Ln(t,s,r)}case"function":{if(t!==void 0){var o=ye,d=r(t);return ye=o,ys(t,s,d)}break}}var m=r;if(s==null)return m;var h=s[m];return h!==void 0?h:m}function Ln(t,s,r){var a="";if(Array.isArray(r))for(var l=0;l<r.length;l++)a+=ys(t,s,r[l])+";";else for(var n in r){var i=r[n];if(typeof i!="object"){var c=i;s!=null&&s[c]!==void 0?a+=n+"{"+s[c]+"}":qr(c)&&(a+=Zs(n)+":"+Br(n,c)+";")}else{if(n==="NO_COMPONENT_SELECTOR"&&On)throw new Error(Mn);if(Array.isArray(i)&&typeof i[0]=="string"&&(s==null||s[i[0]]===void 0))for(var o=0;o<i.length;o++)qr(i[o])&&(a+=Zs(n)+":"+Br(n,i[o])+";");else{var d=ys(t,s,i);switch(n){case"animation":case"animationName":{a+=Zs(n)+":"+d+";";break}default:a+=n+"{"+d+"}"}}}}return a}var zr=/label:\s*([^\s;{]+)\s*(;|$)/g,ye;function qn(t,s,r){if(t.length===1&&typeof t[0]=="object"&&t[0]!==null&&t[0].styles!==void 0)return t[0];var a=!0,l="";ye=void 0;var n=t[0];if(n==null||n.raw===void 0)a=!1,l+=ys(r,s,n);else{var i=n;l+=i[0]}for(var c=1;c<t.length;c++)if(l+=ys(r,s,t[c]),a){var o=n;l+=o[c]}zr.lastIndex=0;for(var d="",m;(m=zr.exec(l))!==null;)d+="-"+m[1];var h=Dn(l)+d;return{name:h,styles:l,next:ye}}var Bn=function(s){return s()},zn=Sr.useInsertionEffect?Sr.useInsertionEffect:!1,Wn=zn||Bn,kt=f.createContext(typeof HTMLElement<"u"?Pn({key:"css"}):null);kt.Provider;var Yn=function(s){return f.forwardRef(function(r,a){var l=f.useContext(kt);return s(r,l,a)})},Vn=f.createContext({});function Gn(t){var s=Object.create(null);return function(r){return s[r]===void 0&&(s[r]=t(r)),s[r]}}var Hn=/^((children|dangerouslySetInnerHTML|key|ref|autoFocus|defaultValue|defaultChecked|innerHTML|suppressContentEditableWarning|suppressHydrationWarning|valueLink|abbr|accept|acceptCharset|accessKey|action|allow|allowUserMedia|allowPaymentRequest|allowFullScreen|allowTransparency|alt|async|autoComplete|autoPlay|capture|cellPadding|cellSpacing|challenge|charSet|checked|cite|classID|className|cols|colSpan|content|contentEditable|contextMenu|controls|controlsList|coords|crossOrigin|data|dateTime|decoding|default|defer|dir|disabled|disablePictureInPicture|disableRemotePlayback|download|draggable|encType|enterKeyHint|fetchpriority|fetchPriority|form|formAction|formEncType|formMethod|formNoValidate|formTarget|frameBorder|headers|height|hidden|high|href|hrefLang|htmlFor|httpEquiv|id|inputMode|integrity|is|keyParams|keyType|kind|label|lang|list|loading|loop|low|marginHeight|marginWidth|max|maxLength|media|mediaGroup|method|min|minLength|multiple|muted|name|nonce|noValidate|open|optimum|pattern|placeholder|playsInline|poster|preload|profile|radioGroup|readOnly|referrerPolicy|rel|required|reversed|role|rows|rowSpan|sandbox|scope|scoped|scrolling|seamless|selected|shape|size|sizes|slot|span|spellCheck|src|srcDoc|srcLang|srcSet|start|step|style|summary|tabIndex|target|title|translate|type|useMap|value|width|wmode|wrap|about|datatype|inlist|prefix|property|resource|typeof|vocab|autoCapitalize|autoCorrect|autoSave|color|incremental|fallback|inert|itemProp|itemScope|itemType|itemID|itemRef|on|option|results|security|unselectable|accentHeight|accumulate|additive|alignmentBaseline|allowReorder|alphabetic|amplitude|arabicForm|ascent|attributeName|attributeType|autoReverse|azimuth|baseFrequency|baselineShift|baseProfile|bbox|begin|bias|by|calcMode|capHeight|clip|clipPathUnits|clipPath|clipRule|colorInterpolation|colorInterpolationFilters|colorProfile|colorRendering|contentScriptType|contentStyleType|cursor|cx|cy|d|decelerate|descent|diffuseConstant|direction|display|divisor|dominantBaseline|dur|dx|dy|edgeMode|elevation|enableBackground|end|exponent|externalResourcesRequired|fill|fillOpacity|fillRule|filter|filterRes|filterUnits|floodColor|floodOpacity|focusable|fontFamily|fontSize|fontSizeAdjust|fontStretch|fontStyle|fontVariant|fontWeight|format|from|fr|fx|fy|g1|g2|glyphName|glyphOrientationHorizontal|glyphOrientationVertical|glyphRef|gradientTransform|gradientUnits|hanging|horizAdvX|horizOriginX|ideographic|imageRendering|in|in2|intercept|k|k1|k2|k3|k4|kernelMatrix|kernelUnitLength|kerning|keyPoints|keySplines|keyTimes|lengthAdjust|letterSpacing|lightingColor|limitingConeAngle|local|markerEnd|markerMid|markerStart|markerHeight|markerUnits|markerWidth|mask|maskContentUnits|maskUnits|mathematical|mode|numOctaves|offset|opacity|operator|order|orient|orientation|origin|overflow|overlinePosition|overlineThickness|panose1|paintOrder|pathLength|patternContentUnits|patternTransform|patternUnits|pointerEvents|points|pointsAtX|pointsAtY|pointsAtZ|preserveAlpha|preserveAspectRatio|primitiveUnits|r|radius|refX|refY|renderingIntent|repeatCount|repeatDur|requiredExtensions|requiredFeatures|restart|result|rotate|rx|ry|scale|seed|shapeRendering|slope|spacing|specularConstant|specularExponent|speed|spreadMethod|startOffset|stdDeviation|stemh|stemv|stitchTiles|stopColor|stopOpacity|strikethroughPosition|strikethroughThickness|string|stroke|strokeDasharray|strokeDashoffset|strokeLinecap|strokeLinejoin|strokeMiterlimit|strokeOpacity|strokeWidth|surfaceScale|systemLanguage|tableValues|targetX|targetY|textAnchor|textDecoration|textRendering|textLength|to|transform|u1|u2|underlinePosition|underlineThickness|unicode|unicodeBidi|unicodeRange|unitsPerEm|vAlphabetic|vHanging|vIdeographic|vMathematical|values|vectorEffect|version|vertAdvY|vertOriginX|vertOriginY|viewBox|viewTarget|visibility|widths|wordSpacing|writingMode|x|xHeight|x1|x2|xChannelSelector|xlinkActuate|xlinkArcrole|xlinkHref|xlinkRole|xlinkShow|xlinkTitle|xlinkType|xmlBase|xmlns|xmlnsXlink|xmlLang|xmlSpace|y|y1|y2|yChannelSelector|z|zoomAndPan|for|class|autofocus)|(([Dd][Aa][Tt][Aa]|[Aa][Rr][Ii][Aa]|x)-.*))$/,Kn=Gn(function(t){return Hn.test(t)||t.charCodeAt(0)===111&&t.charCodeAt(1)===110&&t.charCodeAt(2)<91}),Jn=!1,Xn=Kn,Qn=function(s){return s!=="theme"},Wr=function(s){return typeof s=="string"&&s.charCodeAt(0)>96?Xn:Qn},Yr=function(s,r,a){var l;if(r){var n=r.shouldForwardProp;l=s.__emotion_forwardProp&&n?function(i){return s.__emotion_forwardProp(i)&&n(i)}:n}return typeof l!="function"&&a&&(l=s.__emotion_forwardProp),l},Zn=function(s){var r=s.cache,a=s.serialized,l=s.isStringTag;return vt(r,a,l),Wn(function(){return Tn(r,a,l)}),null},ei=function t(s,r){var a=s.__emotion_real===s,l=a&&s.__emotion_base||s,n,i;r!==void 0&&(n=r.label,i=r.target);var c=Yr(s,r,a),o=c||Wr(l),d=!o("as");return function(){var m=arguments,h=a&&s.__emotion_styles!==void 0?s.__emotion_styles.slice(0):[];if(n!==void 0&&h.push("label:"+n+";"),m[0]==null||m[0].raw===void 0)h.push.apply(h,m);else{var p=m[0];h.push(p[0]);for(var x=m.length,g=1;g<x;g++)h.push(m[g],p[g])}var u=Yn(function(j,b,N){var P=d&&j.as||l,A="",E=[],_=j;if(j.theme==null){_={};for(var w in j)_[w]=j[w];_.theme=f.useContext(Vn)}typeof j.className=="string"?A=An(b.registered,E,j.className):j.className!=null&&(A=j.className+" ");var S=qn(h.concat(E),b.registered,_);A+=b.key+"-"+S.name,i!==void 0&&(A+=" "+i);var C=d&&c===void 0?Wr(P):o,T={};for(var F in j)d&&F==="as"||C(F)&&(T[F]=j[F]);return T.className=A,N&&(T.ref=N),f.createElement(f.Fragment,null,f.createElement(Zn,{cache:b,serialized:S,isStringTag:typeof P=="string"}),f.createElement(P,T))});return u.displayName=n!==void 0?n:"Styled("+(typeof l=="string"?l:l.displayName||l.name||"Component")+")",u.defaultProps=s.defaultProps,u.__emotion_real=u,u.__emotion_base=l,u.__emotion_styles=h,u.__emotion_forwardProp=c,Object.defineProperty(u,"toString",{value:function(){return i===void 0&&Jn?"NO_COMPONENT_SELECTOR":"."+i}}),u.withComponent=function(j,b){var N=t(j,ha({},r,b,{shouldForwardProp:Yr(u,b,!0)}));return N.apply(void 0,h)},u}},si=["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","marquee","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","title","tr","track","u","ul","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","tspan"],We=ei.bind(null);si.forEach(function(t){We[t]=We(t)});const ri=We.div`
  width: 8px;
  height: 8px;
  background-color: #4CAF50;
  border-radius: 50%;
  position: fixed;
  pointer-events: none;
  z-index: 9999;
  will-change: transform;
  mix-blend-mode: difference;
`,ti=We.div`
  width: 40px;
  height: 40px;
  border: 2px solid #4CAF50;
  border-radius: 50%;
  position: fixed;
  pointer-events: none;
  z-index: 9998;
  will-change: transform, width, height;
  mix-blend-mode: difference;
`,ai=We.div`
  width: 4px;
  height: 4px;
  background-color: #4CAF50;
  border-radius: 50%;
  position: fixed;
  pointer-events: none;
  z-index: 9997;
  opacity: 0.3;
  will-change: transform, opacity;
  mix-blend-mode: difference;
`,li=We.div`
  position: fixed;
  pointer-events: none;
  z-index: 9996;
  will-change: transform, opacity;
  mix-blend-mode: difference;
  transform-origin: center;
`,ni=We.div`
  position: fixed;
  pointer-events: none;
  z-index: 9995;
  color: #4CAF50;
  font-size: 12px;
  font-weight: 600;
  opacity: 0;
  transform: translateY(10px) scale(0.95);
  will-change: transform, opacity;
  mix-blend-mode: difference;
  text-shadow: 0 0 8px rgba(76, 175, 80, 0.5);
  white-space: nowrap;
  transition: all 0.15s ease-out;
  padding: 2px 6px;
  border-radius: 4px;
  letter-spacing: 0.5px;
`,ii=()=>{const[t,s]=f.useState(!1),[r,a]=f.useState(!1),[l,n]=f.useState(""),[i,c]=f.useState(!1),[o,d]=f.useState([]),m=f.useRef(),h=f.useRef(null),p=f.useRef(!1),x=f.useRef({x:0,y:0}),g=f.useRef({x:0,y:0,targetX:0,targetY:0,trail:[],lastUpdate:0}),u=f.useRef(),j=f.useRef(0),b=(E,_)=>({x:E,y:_,vx:(Math.random()-.5)*3,vy:(Math.random()-.5)*3,life:1,color:`hsl(${Math.random()*40+120}, 70%, 50%)`,scale:Math.random()*.5+.5,rotation:Math.random()*360}),N=E=>{d(_=>_.map(w=>({...w,x:w.x+w.vx*(E/16),y:w.y+w.vy*(E/16),life:w.life-.015*(E/16),vy:w.vy+.08*(E/16),rotation:w.rotation+2*(E/16)})).filter(w=>w.life>0))};f.useEffect(()=>{const E=C=>{const T=C-j.current;j.current=C;const F=.15;g.current.x+=(g.current.targetX-g.current.x)*F,g.current.y+=(g.current.targetY-g.current.y)*F,T>16&&(g.current.trail=[{x:g.current.x,y:g.current.y},...g.current.trail.slice(0,7)]),N(T),A({x:g.current.x,y:g.current.y}),u.current=requestAnimationFrame(E)},_=C=>{const T=C.target,F=T.closest('button, a, [role="button"], input, select, textarea');if(x.current={x:C.clientX,y:C.clientY},F){const I=T.closest('button, a, [role="button"], input, select, textarea');if(h.current!==I){m.current&&(clearTimeout(m.current),m.current=void 0),h.current=I,p.current=!0;const v=I.getAttribute("data-cursor-text")||(I instanceof HTMLAnchorElement?"Link":I instanceof HTMLButtonElement?"Click":"Interactive");n(v),c(!0)}}else p.current&&(p.current=!1,h.current=null,m.current&&clearTimeout(m.current),m.current=window.setTimeout(()=>{p.current||(c(!1),n(""))},50));if(F){const I=T.getBoundingClientRect(),v=I.left+I.width/2,$=I.top+I.height/2,O=C.clientX-v,z=C.clientY-$,G=Math.sqrt(O*O+z*z),ee=Math.max(I.width,I.height)*.5;if(G<ee){const Q=1-G/ee;g.current.targetX=v+O*Q*.3,g.current.targetY=$+z*Q*.3}else g.current.targetX=C.clientX,g.current.targetY=C.clientY}else g.current.targetX=C.clientX,g.current.targetY=C.clientY;s(window.getComputedStyle(T).cursor==="pointer")},w=()=>{a(!0),d(C=>[...C,...Array(8).fill(null).map(()=>b(g.current.x,g.current.y))])},S=()=>a(!1);return u.current=requestAnimationFrame(E),window.addEventListener("mousemove",_),window.addEventListener("mousedown",w),window.addEventListener("mouseup",S),()=>{window.removeEventListener("mousemove",_),window.removeEventListener("mousedown",w),window.removeEventListener("mouseup",S),u.current&&cancelAnimationFrame(u.current),m.current&&clearTimeout(m.current),c(!1),n(""),p.current=!1,h.current=null}},[]);const[P,A]=f.useState({x:0,y:0});return e.jsxs(e.Fragment,{children:[o.map((E,_)=>e.jsx(li,{style:{transform:`translate(${E.x-2}px, ${E.y-2}px) rotate(${E.rotation}deg) scale(${E.scale})`,opacity:E.life,backgroundColor:E.color,width:"4px",height:"4px",borderRadius:"50%",boxShadow:`0 0 ${E.life*4}px ${E.color}`}},_)),g.current.trail.map((E,_)=>e.jsx(ai,{style:{transform:`translate(${E.x-2}px, ${E.y-2}px)`,opacity:.3*(1-_/g.current.trail.length)}},_)),i&&l&&e.jsx(ni,{style:{transform:`translate(${x.current.x+20}px, ${x.current.y-20}px) translateY(${i?0:10}px) scale(${i?1:.95})`,opacity:i?1:0},children:l}),e.jsx(ri,{style:{transform:`translate(${P.x-4}px, ${P.y-4}px) scale(${r?.8:t?1.5:1})`,opacity:t?.5:1}}),e.jsx(ti,{style:{transform:`translate(${P.x-20}px, ${P.y-20}px) scale(${r?.8:t?1.5:1})`,opacity:t?.5:.3,width:r?"30px":"40px",height:r?"30px":"40px"}})]})},oi=({children:t})=>{const{user:s,isAuthenticated:r}=J(),a=js(),[l,n]=f.useState({isAdmin:!1,isLoading:!0});f.useEffect(()=>{i()},[s,r]);const i=async()=>{try{if(n({isAdmin:!1,isLoading:!0}),!r||!s){n({isAdmin:!1,isLoading:!1,error:"Authentication required"});return}const{data:m,error:h}=await y.from("users").select("id, email, is_admin, admin_role, is_premium, created_at").eq("id",s.id).single();if(h){console.error("Admin validation error:",h),n({isAdmin:!1,isLoading:!1,error:"Database validation failed"});return}if(!m){n({isAdmin:!1,isLoading:!1,error:"User not found in database"});return}if(!(m.is_admin===!0||m.admin_role==="super_admin"||m.admin_role==="admin"||c(m.email))){n({isAdmin:!1,isLoading:!1,error:"Insufficient privileges - Admin access required"});return}const x=await o(m);if(!x.passed){n({isAdmin:!1,isLoading:!1,error:x.reason});return}await d(m),n({isAdmin:!0,isLoading:!1})}catch(m){console.error("Admin guard error:",m),n({isAdmin:!1,isLoading:!1,error:"Security validation failed"})}},c=m=>["<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>"].includes(m.toLowerCase()),o=async m=>{const h=Date.now()-new Date(m.created_at).getTime(),p=24*60*60*1e3;if(h<p&&!c(m.email))return{passed:!1,reason:"Account too new for admin access"};if(!m.is_premium&&!c(m.email))return{passed:!1,reason:"Premium account required for admin access"};const{data:x}=await y.from("login_logs").select("*").eq("user_id",m.id).gte("created_at",new Date(Date.now()-60*60*1e3).toISOString()).order("created_at",{ascending:!1});return x&&x.length>10?{passed:!1,reason:"Too many recent login attempts - security lock"}:{passed:!0}},d=async m=>{try{console.log("Admin access:",{admin_user_id:m.id,admin_email:m.email,access_path:a.pathname,timestamp:new Date().toISOString()})}catch(h){console.error("Failed to log admin access:",h)}};return l.isLoading?e.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:e.jsx("div",{className:"bg-white p-8 rounded-lg shadow-sm border max-w-md w-full mx-4",children:e.jsxs("div",{className:"text-center",children:[e.jsx(ae,{className:"h-12 w-12 text-blue-600 mx-auto mb-4 animate-pulse"}),e.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Validating Admin Access"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"Performing security checks..."}),e.jsx("div",{className:"flex items-center justify-center",children:e.jsx("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"})})]})})}):r?l.isAdmin?e.jsxs("div",{children:[e.jsx("div",{className:"bg-red-50 border-b border-red-200 px-4 py-2",children:e.jsxs("div",{className:"flex items-center justify-between max-w-7xl mx-auto",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(Os,{className:"h-4 w-4 text-red-600"}),e.jsx("span",{className:"text-sm font-medium text-red-800",children:"Admin Panel - Secure Access"})]}),e.jsxs("div",{className:"text-xs text-red-600",children:["User: ",s?.email," | Session: Active"]})]})}),t]}):e.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:e.jsx("div",{className:"bg-white p-8 rounded-lg shadow-sm border max-w-md w-full mx-4",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4",children:e.jsx(Is,{className:"h-6 w-6 text-red-600"})}),e.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Access Denied"}),e.jsx("p",{className:"text-gray-600 mb-4",children:l.error||"You do not have permission to access the admin panel."}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("button",{onClick:()=>window.location.href="/dashboard",className:"w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors",children:"Go to Dashboard"}),e.jsx("button",{onClick:()=>window.location.href="/",className:"w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors",children:"Go to Home"})]})]})})}):e.jsx(Ke,{to:"/login",state:{from:a},replace:!0})},Vr=["https://images.unsplash.com/photo-1544787219-7f47ccb76574?w=400&h=400&fit=crop&auto=format","https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=400&fit=crop&auto=format","https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=400&fit=crop&auto=format","https://images.unsplash.com/photo-1505944270255-72b8c68c6a70?w=400&h=400&fit=crop&auto=format","https://images.unsplash.com/photo-1582750433449-648ed127bb54?w=400&h=400&fit=crop&auto=format","https://images.unsplash.com/photo-1559181567-c3190ca9959b?w=400&h=400&fit=crop&auto=format","https://images.unsplash.com/photo-1512290923902-8a9f81dc236c?w=400&h=400&fit=crop&auto=format","https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=400&fit=crop&auto=format","https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=400&fit=crop&auto=format","https://images.unsplash.com/photo-1515377905703-c4788e51af15?w=400&h=400&fit=crop&auto=format"],ci="https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800&h=600&fit=crop&auto=format",cr="https://images.unsplash.com/photo-1544787219-7f47ccb76574?w=400&h=400&fit=crop&auto=format",di=()=>Vr[Math.floor(Math.random()*Vr.length)],Nr=(t,s)=>t&&t.trim()!==""?t:s||cr,vr=(t,s)=>{const r=t.target;r.src!==(s||cr)&&(r.src=s||cr)},_r=(t,s=400,r=400)=>{if(t.includes("unsplash.com")){const a=t.includes("?")?"&":"?";return`${t}${a}w=${s}&h=${r}&fit=crop&auto=format`}return t},St=({product:t,fallbackImageSrc:s})=>{const{addItem:r}=ts(),{user:a,isAuthenticated:l}=J(),n=Nr(t.image_url,s),i=_r(n,400,300),c=d=>{if(d.preventDefault(),!l){k.error("Please login to add items to cart");return}if(t.is_premium_only&&!a?.is_premium){k.error("This product is only available for Premium members");return}if(!t.stock_quantity){k.error("Product is out of stock");return}r(t),k.success("Added to cart!")},o=l&&(!t.is_premium_only||a?.is_premium);return e.jsxs("div",{className:"bg-white rounded-xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden group",children:[e.jsxs("div",{className:"relative",children:[e.jsx("img",{src:i,alt:t.name,className:"w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300",onError:d=>vr(d,s)}),t.is_premium_only&&e.jsxs("div",{className:"absolute top-2 left-2 bg-yellow-500 text-white px-2 py-1 rounded-full flex items-center text-xs font-medium",children:[e.jsx(L,{className:"h-3 w-3 mr-1"}),"Premium"]}),!t.stock_quantity&&e.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center",children:e.jsx("span",{className:"text-white font-semibold",children:"Out of Stock"})}),t.original_price&&t.original_price>t.price&&e.jsxs("div",{className:"absolute top-2 right-2 bg-red-500 text-white px-2 py-1 rounded-full text-xs font-medium",children:[Math.round((1-t.price/t.original_price)*100),"% OFF"]})]}),e.jsxs("div",{className:"p-4",children:[e.jsx("h3",{className:"font-semibold text-gray-900 mb-2 line-clamp-2 group-hover:text-green-600 transition-colors",children:t.name}),e.jsx("p",{className:"text-gray-600 text-sm mb-3 line-clamp-2",children:t.description}),e.jsxs("div",{className:"flex items-center mb-3",children:[e.jsx("div",{className:"flex items-center",children:[...Array(5)].map((d,m)=>e.jsx(Ce,{className:`h-4 w-4 ${m<Math.floor(t.rating)?"text-yellow-400 fill-current":"text-gray-300"}`},m))}),e.jsxs("span",{className:"text-sm text-gray-600 ml-2",children:["(",t.reviews_count,")"]})]}),e.jsx("div",{className:"flex items-center justify-between mb-4",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("span",{className:"text-xl font-bold text-green-600",children:["₹",t.price]}),t.original_price&&t.original_price>t.price&&e.jsxs("span",{className:"text-sm text-gray-500 line-through",children:["₹",t.original_price]})]})}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsxs(R,{to:`/products/${t.id}`,className:"flex-1 bg-gray-100 text-gray-700 py-2 rounded-lg hover:bg-gray-200 transition-colors text-center flex items-center justify-center",children:[e.jsx(ne,{className:"h-4 w-4 mr-1"}),"View"]}),e.jsxs("button",{onClick:c,disabled:!o||!t.stock_quantity,className:`flex-1 py-2 rounded-lg transition-colors text-center flex items-center justify-center ${o&&t.stock_quantity?"bg-green-600 text-white hover:bg-green-700":"bg-gray-300 text-gray-500 cursor-not-allowed"}`,children:[e.jsx(ge,{className:"h-4 w-4 mr-1"}),"Add to Cart"]})]}),t.is_premium_only&&!a?.is_premium&&e.jsx("p",{className:"text-xs text-amber-600 mt-2 text-center",children:"Premium membership required"})]})]})},ns=t=>{const[s,r]=f.useState(!1),a=f.useRef(null);return f.useEffect(()=>{const l=new IntersectionObserver(([n])=>{r(n.isIntersecting)},t);return a.current&&l.observe(a.current),()=>{a.current&&l.unobserve(a.current),l.disconnect()}},[t]),[a,s]},mi=()=>{const{featuredProducts:t,fetchFeaturedProducts:s,isLoading:r}=Ge(),{user:a}=J(),[l,n]=ns({threshold:.1}),[i,c]=ns({threshold:.1}),[o,d]=ns({threshold:.1}),[m,h]=ns({threshold:.1}),[p,x]=ns({threshold:.1});return f.useEffect(()=>{s()},[]),e.jsxs("div",{className:"min-h-screen",children:[e.jsx("section",{className:"relative bg-gradient-to-r from-emerald-800 to-green-600 py-16 sm:py-20 lg:py-28 overflow-hidden",children:e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10",children:e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-16 items-center",children:[e.jsxs("div",{className:"space-y-6 sm:space-y-8 text-center lg:text-left",children:[e.jsxs("h1",{className:"text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-extrabold text-white leading-tight drop-shadow-lg animate-fade-in-down",children:["Natural Wellness",e.jsx("span",{className:"block text-lime-400",children:"Your Path to Health"})]}),e.jsx("p",{className:"text-lg sm:text-xl lg:text-2xl text-green-50 leading-relaxed max-w-2xl mx-auto lg:mx-0 animate-fade-in-up",children:"Discover premium natural products crafted from nature's finest ingredients. Your journey to holistic well-being starts here with trusted quality and expertise."}),e.jsxs("div",{className:"flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4 justify-center lg:justify-start pt-4",children:[e.jsxs(R,{to:"/products",className:"inline-flex items-center justify-center px-6 sm:px-8 lg:px-10 py-3 sm:py-4 lg:py-5 bg-white text-emerald-800 font-bold rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 shadow-lg touch-target",children:["Explore Products",e.jsx(Se,{className:"ml-2 sm:ml-3 h-4 w-4 sm:h-5 sm:w-5"})]}),e.jsxs(R,{to:"/about",className:"inline-flex items-center justify-center px-6 sm:px-8 lg:px-10 py-3 sm:py-4 lg:py-5 bg-transparent text-white font-bold rounded-full border-2 border-white hover:bg-white hover:text-emerald-800 transition-all duration-300 transform hover:scale-105 shadow-lg touch-target",children:["Learn More",e.jsx(Se,{className:"ml-2 sm:ml-3 h-4 w-4 sm:h-5 sm:w-5"})]})]})]}),e.jsxs("div",{className:"relative hidden lg:block animate-fade-in-right",children:[e.jsx("img",{src:ci,alt:"Natural Products",className:"w-full h-[400px] lg:h-[500px] object-cover rounded-3xl shadow-2xl rotate-3 transform transition-transform duration-500 hover:rotate-0 hover:scale-105"}),e.jsx("div",{className:"absolute inset-0 bg-black opacity-10 rounded-3xl"})]})]})})}),e.jsx("section",{ref:l,className:`py-12 sm:py-16 bg-white relative transition-all duration-1000 ${n?"opacity-100 translate-y-0":"opacity-0 translate-y-10"}`,children:e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[e.jsx("div",{className:"lg:hidden",children:e.jsxs("div",{className:"bg-white p-6 sm:p-8 rounded-xl shadow-lg",children:[e.jsx("h2",{className:"text-2xl sm:text-3xl font-bold text-gray-900 mb-4",children:"Professional services"}),e.jsx("p",{className:"text-gray-700 mb-6 sm:mb-8",children:"Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut elit tellus, luctus nec ullamcorper mattis, pulvinar dapibus leo."}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6",children:[e.jsxs("div",{className:"flex items-center space-x-3 sm:space-x-4",children:[e.jsx("div",{className:"w-12 h-12 sm:w-16 sm:h-16 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0",children:e.jsx(Te,{className:"w-6 h-6 sm:w-10 sm:h-10 text-green-600"})}),e.jsx("span",{className:"text-base sm:text-lg font-semibold text-gray-900",children:"Marketing"})]}),e.jsxs("div",{className:"flex items-center space-x-3 sm:space-x-4",children:[e.jsx("div",{className:"w-12 h-12 sm:w-16 sm:h-16 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0",children:e.jsx(De,{className:"w-6 h-6 sm:w-10 sm:h-10 text-green-600"})}),e.jsx("span",{className:"text-base sm:text-lg font-semibold text-gray-900",children:"Distribution"})]}),e.jsxs("div",{className:"flex items-center space-x-3 sm:space-x-4",children:[e.jsx("div",{className:"w-12 h-12 sm:w-16 sm:h-16 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0",children:e.jsx(ae,{className:"w-6 h-6 sm:w-10 sm:h-10 text-green-600"})}),e.jsx("span",{className:"text-base sm:text-lg font-semibold text-gray-900",children:"Insurance"})]}),e.jsxs("div",{className:"flex items-center space-x-3 sm:space-x-4",children:[e.jsx("div",{className:"w-12 h-12 sm:w-16 sm:h-16 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0",children:e.jsx(Je,{className:"w-6 h-6 sm:w-10 sm:h-10 text-green-600"})}),e.jsx("span",{className:"text-base sm:text-lg font-semibold text-gray-900",children:"Consultation"})]})]})]})}),e.jsxs("div",{className:"hidden lg:flex justify-end",children:[e.jsx("img",{src:"https://upload.wikimedia.org/wikipedia/commons/d/db/Cannabis_sativa_001.JPG",alt:"Cannabis Plant",className:"absolute left-0 top-0 h-full w-3/5 object-cover opacity-100 z-0"}),e.jsxs("div",{className:"bg-white p-8 rounded-xl shadow-lg relative z-10 w-2/5 ml-auto",children:[e.jsx("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Professional services"}),e.jsx("p",{className:"text-gray-700 mb-8",children:"Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut elit tellus, luctus nec ullamcorper mattis, pulvinar dapibus leo."}),e.jsxs("div",{className:"grid grid-cols-2 gap-6",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0",children:e.jsx(Te,{className:"w-10 h-10 text-green-600"})}),e.jsx("span",{className:"text-lg font-semibold text-gray-900",children:"Marketing"})]}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0",children:e.jsx(De,{className:"w-10 h-10 text-green-600"})}),e.jsx("span",{className:"text-lg font-semibold text-gray-900",children:"Distribution"})]}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0",children:e.jsx(ae,{className:"w-10 h-10 text-green-600"})}),e.jsx("span",{className:"text-lg font-semibold text-gray-900",children:"Insurance"})]}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0",children:e.jsx(Je,{className:"w-10 h-10 text-green-600"})}),e.jsx("span",{className:"text-lg font-semibold text-gray-900",children:"Consultation"})]})]})]})]})]})}),e.jsx("section",{ref:i,className:`py-12 sm:py-16 bg-white transition-all duration-1000 ${c?"opacity-100 translate-y-0":"opacity-0 translate-y-10"}`,children:e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[e.jsxs("div",{className:"text-center mb-12 sm:mb-16",children:[e.jsx("h2",{className:"text-2xl sm:text-3xl font-bold text-gray-900 mb-4",children:"Why Choose Start Juicce?"}),e.jsx("p",{className:"text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto",children:"We're committed to providing you with the highest quality natural products and an exceptional shopping experience."})]}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8",children:[e.jsxs("div",{className:"text-center p-4 sm:p-6",children:[e.jsx("div",{className:"w-12 h-12 sm:w-16 sm:h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6",children:e.jsx(ae,{className:"h-6 w-6 sm:h-8 sm:w-8 text-green-600"})}),e.jsx("h3",{className:"text-lg sm:text-xl font-semibold text-gray-900 mb-3 sm:mb-4",children:"Premium Quality"}),e.jsx("p",{className:"text-sm sm:text-base text-gray-600",children:"All our products are sourced from certified organic farms and undergo rigorous quality testing."})]}),e.jsxs("div",{className:"text-center p-4 sm:p-6",children:[e.jsx("div",{className:"w-12 h-12 sm:w-16 sm:h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6",children:e.jsx(De,{className:"h-6 w-6 sm:h-8 sm:w-8 text-green-600"})}),e.jsx("h3",{className:"text-lg sm:text-xl font-semibold text-gray-900 mb-3 sm:mb-4",children:"Fast Delivery"}),e.jsx("p",{className:"text-sm sm:text-base text-gray-600",children:"Free shipping on orders over ₹500 with express delivery options available nationwide."})]}),e.jsxs("div",{className:"text-center p-4 sm:p-6 sm:col-span-2 lg:col-span-1",children:[e.jsx("div",{className:"w-12 h-12 sm:w-16 sm:h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6",children:e.jsx(H,{className:"h-6 w-6 sm:h-8 sm:w-8 text-green-600"})}),e.jsx("h3",{className:"text-lg sm:text-xl font-semibold text-gray-900 mb-3 sm:mb-4",children:"Expert Support"}),e.jsx("p",{className:"text-sm sm:text-base text-gray-600",children:"Our team of wellness experts is available to guide you in choosing the right products for your needs."})]})]})]})}),e.jsx("section",{ref:o,className:`py-16 ${a?.is_premium?"bg-gradient-to-br from-yellow-50 via-amber-50 to-yellow-100":"bg-gradient-to-br from-yellow-100 via-amber-100 to-yellow-200"} transition-all duration-1000 ${d?"opacity-100 translate-y-0":"opacity-0 translate-y-10"}`,children:e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:a?.is_premium?e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"text-center mb-12",children:[e.jsxs("div",{className:"inline-flex items-center space-x-2 bg-gradient-to-r from-yellow-200 to-amber-200 px-4 py-2 rounded-full mb-4 border border-yellow-300",children:[e.jsx(L,{className:"h-5 w-5 text-yellow-600"}),e.jsx("span",{className:"text-yellow-800 font-medium",children:"Premium Member"})]}),e.jsx("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Welcome to Premium!"}),e.jsx("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"You're now part of our exclusive premium community. Enjoy all the benefits and explore our premium-only products and features."})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 mb-12",children:[e.jsxs("div",{className:"bg-white p-6 rounded-xl shadow-lg border-2 border-yellow-300 bg-gradient-to-br from-white to-yellow-50",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx(Te,{className:"h-8 w-8 text-yellow-600"}),e.jsx("span",{className:"bg-gradient-to-r from-yellow-100 to-amber-100 text-yellow-800 text-xs font-medium px-2 py-1 rounded-full border border-yellow-300",children:"ACTIVE"})]}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Exclusive Products"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"Access to premium-only natural formulations and limited edition products."}),e.jsx(R,{to:"/products?premium=true",className:"text-yellow-600 font-semibold hover:underline hover:text-yellow-700",children:"Browse Premium Products →"})]}),e.jsxs("div",{className:"bg-white p-6 rounded-xl shadow-lg border-2 border-yellow-300 bg-gradient-to-br from-white to-yellow-50",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx(Je,{className:"h-8 w-8 text-yellow-600"}),e.jsx("span",{className:"bg-gradient-to-r from-yellow-100 to-amber-100 text-yellow-800 text-xs font-medium px-2 py-1 rounded-full border border-yellow-300",children:"25% OFF"})]}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Special Discounts"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"Enjoy up to 25% off on all products plus exclusive member-only sales."}),e.jsx(R,{to:"/products",className:"text-yellow-600 font-semibold hover:underline hover:text-yellow-700",children:"Shop with Discount →"})]}),e.jsxs("div",{className:"bg-white p-6 rounded-xl shadow-lg border-2 border-yellow-300 bg-gradient-to-br from-white to-yellow-50",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx(H,{className:"h-8 w-8 text-yellow-600"}),e.jsx("span",{className:"bg-gradient-to-r from-yellow-100 to-amber-100 text-yellow-800 text-xs font-medium px-2 py-1 rounded-full border border-yellow-300",children:"EARN"})]}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Referral Rewards"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"Earn wallet credits for every friend you refer who joins our platform."}),e.jsx(R,{to:"/dashboard/referrals",className:"text-yellow-600 font-semibold hover:underline hover:text-yellow-700",children:"View Referrals →"})]})]}),e.jsx("div",{className:"text-center",children:e.jsxs("div",{className:"inline-flex items-center space-x-4",children:[e.jsxs(R,{to:"/dashboard",className:"inline-flex items-center px-6 py-3 bg-gradient-to-r from-yellow-500 to-amber-500 text-white font-semibold rounded-full hover:from-yellow-600 hover:to-amber-600 transition-all duration-300 shadow-lg touch-target",children:["Go to Dashboard",e.jsx(Se,{className:"ml-2 h-5 w-5"})]}),e.jsxs(R,{to:"/products?premium=true",className:"inline-flex items-center px-6 py-3 bg-white text-yellow-600 font-semibold rounded-full border-2 border-yellow-500 hover:bg-yellow-50 hover:border-yellow-600 transition-all duration-300 shadow-lg touch-target",children:[e.jsx(L,{className:"mr-2 h-5 w-5"}),"Premium Products"]})]})})]}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"text-center mb-12",children:[e.jsxs("div",{className:"inline-flex items-center space-x-2 bg-gradient-to-r from-amber-200 to-yellow-300 px-4 py-2 rounded-full mb-4 border border-amber-300",children:[e.jsx(L,{className:"h-5 w-5 text-amber-700"}),e.jsx("span",{className:"text-amber-800 font-medium",children:"Premium Membership"})]}),e.jsx("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Unlock Exclusive Benefits"}),e.jsx("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"Join our premium community and enjoy exclusive products, special discounts, and amazing referral rewards."})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 mb-12",children:[e.jsxs("div",{className:"bg-white p-6 rounded-xl shadow-lg border border-amber-200 bg-gradient-to-br from-white to-amber-50",children:[e.jsx(Te,{className:"h-8 w-8 text-amber-600 mb-4"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Exclusive Products"}),e.jsx("p",{className:"text-gray-600",children:"Access to premium-only natural formulations and limited edition products."})]}),e.jsxs("div",{className:"bg-white p-6 rounded-xl shadow-lg border border-amber-200 bg-gradient-to-br from-white to-amber-50",children:[e.jsx(Je,{className:"h-8 w-8 text-amber-600 mb-4"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Special Discounts"}),e.jsx("p",{className:"text-gray-600",children:"Enjoy up to 25% off on all products plus exclusive member-only sales."})]}),e.jsxs("div",{className:"bg-white p-6 rounded-xl shadow-lg border border-amber-200 bg-gradient-to-br from-white to-amber-50",children:[e.jsx(H,{className:"h-8 w-8 text-amber-600 mb-4"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Referral Rewards"}),e.jsx("p",{className:"text-gray-600",children:"Earn wallet credits for every friend you refer who joins our platform."})]})]}),e.jsx("div",{className:"text-center",children:e.jsxs(R,{to:"/premium",className:"inline-flex items-center px-8 py-4 bg-gradient-to-r from-amber-500 to-yellow-600 text-white font-semibold rounded-full hover:from-amber-600 hover:to-yellow-700 transition-all duration-300 shadow-lg transform hover:scale-105 touch-target",children:[e.jsx(L,{className:"mr-2 h-5 w-5"}),"Upgrade to Premium"]})})]})})}),e.jsx("section",{className:"py-12 sm:py-16 bg-gray-50",children:e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[e.jsxs("div",{className:"text-center mb-8 sm:mb-12",children:[e.jsx("h2",{className:"text-2xl sm:text-3xl font-bold text-gray-900 mb-4",children:"Our New Products"}),e.jsx("p",{className:"text-lg sm:text-xl text-gray-600",children:"Vulpulpate enim nulla aliquet porttitor lacus luctus accumsan. Libero volutpat sed cras ornare arcu dui suspendisse vivamus."})]}),r?e.jsx("div",{className:"responsive-grid",children:[...Array(4)].map((g,u)=>e.jsxs("div",{className:"bg-white rounded-xl shadow-sm p-4 animate-pulse",children:[e.jsx("div",{className:"w-full h-48 bg-gray-200 rounded-lg mb-4"}),e.jsx("div",{className:"h-4 bg-gray-200 rounded mb-2"}),e.jsx("div",{className:"h-4 bg-gray-200 rounded w-3/4 mb-4"}),e.jsx("div",{className:"h-6 bg-gray-200 rounded w-1/2"})]},u))}):e.jsx("div",{className:"responsive-grid",children:t.map(g=>{const u=di();return e.jsx(St,{product:g,fallbackImageSrc:u},g.id)})})]})}),e.jsx("section",{ref:m,className:`py-16 bg-white transition-all duration-1000 ${h?"opacity-100 translate-y-0":"opacity-0 translate-y-10"}`,children:e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[e.jsxs("div",{className:"text-center mb-12",children:[e.jsx("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Our Best Services"}),e.jsx("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"Bibendum ut tristique et egestas quis ipsum suspendisse. Quis risus sed accumsan vulputate odio ut elementum."})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:[e.jsxs("div",{className:"bg-white p-6 rounded-xl shadow-sm text-center border border-gray-100",children:[e.jsx("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6",children:e.jsx(sr,{className:"w-10 h-10 text-green-600"})}),e.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Marijuana Medicine"}),e.jsx("p",{className:"text-gray-600",children:"Libero volutpat sed cras ornare arcu dui suspendisse vivamus."}),e.jsx(R,{to:"#",className:"text-emerald-600 font-semibold mt-4 inline-block hover:underline",children:"Read More"})]}),e.jsxs("div",{className:"bg-white p-6 rounded-xl shadow-sm text-center border border-gray-100",children:[e.jsx("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6",children:e.jsx(sr,{className:"w-10 h-10 text-green-600"})}),e.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Cannabis Leaf"}),e.jsx("p",{className:"text-gray-600",children:"Malesuada proin libero nunc consequat interdum."}),e.jsx(R,{to:"#",className:"text-emerald-600 font-semibold mt-4 inline-block hover:underline",children:"Read More"})]}),e.jsxs("div",{className:"bg-white p-6 rounded-xl shadow-sm text-center border border-gray-100",children:[e.jsx("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6",children:e.jsx(Yt,{className:"w-10 h-10 text-green-600"})}),e.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"100% Natural Edibles"}),e.jsx("p",{className:"text-gray-600",children:"Vestibulum mattis ullamcorper velit sed ullamco."}),e.jsx(R,{to:"#",className:"text-emerald-600 font-semibold mt-4 inline-block hover:underline",children:"Read More"})]}),e.jsxs("div",{className:"bg-white p-6 rounded-xl shadow-sm text-center border border-gray-100",children:[e.jsx("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6",children:e.jsx(Vt,{className:"w-10 h-10 text-green-600"})}),e.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Cannabis Flower"}),e.jsx("p",{className:"text-gray-600",children:"Odio facilisis mauris sit amet massa vitae tortor."}),e.jsx(R,{to:"#",className:"text-emerald-600 font-semibold mt-4 inline-block hover:underline",children:"Read More"})]}),e.jsxs("div",{className:"bg-white p-6 rounded-xl shadow-sm text-center border border-gray-100",children:[e.jsx("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0",children:e.jsx(Gt,{className:"w-10 h-10 text-green-600"})}),e.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"100% Fresh Apothecary"}),e.jsx("p",{className:"text-gray-600",children:"Faucibus turpis in eu mi bibendum neque egestas."}),e.jsx(R,{to:"#",className:"text-emerald-600 font-semibold mt-4 inline-block hover:underline",children:"Read More"})]}),e.jsxs("div",{className:"bg-white p-6 rounded-xl shadow-sm text-center border border-gray-100",children:[e.jsx("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6",children:e.jsx(Ht,{className:"w-10 h-10 text-green-600"})}),e.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Safe & Legal"}),e.jsx("p",{className:"text-gray-600",children:"Dolor sit amet, consectetur adipiscing elit etiam."}),e.jsx(R,{to:"#",className:"text-emerald-600 font-semibold mt-4 inline-block hover:underline",children:"Read More"})]})]})]})}),e.jsx("section",{ref:p,className:`py-16 bg-emerald-800 text-white transition-all duration-1000 ${x?"opacity-100 translate-y-0":"opacity-0 translate-y-10"}`,children:e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[e.jsx("h2",{className:"text-3xl font-bold mb-4",children:"We Are What You Need"}),e.jsx("p",{className:"text-xl text-green-100 mb-12 max-w-3xl mx-auto",children:"Consectetur libero sed tincidunt eget nullam non nisi. Non diam phasellus vestibulum lorem sed risus ultricies elementum."}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[e.jsxs("div",{className:"flex flex-col items-center p-6",children:[e.jsx("div",{className:"w-20 h-20 bg-green-700 rounded-full flex items-center justify-center mb-6",children:e.jsx(Kt,{className:"w-12 h-12 text-white"})}),e.jsx("h3",{className:"text-xl font-semibold mb-2",children:"Organic Cannabis"}),e.jsx("p",{className:"text-green-100 mb-4",children:"Amet aliquam id diam maecenas ultricies eget mauris pharetra."}),e.jsx(R,{to:"#",className:"flex items-center text-lime-400 font-semibold hover:underline",children:e.jsx(Se,{className:"h-5 w-5"})})]}),e.jsxs("div",{className:"flex flex-col items-center p-6",children:[e.jsx("div",{className:"w-20 h-20 bg-green-700 rounded-full flex items-center justify-center mb-6",children:e.jsx(Zr,{className:"w-12 h-12 text-white"})}),e.jsx("h3",{className:"text-xl font-semibold mb-2",children:"Extraction Procedure"}),e.jsx("p",{className:"text-green-100 mb-4",children:"Marwuves arcu felis bibendum ut tristique. In ante met dictum."}),e.jsx(R,{to:"#",className:"flex items-center text-lime-400 font-semibold hover:underline",children:e.jsx(Se,{className:"h-5 w-5"})})]}),e.jsxs("div",{className:"flex flex-col items-center p-6",children:[e.jsx("div",{className:"w-20 h-20 bg-green-700 rounded-full flex items-center justify-center mb-6",children:e.jsx(Jt,{className:"w-12 h-12 text-white"})}),e.jsx("h3",{className:"text-xl font-semibold mb-2",children:"Formulations"}),e.jsx("p",{className:"text-green-100 mb-4",children:"Phyes orese felis bibendum set inr ut tristique sei nullam non."}),e.jsx(R,{to:"#",className:"flex items-center text-lime-400 font-semibold hover:underline",children:e.jsx(Se,{className:"h-5 w-5"})})]}),e.jsxs("div",{className:"flex flex-col items-center p-6",children:[e.jsx("div",{className:"w-20 h-20 bg-green-700 rounded-full flex items-center justify-center mb-6",children:e.jsx(Z,{className:"w-12 h-12 text-white"})}),e.jsx("h3",{className:"text-xl font-semibold mb-2",children:"Free Delivery"}),e.jsx("p",{className:"text-green-100 mb-4",children:"Non diam phasellus vestibulum lorem sed risus ultricies elementum."}),e.jsx(R,{to:"#",className:"flex items-center text-lime-400 font-semibold hover:underline",children:e.jsx(Se,{className:"h-5 w-5"})})]})]})]})}),e.jsx("section",{className:"py-16 bg-green-600",children:e.jsxs("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[e.jsx("h2",{className:"text-3xl font-bold text-white mb-4",children:"Stay Updated with Start Juicce"}),e.jsx("p",{className:"text-xl text-green-100 mb-8",children:"Get the latest updates on new products, health tips, and exclusive offers."}),e.jsxs("div",{className:"flex flex-col sm:flex-row max-w-md mx-auto",children:[e.jsx("input",{type:"email",placeholder:"Enter your email",className:"flex-1 px-6 py-4 rounded-l-full sm:rounded-r-none rounded-r-full border-0 focus:outline-none focus:ring-2 focus:ring-green-300"}),e.jsx("button",{className:"px-8 py-4 bg-green-800 text-white font-semibold rounded-r-full sm:rounded-l-none rounded-l-full hover:bg-green-900 transition-colors",children:"Subscribe"})]})]})})]})},ui=({isOpen:t,onToggle:s})=>{const{filters:r,categories:a,setFilters:l,sortBy:n,setSortBy:i}=Ge(),c=()=>{l({category:"",subcategory:"",priceRange:[0,1e3],rating:0,inStock:!1,isPremiumOnly:!1})};return e.jsxs(e.Fragment,{children:[e.jsxs("button",{onClick:s,className:"md:hidden flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50",children:[e.jsx(hr,{className:"h-4 w-4"}),e.jsx("span",{children:"Filters"})]}),e.jsxs("div",{className:`${t?"block":"hidden"} md:block bg-white p-6 rounded-lg shadow-sm border border-gray-200`,children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Filters"}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsx("button",{onClick:c,className:"text-sm text-gray-500 hover:text-gray-700",children:"Clear All"}),e.jsx("button",{onClick:s,className:"md:hidden text-gray-500 hover:text-gray-700",children:e.jsx(oe,{className:"h-4 w-4"})})]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Sort By"}),e.jsxs("select",{value:n,onChange:o=>i(o.target.value),className:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500",children:[e.jsx("option",{value:"name",children:"Name (A-Z)"}),e.jsx("option",{value:"price-low",children:"Price (Low to High)"}),e.jsx("option",{value:"price-high",children:"Price (High to Low)"}),e.jsx("option",{value:"rating",children:"Highest Rated"}),e.jsx("option",{value:"newest",children:"Newest First"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Category"}),e.jsxs("select",{value:r.category,onChange:o=>l({category:o.target.value,subcategory:""}),className:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500",children:[e.jsx("option",{value:"",children:"All Categories"}),a.map(o=>e.jsx("option",{value:o.name,children:o.name},o.id))]})]}),r.category&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Subcategory"}),e.jsxs("select",{value:r.subcategory,onChange:o=>l({subcategory:o.target.value}),className:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500",children:[e.jsx("option",{value:"",children:"All Subcategories"}),a.find(o=>o.name===r.category)?.subcategories.map(o=>e.jsx("option",{value:o.name,children:o.name},o.id))]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Price Range"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("input",{type:"range",min:"0",max:"1000",value:r.priceRange[1],onChange:o=>l({priceRange:[r.priceRange[0],parseInt(o.target.value)]}),className:"w-full accent-green-600"}),e.jsxs("div",{className:"flex justify-between text-sm text-gray-600",children:[e.jsxs("span",{children:["₹",r.priceRange[0]]}),e.jsxs("span",{children:["₹",r.priceRange[1]]})]})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Minimum Rating"}),e.jsxs("div",{className:"space-y-2",children:[[4,3,2,1].map(o=>e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"radio",name:"rating",value:o,checked:r.rating===o,onChange:d=>l({rating:parseInt(d.target.value)}),className:"mr-2 accent-green-600"}),e.jsxs("span",{className:"text-sm",children:[o,"+ Stars"]})]},o)),e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"radio",name:"rating",value:0,checked:r.rating===0,onChange:o=>l({rating:parseInt(o.target.value)}),className:"mr-2 accent-green-600"}),e.jsx("span",{className:"text-sm",children:"All Ratings"})]})]})]}),e.jsx("div",{children:e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",checked:r.inStock,onChange:o=>l({inStock:o.target.checked}),className:"mr-2 accent-green-600"}),e.jsx("span",{className:"text-sm",children:"In Stock Only"})]})}),e.jsx("div",{children:e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",checked:r.isPremiumOnly,onChange:o=>l({isPremiumOnly:o.target.checked}),className:"mr-2 accent-green-600"}),e.jsx("span",{className:"text-sm",children:"Premium Products Only"})]})})]})]})]})},xi=()=>{const[t]=Qr(),[s,r]=f.useState(!1),[a,l]=f.useState("grid"),{isLoading:n,setSearchQuery:i,getFilteredProducts:c,fetchProducts:o,fetchCategories:d}=Ge(),m=c();return f.useEffect(()=>{o(),d();const h=t.get("search");h&&i(h)},[t]),e.jsx("div",{className:"min-h-screen bg-gray-50",children:e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8",children:[e.jsxs("div",{className:"mb-6 sm:mb-8",children:[e.jsx("h1",{className:"text-2xl sm:text-3xl font-bold text-gray-900 mb-4",children:"Natural Products"}),e.jsx("p",{className:"text-sm sm:text-base text-gray-600",children:"Discover our complete collection of premium natural products for your wellness journey."})]}),e.jsxs("div",{className:"flex flex-col lg:flex-row gap-6 lg:gap-8",children:[e.jsx("aside",{className:"lg:w-64 flex-shrink-0",children:e.jsx(ui,{isOpen:s,onToggle:()=>r(!s)})}),e.jsxs("main",{className:"flex-1",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between mb-6 bg-white p-4 rounded-lg shadow-sm gap-4",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("button",{onClick:()=>r(!s),className:"lg:hidden flex items-center space-x-2 px-4 py-2 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors touch-target",children:e.jsx("span",{className:"text-sm font-medium",children:"Filters"})}),e.jsxs("span",{className:"text-sm sm:text-base text-gray-600",children:[m.length," products found"]})]}),e.jsxs("div",{className:"flex items-center justify-end space-x-2",children:[e.jsx("span",{className:"text-sm text-gray-500 hidden sm:inline",children:"View:"}),e.jsx("button",{onClick:()=>l("grid"),className:`p-2 rounded touch-target ${a==="grid"?"bg-green-100 text-green-600":"text-gray-400 hover:text-gray-600"}`,children:e.jsx(Xt,{className:"h-5 w-5"})}),e.jsx("button",{onClick:()=>l("list"),className:`p-2 rounded touch-target ${a==="list"?"bg-green-100 text-green-600":"text-gray-400 hover:text-gray-600"}`,children:e.jsx(Qt,{className:"h-5 w-5"})})]})]}),n?e.jsx("div",{className:"responsive-grid",children:[...Array(9)].map((h,p)=>e.jsxs("div",{className:"bg-white rounded-xl shadow-sm p-4 animate-pulse",children:[e.jsx("div",{className:"w-full h-48 bg-gray-200 rounded-lg mb-4"}),e.jsx("div",{className:"h-4 bg-gray-200 rounded mb-2"}),e.jsx("div",{className:"h-4 bg-gray-200 rounded w-3/4 mb-4"}),e.jsx("div",{className:"h-6 bg-gray-200 rounded w-1/2"})]},p))}):m.length===0?e.jsxs("div",{className:"text-center py-12 sm:py-16 bg-white rounded-lg shadow-sm border border-gray-200 flex flex-col items-center justify-center px-4",children:[e.jsx("div",{className:"w-20 h-20 sm:w-28 sm:h-28 bg-green-100 rounded-full mx-auto mb-6 flex items-center justify-center text-green-600",children:e.jsx("svg",{className:"w-12 h-12 sm:w-16 sm:h-16",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"1.5",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m0 0H7"})})}),e.jsx("h3",{className:"text-xl sm:text-2xl font-bold text-gray-900 mb-3",children:"No Products Found"}),e.jsx("p",{className:"text-sm sm:text-base text-gray-600 max-w-md mb-6 sm:mb-8",children:"We couldn't find any products matching your current filters or search terms. Try adjusting your selections or clearing all filters."}),e.jsx("button",{onClick:()=>{i(""),o()},className:"inline-flex items-center px-6 py-3 bg-green-600 text-white font-semibold rounded-full hover:bg-green-700 transition-colors shadow-md touch-target",children:"Clear All Filters"})]}):e.jsx("div",{className:`grid gap-4 sm:gap-6 ${a==="grid"?"grid-cols-1 sm:grid-cols-2 lg:grid-cols-3":"grid-cols-1"}`,children:m.map(h=>e.jsx(St,{product:h},h.id))}),m.length>0&&e.jsx("div",{className:"text-center mt-8 sm:mt-12",children:e.jsx("button",{className:"px-6 sm:px-8 py-3 bg-green-600 text-white font-semibold rounded-full hover:bg-green-700 transition-colors touch-target",children:"Load More Products"})})]})]})]})})};function hi(){const{id:t}=$t(),s=Oe(),{user:r}=J(),{addItem:a}=ts(),[l,n]=f.useState(null),[i,c]=f.useState([]),[o,d]=f.useState(1),[m,h]=f.useState(!0),[p,x]=f.useState(!1);f.useEffect(()=>{t&&(g(),u())},[t]);const g=async()=>{try{const{data:N,error:P}=await y.from("products").select("*").eq("id",t).single();if(P)throw P;n(N)}catch(N){console.error("Error fetching product:",N),k.error("Product not found"),s("/products")}finally{h(!1)}},u=async()=>{try{const{data:N,error:P}=await y.from("reviews").select(`
          *,
          user:users(full_name)
        `).eq("product_id",t).order("created_at",{ascending:!1}).limit(10);if(P)throw P;c(N||[])}catch(N){console.error("Error fetching reviews:",N)}},j=()=>{if(!r){k.error("Please login to add items to cart"),s("/login");return}if(l){if(l.is_premium_only&&!r.is_premium){k.error("This product is only available for premium members");return}a(l,o),k.success("Added to cart successfully!")}},b=N=>{const P=o+N;P>=1&&P<=(l?.stock_quantity||1)&&d(P)};return m?e.jsx("div",{className:"min-h-screen flex items-center justify-center",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"})}):l?e.jsx("div",{className:"min-h-screen bg-gray-50 py-8",children:e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[e.jsx("nav",{className:"flex mb-8","aria-label":"Breadcrumb",children:e.jsxs("ol",{className:"flex items-center space-x-4",children:[e.jsx("li",{children:e.jsx("button",{onClick:()=>s("/"),className:"text-gray-500 hover:text-gray-700",children:"Home"})}),e.jsx("li",{children:e.jsx("span",{className:"text-gray-400",children:"/"})}),e.jsx("li",{children:e.jsx("button",{onClick:()=>s("/products"),className:"text-gray-500 hover:text-gray-700",children:"Products"})}),e.jsx("li",{children:e.jsx("span",{className:"text-gray-400",children:"/"})}),e.jsx("li",{className:"text-gray-900 font-medium",children:l.name})]})}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"aspect-square bg-white rounded-2xl overflow-hidden shadow-lg",children:e.jsx("img",{src:_r(Nr(l.image_url),600,600),alt:l.name,className:"w-full h-full object-cover",onError:N=>vr(N)})}),l.is_premium_only&&e.jsx("div",{className:"bg-gradient-to-r from-yellow-400 to-yellow-600 text-white px-4 py-2 rounded-lg text-center font-medium",children:e.jsxs("span",{className:"flex items-center justify-center gap-2",children:[e.jsx(Ce,{className:"h-4 w-4 fill-current"}),"Premium Exclusive"]})})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:l.name}),e.jsx("div",{className:"flex items-center gap-4 mb-4",children:e.jsxs("div",{className:"flex items-center",children:[[...Array(5)].map((N,P)=>e.jsx(Ce,{className:`h-5 w-5 ${P<Math.floor(l.rating)?"text-yellow-400 fill-current":"text-gray-300"}`},P)),e.jsxs("span",{className:"ml-2 text-gray-600",children:[l.rating," (",l.reviews_count," reviews)"]})]})}),e.jsxs("p",{className:"text-4xl font-bold text-green-600 mb-6",children:["₹",l.price]})]}),e.jsx("div",{className:"prose prose-gray max-w-none",children:e.jsx("p",{children:l.description})}),e.jsxs("div",{children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2",children:[e.jsx(sr,{className:"h-5 w-5 text-green-600"}),"Natural Ingredients"]}),e.jsx("div",{className:"flex flex-wrap gap-2",children:l.ingredients.map((N,P)=>e.jsx("span",{className:"bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium",children:N},P))})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("span",{className:"text-gray-700 font-medium",children:"Quantity:"}),e.jsxs("div",{className:"flex items-center border border-gray-300 rounded-lg",children:[e.jsx("button",{onClick:()=>b(-1),disabled:o<=1,className:"p-2 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed",children:e.jsx(ws,{className:"h-4 w-4"})}),e.jsx("span",{className:"px-4 py-2 font-medium",children:o}),e.jsx("button",{onClick:()=>b(1),disabled:o>=l.stock_quantity,className:"p-2 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed",children:e.jsx(re,{className:"h-4 w-4"})})]}),e.jsxs("span",{className:"text-sm text-gray-500",children:[l.stock_quantity," available"]})]}),e.jsxs("div",{className:"flex gap-4",children:[e.jsxs("button",{onClick:j,disabled:l.stock_quantity===0,className:"flex-1 bg-green-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2",children:[e.jsx(ge,{className:"h-5 w-5"}),l.stock_quantity===0?"Out of Stock":"Add to Cart"]}),e.jsx("button",{onClick:()=>x(!p),className:`p-3 rounded-lg border transition-colors ${p?"bg-red-50 border-red-200 text-red-600":"bg-white border-gray-300 text-gray-600 hover:bg-gray-50"}`,children:e.jsx(Zt,{className:`h-5 w-5 ${p?"fill-current":""}`})})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 pt-6 border-t border-gray-200",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(De,{className:"h-5 w-5 text-green-600"}),e.jsx("span",{className:"text-sm text-gray-700",children:"Free Delivery"})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(ae,{className:"h-5 w-5 text-green-600"}),e.jsx("span",{className:"text-sm text-gray-700",children:"Quality Assured"})]})]})]})]}),e.jsxs("div",{className:"mt-16",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-8",children:"Customer Reviews"}),i.length>0?e.jsx("div",{className:"space-y-6",children:i.map(N=>e.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-sm border border-gray-200",children:[e.jsxs("div",{className:"flex items-start justify-between mb-4",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-gray-900",children:N.user.full_name}),e.jsx("div",{className:"flex items-center mt-1",children:[...Array(5)].map((P,A)=>e.jsx(Ce,{className:`h-4 w-4 ${A<N.rating?"text-yellow-400 fill-current":"text-gray-300"}`},A))})]}),e.jsx("span",{className:"text-sm text-gray-500",children:new Date(N.created_at).toLocaleDateString()})]}),e.jsx("p",{className:"text-gray-700",children:N.comment})]},N.id))}):e.jsx("div",{className:"text-center py-12",children:e.jsx("p",{className:"text-gray-500",children:"No reviews yet. Be the first to review this product!"})})]})]})}):e.jsx("div",{className:"min-h-screen flex items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Product not found"}),e.jsx("button",{onClick:()=>s("/products"),className:"bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors",children:"Back to Products"})]})})}const Ps=(t,s={})=>{const{showSymbol:r=!0,showDecimals:a=!0,locale:l="en-IN"}=s,n=typeof t=="string"?parseFloat(t):t;if(isNaN(n))return r?"₹0.00":"0.00";let c=new Intl.NumberFormat(l,{style:r?"currency":"decimal",currency:"INR",minimumFractionDigits:a?2:0,maximumFractionDigits:a?2:0}).format(n);return r&&l==="en-IN"&&(c=c.replace(/₹|INR/g,"₹")),c},gi=()=>{const t=Oe(),{items:s,updateQuantity:r,removeItem:a,getTotalPrice:l,getTotalItems:n}=ts(),i=(d,m)=>{m<=0?a(d):r(d,m)},c=()=>{t("/checkout")},o=()=>{t("/products")};return s.length===0?e.jsx("div",{className:"container mx-auto px-4 py-8 sm:py-12",children:e.jsx("div",{className:"max-w-2xl mx-auto text-center",children:e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-8 sm:p-12",children:[e.jsx(Me,{className:"h-16 w-16 sm:h-20 sm:w-20 text-gray-400 mx-auto mb-6"}),e.jsx("h1",{className:"text-2xl sm:text-3xl font-bold text-gray-900 mb-4",children:"Your Cart is Empty"}),e.jsx("p",{className:"text-gray-600 mb-8 text-sm sm:text-base",children:"Looks like you haven't added any items to your cart yet."}),e.jsx("button",{onClick:o,className:"bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors font-medium text-sm sm:text-base",children:"Start Shopping"})]})})}):e.jsx("div",{className:"container mx-auto px-4 py-6 sm:py-8",children:e.jsxs("div",{className:"max-w-4xl mx-auto",children:[e.jsxs("div",{className:"flex items-center gap-4 mb-6 sm:mb-8",children:[e.jsx("button",{onClick:()=>t(-1),className:"p-2 hover:bg-gray-100 rounded-lg transition-colors touch-target",children:e.jsx(zs,{className:"h-5 w-5 text-gray-600"})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl sm:text-3xl font-bold text-gray-900",children:"Shopping Cart"}),e.jsxs("p",{className:"text-gray-600 text-sm sm:text-base",children:[n()," ",n()===1?"item":"items"," in your cart"]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6 sm:gap-8",children:[e.jsxs("div",{className:"lg:col-span-2",children:[e.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden",children:e.jsx("div",{className:"divide-y divide-gray-200",children:s.map(d=>e.jsx("div",{className:"p-4 sm:p-6",children:e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("img",{src:d.product.image_url||"/placeholder-product.jpg",alt:d.product.name,className:"w-16 h-16 sm:w-20 sm:h-20 object-cover rounded-lg"})}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h3",{className:"text-base sm:text-lg font-medium text-gray-900 mb-1",children:d.product.name}),e.jsxs("p",{className:"text-sm text-gray-600 mb-2",children:[Ps(d.product.price)," each"]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs("div",{className:"flex items-center border border-gray-300 rounded-lg",children:[e.jsx("button",{onClick:()=>i(d.id,d.quantity-1),className:"p-2 hover:bg-gray-50 transition-colors touch-target",children:e.jsx(ws,{className:"h-4 w-4"})}),e.jsx("span",{className:"px-3 py-2 text-sm font-medium min-w-[3rem] text-center",children:d.quantity}),e.jsx("button",{onClick:()=>i(d.id,d.quantity+1),className:"p-2 hover:bg-gray-50 transition-colors touch-target",children:e.jsx(re,{className:"h-4 w-4"})})]}),e.jsx("button",{onClick:()=>a(d.id),className:"p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors touch-target",children:e.jsx(gr,{className:"h-4 w-4"})})]})]}),e.jsx("div",{className:"text-right",children:e.jsx("p",{className:"text-lg font-semibold text-gray-900",children:Ps(d.product.price*d.quantity)})})]})},d.id))})}),e.jsx("div",{className:"mt-6",children:e.jsx("button",{onClick:o,className:"text-green-600 hover:text-green-700 font-medium text-sm sm:text-base",children:"← Continue Shopping"})})]}),e.jsx("div",{className:"lg:col-span-1",children:e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6 sticky top-6",children:[e.jsx("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Order Summary"}),e.jsxs("div",{className:"space-y-3 mb-6",children:[e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsxs("span",{className:"text-gray-600",children:["Subtotal (",n()," items)"]}),e.jsx("span",{className:"font-medium",children:Ps(l())})]}),e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{className:"text-gray-600",children:"Shipping"}),e.jsx("span",{className:"font-medium text-green-600",children:"Free"})]}),e.jsx("div",{className:"border-t border-gray-200 pt-3",children:e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-base font-semibold text-gray-900",children:"Total"}),e.jsx("span",{className:"text-lg font-bold text-gray-900",children:Ps(l())})]})})]}),e.jsx("button",{onClick:c,className:"w-full bg-green-600 text-white py-3 px-4 rounded-lg hover:bg-green-700 transition-colors font-medium text-sm sm:text-base touch-target",children:"Proceed to Checkout"}),e.jsx("div",{className:"mt-4 text-center",children:e.jsx("p",{className:"text-xs text-gray-500",children:"Secure checkout with SSL encryption"})})]})})]})]})})};class fi{static async getDashboardStats(){try{const{data:s,error:r}=await y.rpc("get_dashboard_stats");if(r)throw new Error(r.message);return s}catch(s){throw console.error("Error getting dashboard stats:",s),s}}static async getUserWalletInfo(s){try{const{data:r,error:a}=await y.rpc("get_user_wallet_info",{p_user_id:s});if(a)throw new Error(a.message);return r}catch(r){throw console.error("Error getting wallet info:",r),r}}static async processReferralBonus(s,r,a=100){try{if(console.log("Multi-level referral bonus processing:",{referrerId:s,referredUserId:r,bonusAmount:a}),!await Fe.isMultiLevelReferralEnabled())throw new Error("Multi-level referral system is disabled");const{data:n,error:i}=await y.from("users").select("id, email, full_name, is_premium, referred_by").eq("id",r).single();if(i||!n)throw new Error("Referred user not found");if(!n.is_premium)throw new Error("User is not premium - no bonuses will be processed");if(!n.referred_by)throw new Error("User has no referral code");const{data:c,error:o}=await y.from("users").select("id, email, referral_code, is_premium").eq("id",s).single();if(o||!c)throw new Error("Referrer not found");if(!c.is_premium)throw new Error("Referrer is not premium");if(n.referred_by!==c.referral_code)throw new Error("Referral code mismatch");const d=await Fe.processMultiLevelReferral(r,n.email,c.referral_code);if(d.success)return{success:!0,message:"Multi-level referral bonus processed successfully",totalBonusDistributed:d.totalBonusDistributed,levelsProcessed:d.levelsProcessed,referralId:d.referralId,distributions:d.chainUsers.map(m=>({userId:m.id,level:m.level,email:m.email,name:m.full_name}))};throw new Error(`Multi-level referral processing failed: ${d.errors.join(", ")}`)}catch(l){throw console.error("Error processing multi-level referral bonus:",l),l}}static async upgradeToPremium(s,r=299){try{const{data:a,error:l}=await y.rpc("upgrade_to_premium",{p_user_id:s,p_payment_amount:r});if(l)throw new Error(l.message);return a}catch(a){throw console.error("Error upgrading to premium:",a),a}}static async createNotification(s,r,a,l="info",n={}){try{const{data:i,error:c}=await y.rpc("create_notification",{p_user_id:s,p_title:r,p_message:a,p_type:l,p_metadata:n});if(c)throw new Error(c.message);return i}catch(i){throw console.error("Error creating notification:",i),i}}static async markNotificationRead(s){try{const{data:r,error:a}=await y.rpc("mark_notification_read",{notification_id:s});if(a)throw new Error(a.message);return r}catch(r){throw console.error("Error marking notification as read:",r),r}}}const Ct=Gs((t,s)=>({balance:0,transactions:[],loading:!1,error:null,fetchBalance:async r=>{try{t({loading:!0,error:null});const a=await fi.getUserWalletInfo(r);t({balance:a?.current_balance||0,loading:!1})}catch(a){console.error("Error fetching wallet balance:",a),k.error("Failed to fetch wallet balance"),t({error:a instanceof Error?a.message:"Failed to fetch balance",loading:!1})}},fetchTransactions:async r=>{try{t({loading:!0,error:null});const{data:a,error:l}=await y.from("wallet_transactions").select("*").eq("user_id",r).order("created_at",{ascending:!1});if(l)throw l;t({transactions:a||[],loading:!1})}catch(a){t({error:a instanceof Error?a.message:"Failed to fetch transactions",loading:!1})}},refreshData:async r=>{const{fetchBalance:a,fetchTransactions:l}=s();await Promise.all([a(r),l(r)])}}));function pi(){const t=Oe(),{user:s}=J(),{items:r,total:a,clearCart:l}=ts(),{balance:n,deductMoney:i}=Ct(),[c,o]=f.useState(""),[d,m]=f.useState("card"),[h,p]=f.useState(0),[x,g]=f.useState(!1);f.useEffect(()=>{if(!s){t("/login");return}if(r.length===0){t("/cart");return}s.address&&o(s.address)},[s,r,t]);const u=Math.max(0,a-h),j=N=>{const P=Math.min(n,a),A=Math.max(0,Math.min(N,P));p(A),A===a?m("wallet"):A>0?m("mixed"):m("card")},b=async()=>{if(!c.trim()){k.error("Please enter delivery address");return}if(d==="wallet"&&n<a){k.error("Insufficient wallet balance");return}g(!0);try{const N=`ORD${new Date().getFullYear()}${String(new Date().getMonth()+1).padStart(2,"0")}${String(Date.now()).slice(-6)}`,P={user_id:s.id,order_number:N,total_amount:a,wallet_used:h,payment_method:d,delivery_address:c,shipping_cost:0,tax_amount:0,status:"pending",estimated_delivery:new Date(Date.now()+3*24*60*60*1e3).toISOString().split("T")[0]},{data:A,error:E}=await y.from("orders").insert(P).select().single();if(E)throw E;const _=r.map(S=>({order_id:A.id,product_id:S.product.id,quantity:S.quantity,price:S.product.price})),{error:w}=await y.from("order_items").insert(_);if(w)throw w;if(h>0&&!await i(h,`Payment for order #${A.id}`,"order",A.id))throw new Error("Failed to deduct wallet amount");for(const S of r){const{data:C,error:T}=await y.rpc("process_inventory_transaction",{p_product_id:S.product.id,p_transaction_type:"sale",p_quantity_change:-S.quantity,p_reference_type:"order",p_reference_id:A.id,p_notes:`Sale - Order ${N}`});if(T||!C?.success)throw new Error(`Failed to update inventory for ${S.product.name}`)}await y.from("order_notifications").insert({order_id:A.id,user_id:s.id,notification_type:"order_placed",title:"Order Placed Successfully!",message:`Your order ${N} has been placed successfully. We'll notify you when it's confirmed.`,sent_via:["in_app"]}),l(),k.success(`Order ${N} placed successfully!`),t("/dashboard/orders")}catch(N){console.error("Error placing order:",N),k.error("Failed to place order. Please try again.")}finally{g(!1)}};return!s||r.length===0?null:e.jsx("div",{className:"min-h-screen bg-gray-50 py-4 sm:py-8",children:e.jsxs("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:[e.jsx("h1",{className:"text-2xl sm:text-3xl font-bold text-gray-900 mb-6 sm:mb-8",children:"Checkout"}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6 sm:gap-8",children:[e.jsxs("div",{className:"lg:col-span-2 space-y-4 sm:space-y-6",children:[e.jsxs("div",{className:"bg-white p-4 sm:p-6 rounded-lg shadow-sm",children:[e.jsxs("h2",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2",children:[e.jsx(rs,{className:"h-5 w-5 text-green-600"}),"Delivery Address"]}),e.jsx("textarea",{value:c,onChange:N=>o(N.target.value),placeholder:"Enter your complete delivery address...",className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm sm:text-base",rows:4,required:!0})]}),e.jsxs("div",{className:"bg-white p-4 sm:p-6 rounded-lg shadow-sm",children:[e.jsxs("h2",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2",children:[e.jsx(xs,{className:"h-5 w-5 text-green-600"}),"Payment Method"]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between p-4 border border-gray-200 rounded-lg gap-3",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(K,{className:"h-5 w-5 text-green-600"}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-gray-900",children:"Use Wallet"}),e.jsxs("p",{className:"text-sm text-gray-500",children:["Available: ₹",n]})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"₹"}),e.jsx("input",{type:"number",value:h,onChange:N=>j(Number(N.target.value)),max:Math.min(n,a),min:0,className:"w-20 sm:w-24 p-2 border border-gray-300 rounded text-center text-sm sm:text-base"})]})]}),u>0&&e.jsx("div",{className:"p-4 border border-gray-200 rounded-lg",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(xs,{className:"h-5 w-5 text-blue-600"}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-gray-900",children:"Card Payment"}),e.jsxs("p",{className:"text-sm text-gray-500",children:["Remaining: ₹",u]})]})]})})]})]}),e.jsxs("div",{className:"bg-white p-4 sm:p-6 rounded-lg shadow-sm",children:[e.jsxs("h2",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2",children:[e.jsx(Z,{className:"h-5 w-5 text-green-600"}),"Order Items"]}),e.jsx("div",{className:"space-y-4",children:r.map(N=>e.jsxs("div",{className:"flex items-center gap-3 sm:gap-4 p-3 sm:p-4 border border-gray-200 rounded-lg",children:[e.jsx("img",{src:N.product.image_url,alt:N.product.name,className:"w-12 h-12 sm:w-16 sm:h-16 object-cover rounded-lg flex-shrink-0"}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h3",{className:"font-medium text-gray-900 text-sm sm:text-base truncate",children:N.product.name}),e.jsxs("p",{className:"text-xs sm:text-sm text-gray-500",children:["Quantity: ",N.quantity]})]}),e.jsxs("p",{className:"font-semibold text-gray-900 text-sm sm:text-base",children:["₹",N.product.price*N.quantity]})]},N.id))})]})]}),e.jsxs("div",{className:"bg-white p-4 sm:p-6 rounded-lg shadow-sm h-fit",children:[e.jsx("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Order Summary"}),e.jsxs("div",{className:"space-y-3 mb-6",children:[e.jsxs("div",{className:"flex justify-between text-sm sm:text-base",children:[e.jsx("span",{className:"text-gray-600",children:"Subtotal"}),e.jsxs("span",{className:"font-medium",children:["₹",a]})]}),e.jsxs("div",{className:"flex justify-between text-sm sm:text-base",children:[e.jsx("span",{className:"text-gray-600",children:"Delivery"}),e.jsx("span",{className:"font-medium text-green-600",children:"Free"})]}),h>0&&e.jsxs("div",{className:"flex justify-between text-green-600 text-sm sm:text-base",children:[e.jsx("span",{children:"Wallet Used"}),e.jsxs("span",{children:["-₹",h]})]}),e.jsx("div",{className:"border-t border-gray-200 pt-3",children:e.jsxs("div",{className:"flex justify-between text-lg font-semibold",children:[e.jsx("span",{children:"Total"}),e.jsxs("span",{children:["₹",u]})]})})]}),e.jsx("button",{onClick:b,disabled:x||!c.trim(),className:"w-full bg-green-600 text-white py-3 sm:py-4 rounded-lg font-medium hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed text-sm sm:text-base touch-target",children:x?"Placing Order...":"Place Order"}),e.jsx("p",{className:"text-xs text-gray-500 mt-4 text-center",children:"By placing this order, you agree to our terms and conditions."})]})]})]})})}const bi=()=>{const[t,s]=f.useState(""),[r,a]=f.useState(""),[l,n]=f.useState(!1),{signIn:i,error:c,isLoading:o}=J(),d=Oe(),m=js(),[h,p]=f.useState("");m.state?.from?.pathname;const x=async g=>{g.preventDefault(),p("");try{await i(t,r),d("/dashboard")}catch(u){p(u.message)}};return e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-green-50 to-green-100 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"max-w-md w-full space-y-8",children:[e.jsxs("div",{children:[e.jsx("div",{className:"flex justify-center",children:e.jsx("div",{className:"w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center",children:e.jsx("span",{className:"text-white font-bold text-2xl",children:"🌿"})})}),e.jsx("h2",{className:"mt-6 text-center text-3xl font-bold text-gray-900",children:"Welcome back"}),e.jsxs("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Don't have an account?"," ",e.jsx(R,{to:"/register",className:"font-medium text-green-600 hover:text-green-500",children:"Sign up"})]})]}),e.jsxs("form",{className:"mt-8 space-y-6",onSubmit:x,children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"email",className:"sr-only",children:"Email address"}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:e.jsx(Ie,{className:"h-5 w-5 text-gray-400"})}),e.jsx("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:t,onChange:g=>s(g.target.value),className:"appearance-none relative block w-full pl-10 pr-3 py-3 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-lg focus:outline-none focus:ring-green-500 focus:border-green-500 focus:z-10 sm:text-sm",placeholder:"Email address"})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"password",className:"sr-only",children:"Password"}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:e.jsx(Os,{className:"h-5 w-5 text-gray-400"})}),e.jsx("input",{id:"password",name:"password",type:l?"text":"password",autoComplete:"current-password",required:!0,value:r,onChange:g=>a(g.target.value),className:"appearance-none relative block w-full pl-10 pr-10 py-3 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-lg focus:outline-none focus:ring-green-500 focus:border-green-500 focus:z-10 sm:text-sm",placeholder:"Password"}),e.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>n(!l),children:l?e.jsx(hs,{className:"h-5 w-5 text-gray-400"}):e.jsx(ne,{className:"h-5 w-5 text-gray-400"})})]})]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("input",{id:"remember-me",name:"remember-me",type:"checkbox",className:"h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"}),e.jsx("label",{htmlFor:"remember-me",className:"ml-2 block text-sm text-gray-900",children:"Remember me"})]}),e.jsx("div",{className:"text-sm",children:e.jsx(R,{to:"/forgot-password",className:"font-medium text-green-600 hover:text-green-500",children:"Forgot your password?"})})]}),(h||c)&&e.jsx("div",{className:"text-red-500 text-sm text-center",children:h||c}),e.jsx("div",{children:e.jsx("button",{type:"submit",disabled:o,className:"group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed",children:o?"Signing in...":"Sign in"})})]})]})})},yi=async(t,s)=>{try{return console.log("Sending OTP email to:",t),console.log("OTP Code:",s),await new Promise(r=>setTimeout(r,1e3)),!0}catch(r){return console.error("Error sending OTP email:",r),!1}},Gr=new Map,ji=(t,s=3,r=3e5)=>{const a=Date.now(),l=t.toLowerCase(),n=Gr.get(l);return!n||a>n.resetTime?(Gr.set(l,{count:1,resetTime:a+r}),!0):n.count>=s?!1:(n.count++,!0)},Ye={provider:"msg91",apiKey:"demo_key",senderId:"STARTJC"},kr={english:`🌿 Start Juicce OTP: {{OTP_CODE}}

Use this code to verify your mobile number. Valid for 10 minutes.

Never share this OTP with anyone.

- Start Juicce Team`,hindi:`🌿 Start Juicce OTP: {{OTP_CODE}}

अपना मोबाइल नंबर सत्यापित करने के लिए इस कोड का उपयोग करें। 10 मिनट के लिए वैध।

इस OTP को किसी के साथ साझा न करें।

- Start Juicce Team`},wi=async(t,s)=>{try{return console.log("Twilio would send SMS to:",`+91${t}`,"with OTP:",s),!0}catch(r){return console.error("Error sending SMS via Twilio:",r),!1}},Ni=async(t,s)=>{try{if(!Ye.apiKey||Ye.apiKey==="demo_key")return console.log("MSG91 API key not configured, using demo mode"),!1;const r=kr.english.replace("{{OTP_CODE}}",s);return console.log("MSG91 SMS would be sent to:",t,"with OTP:",s),console.log("Note: MSG91 API disabled due to CORS. Use server-side proxy in production."),!1}catch(r){return console.error("Error sending SMS via MSG91:",r),!1}},vi=async(t,s)=>{try{const r=kr.english.replace("{{OTP_CODE}}",s),a=new URLSearchParams({apikey:Ye.apiKey,numbers:`91${t}`,message:r,sender:Ye.senderId||"TXTLCL"});return(await(await fetch("https://api.textlocal.in/send/",{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:a})).json()).status==="success"}catch(r){return console.error("Error sending SMS via TextLocal:",r),!1}},_i=async(t,s)=>{try{const r=kr.english.replace("{{OTP_CODE}}",s);return(await(await fetch("https://www.fast2sms.com/dev/bulkV2",{method:"POST",headers:{authorization:Ye.apiKey,"Content-Type":"application/json"},body:JSON.stringify({route:"v3",sender_id:Ye.senderId,message:r,language:"english",flash:0,numbers:t})})).json()).return===!0}catch(r){return console.error("Error sending SMS via Fast2SMS:",r),!1}},ki=async(t,s)=>{try{return console.log("AWS SNS would send SMS to:",`+91${t}`,"with OTP:",s),!0}catch(r){return console.error("Error sending SMS via AWS SNS:",r),!1}},Si=async(t,s)=>{try{if(!Ci(t))throw new Error("Invalid mobile number format");switch(Ye.provider){case"twilio":return await wi(t,s);case"msg91":return await Ni(t,s);case"textlocal":return await vi(t,s);case"fast2sms":return await _i(t,s);case"aws-sns":return await ki(t,s);default:throw new Error("No SMS provider configured")}}catch(r){return console.error("Error in sendOTPSMS:",r),!1}},Ci=t=>/^[6-9]\d{9}$/.test(t),Hr=new Map,Ei=(t,s=3,r=3e5)=>{const a=Date.now(),l=t,n=Hr.get(l);return!n||a>n.resetTime?(Hr.set(l,{count:1,resetTime:a+r}),!0):n.count>=s?!1:(n.count++,!0)},Pi=()=>Math.floor(1e5+Math.random()*9e5).toString(),Ri=async(t,s)=>{try{return ji(t)?await yi(t,s)?(k.success(`Demo OTP sent to ${t}: ${s}`,{duration:15e3,style:{background:"#10B981",color:"white"}}),!0):(console.log(`OTP for ${t}: ${s}`),k.success(`Demo OTP for ${t}: ${s}`,{duration:15e3,style:{background:"#10B981",color:"white"}}),!0):(k.error("Too many OTP requests. Please try again later."),!1)}catch(r){return console.error("Error sending email OTP:",r),!1}},Ai=async(t,s)=>{try{return Ei(t)?await Si(t,s)?(k.success(`Demo SMS OTP sent to +91${t}: ${s}`,{duration:15e3,style:{background:"#10B981",color:"white"}}),!0):(console.log(`SMS OTP for +91${t}: ${s}`),k.success(`Demo SMS OTP for +91${t}: ${s}`,{duration:15e3,style:{background:"#10B981",color:"white"}}),!0):(k.error("Too many SMS OTP requests. Please try again later."),!1)}catch(r){return console.error("Error sending SMS OTP:",r),!1}},Ti=async(t,s)=>{try{const r=Pi(),a=new Date(Date.now()+10*60*1e3);await y.from("otp_verifications").delete().eq("contact",t).eq("contact_type",s);const{error:l}=await y.from("otp_verifications").insert({contact:t,contact_type:s,otp_code:r,expires_at:a.toISOString(),verified:!1,attempts:0});if(l)return console.error("Error storing OTP:",l),!1;let n=!1;return s==="email"?n=await Ri(t,r):s==="mobile"&&(n=await Ai(t,r)),n?(k.success(`OTP sent to your ${s}`),!0):(k.error("Failed to send OTP. Please try again."),!1)}catch(r){return console.error("Error in sendOTP:",r),k.error("Failed to send OTP. Please try again."),!1}},Di=async(t,s,r)=>{try{const{data:a,error:l}=await y.from("otp_verifications").select("*").eq("contact",t).eq("contact_type",s).eq("verified",!1).order("created_at",{ascending:!1}).limit(1).maybeSingle();if(l&&l.code!=="PGRST116")return console.error("Error fetching OTP record:",l),k.error("Database error. Please try again."),!1;if(!a)return k.error("No valid OTP found. Please request a new one."),!1;const n=new Date,i=new Date(a.expires_at);if(n>i)return k.error("OTP has expired. Please request a new one."),!1;if(a.attempts>=3)return k.error("Too many failed attempts. Please request a new OTP."),!1;if(a.otp_code===r){const{error:c}=await y.from("otp_verifications").update({verified:!0,attempts:a.attempts+1}).eq("id",a.id);return c?(console.error("Error updating OTP record:",c),!1):(k.success("OTP verified successfully!"),!0)}else{await y.from("otp_verifications").update({attempts:a.attempts+1}).eq("id",a.id);const c=3-(a.attempts+1);return c>0?k.error(`Invalid OTP. ${c} attempts remaining.`):k.error("Invalid OTP. Too many failed attempts. Please request a new OTP."),!1}}catch(a){return console.error("Error in verifyOTP:",a),k.error("Failed to verify OTP. Please try again."),!1}},Kr=async(t,s)=>{try{const{data:r,error:a}=await y.from("otp_verifications").select("verified").eq("contact",t).eq("contact_type",s).eq("verified",!0).order("created_at",{ascending:!1}).limit(1).maybeSingle();return a&&a.code!=="PGRST116"?(console.error("Error checking contact verification:",a),!1):r?.verified===!0}catch(r){return console.error("Error in isContactVerified:",r),!1}},Fi=()=>{const[t,s]=f.useState(""),[r,a]=f.useState(!1),[l,n]=f.useState(null);f.useEffect(()=>{i()},[]);const i=()=>{const d=new URLSearchParams(window.location.search),m=d.get("ref")||d.get("referral");m&&(s(m.toUpperCase()),setTimeout(()=>{c()},500))},c=async()=>{if(!t.trim()){n(null);return}a(!0),n(null);try{const{data:d,error:m}=await y.from("users").select("id, full_name, is_premium, premium_lifetime_access").eq("referral_code",t.toUpperCase()).single();if(m||!d){n({isValid:!1,error:"Invalid referral code. Please check and try again."});return}if(!(d.is_premium&&d.premium_lifetime_access)){n({isValid:!1,error:"This referral code is from a non-premium user. Only premium users can refer others."});return}const{data:p}=await y.from("admin_settings").select("setting_value").eq("setting_key","referral_bonus_amount").single(),x=parseFloat(p?.setting_value||"100");n({isValid:!0,referrerName:d.full_name,bonusAmount:x}),k.success(`Valid referral code! You'll receive ₹${x} bonus.`)}catch(d){console.error("Error validating referral code:",d),n({isValid:!1,error:"Error validating referral code. Please try again."})}finally{a(!1)}};return{referralCode:t,setReferralCode:s,validatingReferral:r,referralValidation:l,validateReferralCode:c,processReferralOnRegistration:async(d,m)=>{if(!(!t.trim()||!l?.isValid))try{console.log("Multi-level referral processing on registration:",{newUserId:d,newUserEmail:m,referralCode:t});let h=m;if(!h){const{data:g}=await y.from("users").select("email, is_premium").eq("id",d).single();h=g?.email}if(!h){console.error("Could not get user email for referral processing");return}const{data:p}=await y.from("users").select("is_premium").eq("id",d).single();if(!p?.is_premium){await y.from("users").update({referred_by:t}).eq("id",d),k.info("Referral code saved! Bonuses will be processed when you upgrade to premium.");return}const x=await Fe.processMultiLevelReferral(d,h,t);x.success?(k.success(`Welcome! Multi-level referral bonuses distributed: ₹${x.totalBonusDistributed} across ${x.levelsProcessed} levels`,{duration:6e3}),await y.from("users").update({referred_by:t}).eq("id",d)):(console.error("Multi-level referral processing failed:",x.errors),await y.from("users").update({referred_by:t}).eq("id",d),k.info("Referral code saved! There was an issue processing bonuses, but they may be processed later."))}catch(h){console.error("Error processing referral on registration:",h);try{await y.from("users").update({referred_by:t}).eq("id",d),k.info("Referral code saved! Bonuses will be processed when you upgrade to premium.")}catch(p){console.error("Failed to save referral code:",p)}}},extractReferralFromUrl:i}},Rs=5,Ii=["State Bank of India (SBI)","HDFC Bank","ICICI Bank","Axis Bank","Punjab National Bank (PNB)","Bank of Baroda","Canara Bank","Union Bank of India","Bank of India","Indian Bank","Central Bank of India","Indian Overseas Bank","UCO Bank","Bank of Maharashtra","Punjab & Sind Bank"],Oi=()=>{const[t,s]=f.useState(1),[r,a]=f.useState({contactMethod:"mobile",mobile:"",email:"",otp:"",isContactVerified:!1,referralCode:"",isReferralVerified:!1,aadhaarFront:null,aadhaarBack:null,panCard:null,aadhaarFrontUrl:"",aadhaarBackUrl:"",panCardUrl:"",accountNumber:"",ifscCode:"",accountHolderName:"",bankName:"",firstName:"",lastName:"",username:"",password:"",confirmPassword:""}),[l,n]=f.useState(!1),[i,c]=f.useState(!1),[o,d]=f.useState(!1),[m,h]=f.useState(0),[p,x]=f.useState(!0),{register:g,isLoading:u}=J(),j=Oe(),{referralCode:b,setReferralCode:N,validatingReferral:P,referralValidation:A,validateReferralCode:E,processReferralOnRegistration:_}=Fi();f.useEffect(()=>{let D;return m>0?D=setInterval(()=>{h(U=>U-1)},1e3):m===0&&!p&&x(!0),()=>clearInterval(D)},[m,p]),f.useEffect(()=>{const D=localStorage.getItem("kycFormData");if(D)try{const U=JSON.parse(D);a(q=>({...q,...U}))}catch(U){console.error("Error loading saved form data:",U)}},[]),f.useEffect(()=>{(async()=>{r.contactMethod==="mobile"&&r.mobile&&w(r.mobile)?await Kr(r.mobile,"mobile")&&a(q=>({...q,isContactVerified:!0})):r.contactMethod==="email"&&r.email&&S(r.email)&&await Kr(r.email,"email")&&a(q=>({...q,isContactVerified:!0}))})()},[r.contactMethod,r.mobile,r.email]),f.useEffect(()=>{localStorage.setItem("kycFormData",JSON.stringify(r))},[r]);const w=D=>/^[6-9]\d{9}$/.test(D),S=D=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(D),C=D=>/^[A-Z]{4}0[A-Z0-9]{6}$/.test(D.toUpperCase()),T=D=>["image/jpeg","image/jpg","image/png","application/pdf"].includes(D.type)?D.size>5242880?(k.error("File size must be less than 5MB"),!1):!0:(k.error("Only JPG, PNG, and PDF files are allowed"),!1),F=async(D,U)=>{try{const q=await Ti(D,U);return q&&(h(60),x(!1)),q}catch{return k.error("Failed to send OTP. Please try again."),!1}},I=async(D,U,q)=>{try{return await Di(D,U,q)}catch{return k.error("Failed to verify OTP. Please try again."),!1}},v=async(D,U)=>{try{return await new Promise(q=>setTimeout(q,2e3)),`https://mock-storage.com/${U}_${Date.now()}.${D.name.split(".").pop()}`}catch{throw new Error("File upload failed")}},$=async D=>{try{await new Promise(fe=>setTimeout(fe,500));const U={SBIN:"State Bank of India (SBI)",HDFC:"HDFC Bank",ICIC:"ICICI Bank",UTIB:"Axis Bank",PUNB:"Punjab National Bank (PNB)"},q=D.substring(0,4);return U[q]||null}catch{return null}},O=D=>{const{name:U,value:q}=D.target;a(fe=>({...fe,[U]:q}))},z=(D,U)=>{const q=D.target.files?.[0];q&&T(q)&&a(fe=>({...fe,[U]:q}))},G=async()=>{const D=r.contactMethod==="mobile"?r.mobile:r.email;if(!(r.contactMethod==="mobile"?w(r.mobile):S(r.email))){k.error(`Please enter a valid ${r.contactMethod}`);return}await F(D,r.contactMethod)},ee=async()=>{if(!r.otp||r.otp.length!==6){k.error("Please enter a valid 6-digit OTP");return}const D=r.contactMethod==="mobile"?r.mobile:r.email;await I(D,r.contactMethod,r.otp)&&a(q=>({...q,isContactVerified:!0}))},Q=async()=>{if(!b.trim()){k.error("Please enter a referral code");return}await E(),A?.isValid&&a(D=>({...D,isReferralVerified:!0}))},ve=async()=>{if(!r.aadhaarFront||!r.aadhaarBack||!r.panCard){k.error("Please upload all required documents");return}d(!0);try{const[D,U,q]=await Promise.all([v(r.aadhaarFront,"aadhaar_front"),v(r.aadhaarBack,"aadhaar_back"),v(r.panCard,"pan_card")]);a(fe=>({...fe,aadhaarFrontUrl:D,aadhaarBackUrl:U,panCardUrl:q})),k.success("Documents uploaded successfully!"),s(4)}catch{k.error("Failed to upload documents. Please try again.")}finally{d(!1)}},Rt=async D=>{const U=D.target.value.toUpperCase();if(a(q=>({...q,ifscCode:U})),C(U)){const q=await $(U);q&&a(fe=>({...fe,bankName:q}))}},At=()=>{t<Rs&&s(t+1)},Tt=()=>{t>1&&s(t-1)},Dt=async()=>{if(!r.firstName||!r.lastName||!r.username||!r.password){k.error("Please fill in all required fields");return}if(r.password!==r.confirmPassword){k.error("Passwords do not match");return}if(r.password.length<8){k.error("Password must be at least 8 characters long");return}try{const D={firstName:r.firstName,lastName:r.lastName,username:r.username,email:r.contactMethod==="email"?r.email:`${r.mobile}@temp.com`,password:r.password,referralCode:b,mobile:r.mobile,kycData:{aadhaarFrontUrl:r.aadhaarFrontUrl,aadhaarBackUrl:r.aadhaarBackUrl,panCardUrl:r.panCardUrl,accountNumber:r.accountNumber,ifscCode:r.ifscCode,accountHolderName:r.accountHolderName,bankName:r.bankName}};console.log("Registration data:",D);const U=await g(D);if(U.success){if(A?.isValid&&U.user?.id){const q=r.contactMethod==="email"?r.email:`${r.mobile}@temp.com`;await _(U.user.id,q)}localStorage.removeItem("kycFormData"),k.success("Registration successful! Welcome to Start Juicce!"),j("/")}else k.error(U.error||"Registration failed")}catch{k.error("Registration failed. Please try again.")}},ks=()=>{switch(t){case 1:return r.isContactVerified;case 2:return A?.isValid||!1;case 3:return!!(r.aadhaarFrontUrl&&r.aadhaarBackUrl&&r.panCardUrl);case 4:return!!(r.accountNumber&&r.ifscCode&&r.accountHolderName&&r.bankName);case 5:return!!(r.firstName&&r.lastName&&r.username&&r.password&&r.confirmPassword);default:return!1}},Ft=()=>{switch(t){case 1:return"Verify Contact";case 2:return"Referral Code";case 3:return"Upload Documents";case 4:return"Banking Details";case 5:return"Personal Information";default:return""}};return e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-green-50 to-green-100 py-8 px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"max-w-2xl mx-auto",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx("div",{className:"flex justify-center mb-4",children:e.jsx("div",{className:"w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center",children:e.jsx("span",{className:"text-white font-bold text-2xl",children:"🌿"})})}),e.jsx("h1",{className:"text-2xl sm:text-3xl font-bold text-gray-900 mb-2",children:"Join Start Juicce"}),e.jsx("p",{className:"text-sm sm:text-base text-gray-600 mb-4",children:"Complete KYC verification to get started"}),e.jsx("div",{className:"flex items-center justify-center space-x-2 sm:space-x-4 mb-6",children:Array.from({length:Rs},(D,U)=>e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:`w-8 h-8 sm:w-10 sm:h-10 rounded-full flex items-center justify-center text-sm font-medium ${U+1<t?"bg-green-600 text-white":U+1===t?"bg-green-100 text-green-600 border-2 border-green-600":"bg-gray-200 text-gray-500"}`,children:U+1<t?e.jsx(Us,{className:"w-4 h-4"}):U+1}),U<Rs-1&&e.jsx("div",{className:`w-8 sm:w-12 h-1 mx-1 ${U+1<t?"bg-green-600":"bg-gray-200"}`})]},U))}),e.jsxs("h2",{className:"text-lg sm:text-xl font-semibold text-gray-800",children:["Step ",t,": ",Ft()]})]}),e.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-6 sm:p-8",children:[t===1&&e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"Choose verification method"}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("button",{type:"button",onClick:()=>a(D=>({...D,contactMethod:"mobile"})),className:`p-4 border-2 rounded-lg flex items-center justify-center space-x-2 transition-colors ${r.contactMethod==="mobile"?"border-green-600 bg-green-50 text-green-700":"border-gray-300 hover:border-gray-400"}`,children:[e.jsx(Bs,{className:"w-5 h-5"}),e.jsx("span",{className:"font-medium",children:"Mobile"})]}),e.jsxs("button",{type:"button",onClick:()=>a(D=>({...D,contactMethod:"email"})),className:`p-4 border-2 rounded-lg flex items-center justify-center space-x-2 transition-colors ${r.contactMethod==="email"?"border-green-600 bg-green-50 text-green-700":"border-gray-300 hover:border-gray-400"}`,children:[e.jsx(Ie,{className:"w-5 h-5"}),e.jsx("span",{className:"font-medium",children:"Email"})]})]})]}),r.contactMethod==="mobile"?e.jsxs("div",{children:[e.jsx("label",{htmlFor:"mobile",className:"block text-sm font-medium text-gray-700 mb-2",children:"Mobile Number"}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:e.jsx("span",{className:"text-gray-500 text-sm",children:"+91"})}),e.jsx("input",{id:"mobile",name:"mobile",type:"tel",value:r.mobile,onChange:O,className:"block w-full pl-12 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 text-base touch-target",placeholder:"Enter 10-digit mobile number",maxLength:10})]})]}):e.jsxs("div",{children:[e.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address"}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:e.jsx(Ie,{className:"h-5 w-5 text-gray-400"})}),e.jsx("input",{id:"email",name:"email",type:"email",value:r.email,onChange:O,className:"block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 text-base touch-target",placeholder:"Enter your email address"})]})]}),e.jsx("button",{type:"button",onClick:G,disabled:m>0||r.isContactVerified,className:"w-full bg-green-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors touch-target",children:r.isContactVerified?"Contact Verified":m>0?`Resend OTP in ${m}s`:"Send OTP"}),(m>0||!p)&&!r.isContactVerified&&e.jsxs("div",{children:[e.jsx("label",{htmlFor:"otp",className:"block text-sm font-medium text-gray-700 mb-2",children:"Enter OTP"}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsx("input",{id:"otp",name:"otp",type:"text",value:r.otp,onChange:O,className:"flex-1 px-3 py-3 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 text-center text-lg font-mono touch-target",placeholder:"000000",maxLength:6}),e.jsx("button",{type:"button",onClick:ee,disabled:r.otp.length!==6,className:"px-6 py-3 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors touch-target",children:"Verify"})]}),e.jsx("p",{className:"text-sm text-gray-600 mt-2",children:"Check the green notification above for your demo OTP code"})]}),r.isContactVerified&&e.jsxs("div",{className:"flex items-center space-x-2 text-green-600 bg-green-50 p-3 rounded-lg",children:[e.jsx(V,{className:"w-5 h-5"}),e.jsx("span",{className:"font-medium",children:"Contact verified successfully!"})]})]}),t===2&&e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"text-center",children:[e.jsx(H,{className:"w-16 h-16 text-green-600 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Referral Code Required"}),e.jsx("p",{className:"text-gray-600",children:"Enter the referral code you received to continue registration"})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"referralCode",className:"block text-sm font-medium text-gray-700 mb-2",children:"Referral Code *"}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:e.jsx(H,{className:"h-5 w-5 text-gray-400"})}),e.jsx("input",{id:"referralCode",name:"referralCode",type:"text",value:b,onChange:D=>N(D.target.value.toUpperCase()),className:"block w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 text-base touch-target",placeholder:"Enter referral code"}),P&&e.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center",children:e.jsx(le,{className:"h-5 w-5 text-gray-400 animate-spin"})}),A?.isValid&&e.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center",children:e.jsx(V,{className:"h-5 w-5 text-green-600"})})]})]}),e.jsx("button",{type:"button",onClick:Q,disabled:!b.trim()||P||A?.isValid,className:"w-full bg-green-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors touch-target",children:P?"Verifying...":A?.isValid?"Verified":"Verify Referral Code"}),r.isReferralVerified&&e.jsxs("div",{className:"flex items-center space-x-2 text-green-600 bg-green-50 p-3 rounded-lg",children:[e.jsx(V,{className:"w-5 h-5"}),e.jsx("span",{className:"font-medium",children:"Referral code verified successfully!"})]}),e.jsx("div",{className:"text-center",children:e.jsxs("p",{className:"text-sm text-gray-600",children:["Don't have a referral code?"," ",e.jsx(R,{to:"/contact",className:"text-green-600 hover:text-green-700 font-medium",children:"Contact us"})]})})]}),t===3&&e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"text-center",children:[e.jsx(ae,{className:"w-16 h-16 text-green-600 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Upload KYC Documents"}),e.jsx("p",{className:"text-gray-600",children:"Please upload clear photos of your documents for verification"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Aadhaar Card (Front) *"}),e.jsxs("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-green-400 transition-colors",children:[r.aadhaarFront?e.jsxs("div",{className:"space-y-2",children:[e.jsx(he,{className:"w-8 h-8 text-green-600 mx-auto"}),e.jsx("p",{className:"text-sm text-gray-600",children:r.aadhaarFront.name}),e.jsx("button",{type:"button",onClick:()=>a(D=>({...D,aadhaarFront:null})),className:"text-red-600 hover:text-red-700 text-sm",children:"Remove"})]}):e.jsxs("div",{className:"space-y-2",children:[e.jsx(qe,{className:"w-8 h-8 text-gray-400 mx-auto"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Click to upload or drag and drop"}),e.jsx("p",{className:"text-xs text-gray-500",children:"JPG, PNG, PDF (max 5MB)"})]}),e.jsx("input",{type:"file",accept:".jpg,.jpeg,.png,.pdf",onChange:D=>z(D,"aadhaarFront"),className:"absolute inset-0 w-full h-full opacity-0 cursor-pointer"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Aadhaar Card (Back) *"}),e.jsxs("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-green-400 transition-colors relative",children:[r.aadhaarBack?e.jsxs("div",{className:"space-y-2",children:[e.jsx(he,{className:"w-8 h-8 text-green-600 mx-auto"}),e.jsx("p",{className:"text-sm text-gray-600",children:r.aadhaarBack.name}),e.jsx("button",{type:"button",onClick:()=>a(D=>({...D,aadhaarBack:null})),className:"text-red-600 hover:text-red-700 text-sm",children:"Remove"})]}):e.jsxs("div",{className:"space-y-2",children:[e.jsx(qe,{className:"w-8 h-8 text-gray-400 mx-auto"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Click to upload or drag and drop"}),e.jsx("p",{className:"text-xs text-gray-500",children:"JPG, PNG, PDF (max 5MB)"})]}),e.jsx("input",{type:"file",accept:".jpg,.jpeg,.png,.pdf",onChange:D=>z(D,"aadhaarBack"),className:"absolute inset-0 w-full h-full opacity-0 cursor-pointer"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"PAN Card *"}),e.jsxs("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-green-400 transition-colors relative",children:[r.panCard?e.jsxs("div",{className:"space-y-2",children:[e.jsx(he,{className:"w-8 h-8 text-green-600 mx-auto"}),e.jsx("p",{className:"text-sm text-gray-600",children:r.panCard.name}),e.jsx("button",{type:"button",onClick:()=>a(D=>({...D,panCard:null})),className:"text-red-600 hover:text-red-700 text-sm",children:"Remove"})]}):e.jsxs("div",{className:"space-y-2",children:[e.jsx(qe,{className:"w-8 h-8 text-gray-400 mx-auto"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Click to upload or drag and drop"}),e.jsx("p",{className:"text-xs text-gray-500",children:"JPG, PNG, PDF (max 5MB)"})]}),e.jsx("input",{type:"file",accept:".jpg,.jpeg,.png,.pdf",onChange:D=>z(D,"panCard"),className:"absolute inset-0 w-full h-full opacity-0 cursor-pointer"})]})]}),e.jsx("button",{type:"button",onClick:ve,disabled:!r.aadhaarFront||!r.aadhaarBack||!r.panCard||o,className:"w-full bg-green-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors touch-target",children:o?"Uploading Documents...":"Upload Documents"})]}),t===4&&e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"text-center",children:[e.jsx(ea,{className:"w-16 h-16 text-green-600 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Banking Information"}),e.jsx("p",{className:"text-gray-600",children:"Enter your Indian bank account details for transactions"})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"accountNumber",className:"block text-sm font-medium text-gray-700 mb-2",children:"Account Number *"}),e.jsx("input",{id:"accountNumber",name:"accountNumber",type:"text",value:r.accountNumber,onChange:O,className:"block w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 text-base touch-target",placeholder:"Enter 9-18 digit account number",maxLength:18})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"ifscCode",className:"block text-sm font-medium text-gray-700 mb-2",children:"IFSC Code *"}),e.jsx("input",{id:"ifscCode",name:"ifscCode",type:"text",value:r.ifscCode,onChange:Rt,className:"block w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 text-base touch-target uppercase",placeholder:"e.g., SBIN0001234",maxLength:11})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"accountHolderName",className:"block text-sm font-medium text-gray-700 mb-2",children:"Account Holder Name *"}),e.jsx("input",{id:"accountHolderName",name:"accountHolderName",type:"text",value:r.accountHolderName,onChange:O,className:"block w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 text-base touch-target",placeholder:"Enter name as per bank records"})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"bankName",className:"block text-sm font-medium text-gray-700 mb-2",children:"Bank Name *"}),e.jsxs("select",{id:"bankName",name:"bankName",value:r.bankName,onChange:O,className:"block w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 text-base touch-target",children:[e.jsx("option",{value:"",children:"Select your bank"}),Ii.map(D=>e.jsx("option",{value:D,children:D},D))]})]}),e.jsx("div",{className:"bg-blue-50 p-4 rounded-lg",children:e.jsxs("p",{className:"text-sm text-blue-800",children:[e.jsx("strong",{children:"Note:"})," Your banking information is encrypted and securely stored. This information is required for processing payments and withdrawals."]})})]}),t===5&&e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"text-center",children:[e.jsx(Ee,{className:"w-16 h-16 text-green-600 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Personal Information"}),e.jsx("p",{className:"text-gray-600",children:"Complete your profile to finish registration"})]}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"firstName",className:"block text-sm font-medium text-gray-700 mb-2",children:"First Name *"}),e.jsx("input",{id:"firstName",name:"firstName",type:"text",value:r.firstName,onChange:O,className:"block w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 text-base touch-target",placeholder:"Enter first name"})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"lastName",className:"block text-sm font-medium text-gray-700 mb-2",children:"Last Name *"}),e.jsx("input",{id:"lastName",name:"lastName",type:"text",value:r.lastName,onChange:O,className:"block w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 text-base touch-target",placeholder:"Enter last name"})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"username",className:"block text-sm font-medium text-gray-700 mb-2",children:"Username *"}),e.jsx("input",{id:"username",name:"username",type:"text",value:r.username,onChange:O,className:"block w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 text-base touch-target",placeholder:"Choose a unique username"})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-2",children:"Password *"}),e.jsxs("div",{className:"relative",children:[e.jsx("input",{id:"password",name:"password",type:l?"text":"password",value:r.password,onChange:O,className:"block w-full px-3 py-3 pr-10 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 text-base touch-target",placeholder:"Create a strong password"}),e.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>n(!l),children:l?e.jsx(hs,{className:"h-5 w-5 text-gray-400"}):e.jsx(ne,{className:"h-5 w-5 text-gray-400"})})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 mb-2",children:"Confirm Password *"}),e.jsxs("div",{className:"relative",children:[e.jsx("input",{id:"confirmPassword",name:"confirmPassword",type:i?"text":"password",value:r.confirmPassword,onChange:O,className:"block w-full px-3 py-3 pr-10 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 text-base touch-target",placeholder:"Confirm your password"}),e.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>c(!i),children:i?e.jsx(hs,{className:"h-5 w-5 text-gray-400"}):e.jsx(ne,{className:"h-5 w-5 text-gray-400"})})]})]}),e.jsx("div",{className:"bg-green-50 p-4 rounded-lg",children:e.jsxs("p",{className:"text-sm text-green-800",children:[e.jsx("strong",{children:"Almost done!"})," Review your information and complete registration."]})})]})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-center mt-8 space-y-4 sm:space-y-0 sm:space-x-4",children:[e.jsxs("button",{type:"button",onClick:Tt,disabled:t===1,className:`flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-colors touch-target ${t===1?"bg-gray-100 text-gray-400 cursor-not-allowed":"bg-gray-200 text-gray-700 hover:bg-gray-300"}`,children:[e.jsx(zs,{className:"w-4 h-4"}),e.jsx("span",{children:"Previous"})]}),t<Rs?e.jsxs("button",{type:"button",onClick:At,disabled:!ks(),className:`flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-colors touch-target ${ks()?"bg-green-600 text-white hover:bg-green-700":"bg-gray-300 text-gray-500 cursor-not-allowed"}`,children:[e.jsx("span",{children:"Next"}),e.jsx(Se,{className:"w-4 h-4"})]}):e.jsx("button",{type:"button",onClick:Dt,disabled:!ks()||u,className:`flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-colors touch-target ${ks()&&!u?"bg-green-600 text-white hover:bg-green-700":"bg-gray-300 text-gray-500 cursor-not-allowed"}`,children:u?e.jsxs(e.Fragment,{children:[e.jsx(le,{className:"w-4 h-4 animate-spin"}),e.jsx("span",{children:"Creating Account..."})]}):e.jsxs(e.Fragment,{children:[e.jsx("span",{children:"Complete Registration"}),e.jsx(V,{className:"w-4 h-4"})]})})]}),e.jsx("div",{className:"text-center mt-8",children:e.jsxs("p",{className:"text-sm text-gray-600",children:["Already have an account?"," ",e.jsx(R,{to:"/login",className:"font-medium text-green-600 hover:text-green-500",children:"Sign in here"})]})})]})})},Ui=()=>{const[t,s]=f.useState(""),[r,a]=f.useState(!1),[l,n]=f.useState(!1),[i,c]=f.useState(""),{requestPasswordReset:o}=J(),d=async m=>{m.preventDefault(),c(""),a(!0);try{const h=await o(t);h.success?n(!0):c(h.message)}catch{c("An unexpected error occurred. Please try again.")}finally{a(!1)}};return l?e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-green-50 to-emerald-100 flex items-center justify-center p-4",children:e.jsx(Re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-white rounded-2xl shadow-xl p-8 w-full max-w-md",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-6",children:e.jsx(V,{className:"w-8 h-8 text-green-600"})}),e.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Check Your Email"}),e.jsxs("p",{className:"text-gray-600 mb-6 leading-relaxed",children:["We've sent a password reset link to ",e.jsx("strong",{children:t}),". Please check your email and follow the instructions to reset your password."]}),e.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6",children:e.jsxs("p",{className:"text-sm text-blue-800",children:[e.jsx("strong",{children:"Didn't receive the email?"}),e.jsx("br",{}),"• Check your spam/junk folder",e.jsx("br",{}),"• Make sure the email address is correct",e.jsx("br",{}),"• Wait a few minutes and try again"]})}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("button",{onClick:()=>{n(!1),s(""),c("")},className:"w-full bg-green-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-green-700 transition-colors",children:"Send Another Email"}),e.jsx(R,{to:"/login",className:"block w-full text-center text-gray-600 hover:text-gray-800 transition-colors",children:"Back to Login"})]})]})})}):e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-green-50 to-emerald-100 flex items-center justify-center p-4",children:e.jsxs(Re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-white rounded-2xl shadow-xl p-8 w-full max-w-md",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx("div",{className:"mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-6",children:e.jsx(Ie,{className:"w-8 h-8 text-green-600"})}),e.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Forgot Password?"}),e.jsx("p",{className:"text-gray-600",children:"No worries! Enter your email address and we'll send you a link to reset your password."})]}),e.jsxs("form",{onSubmit:d,className:"space-y-6",children:[i&&e.jsxs(Re.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"bg-red-50 border border-red-200 rounded-lg p-4 flex items-start space-x-3",children:[e.jsx(we,{className:"w-5 h-5 text-red-500 mt-0.5 flex-shrink-0"}),e.jsx("p",{className:"text-sm text-red-700",children:i})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address"}),e.jsxs("div",{className:"relative",children:[e.jsx(Ie,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),e.jsx("input",{id:"email",type:"email",value:t,onChange:m=>s(m.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all",placeholder:"Enter your email address",required:!0,disabled:r})]})]}),e.jsx("button",{type:"submit",disabled:r||!t.trim(),className:"w-full bg-green-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all flex items-center justify-center space-x-2",children:r?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"}),e.jsx("span",{children:"Sending..."})]}):e.jsxs(e.Fragment,{children:[e.jsx(sa,{className:"w-5 h-5"}),e.jsx("span",{children:"Send Reset Link"})]})})]}),e.jsx("div",{className:"mt-8 text-center",children:e.jsxs(R,{to:"/login",className:"inline-flex items-center space-x-2 text-gray-600 hover:text-gray-800 transition-colors",children:[e.jsx(zs,{className:"w-4 h-4"}),e.jsx("span",{children:"Back to Login"})]})}),e.jsx("div",{className:"mt-6 bg-gray-50 rounded-lg p-4",children:e.jsxs("p",{className:"text-xs text-gray-600 text-center",children:[e.jsx("strong",{children:"Security Note:"})," For your protection, we'll only send reset instructions to registered email addresses. If you don't receive an email, the address may not be associated with an account."]})})]})})},$i=()=>{const[t]=Qr(),s=Oe();t.get("token");const[r,a]=f.useState(""),[l,n]=f.useState(""),[i,c]=f.useState(!1),[o,d]=f.useState(!1),[m,h]=f.useState(!1),[p,x]=f.useState(!0),[g,u]=f.useState(!1),[j,b]=f.useState(""),[N,P]=f.useState(!1),[A,E]=f.useState(null);J(),f.useEffect(()=>{(async()=>{try{const{data:{session:T},error:F}=await y.auth.getSession();if(F){console.error("Error getting session:",F),b("An error occurred while validating the reset link."),x(!1);return}if(!T){b("Invalid or expired reset link. Please click the password reset link in your email again."),x(!1);return}if(!T.user.email){b("Invalid session. Please request a new password reset."),x(!1);return}const{data:I,error:v}=await y.from("users").select("id, email, full_name").eq("email",T.user.email).maybeSingle();if(v&&v.code!=="PGRST116"){console.error("Error getting user:",v),b("An error occurred while validating the user."),x(!1);return}if(!I){b("User not found. Please contact support."),x(!1);return}P(!0),E(I),x(!1)}catch(T){console.error("Error validating session:",T),b("An error occurred while validating the reset link."),x(!1)}})()},[]);const _=C=>{const T=[];return C.length<8&&T.push("At least 8 characters"),/[A-Z]/.test(C)||T.push("One uppercase letter"),/[a-z]/.test(C)||T.push("One lowercase letter"),/\d/.test(C)||T.push("One number"),T},w=async C=>{if(C.preventDefault(),b(""),r!==l){b("Passwords do not match.");return}const T=_(r);if(T.length>0){b(`Password must contain: ${T.join(", ")}`);return}h(!0);try{const{error:F}=await y.auth.updateUser({password:r});if(F){console.error("Error updating password:",F),b("Failed to update password. Please try again or request a new reset link."),h(!1);return}try{const{data:{session:I}}=await y.auth.getSession();I?.user?.id&&await y.from("password_reset_tokens").update({used:!0,used_at:new Date().toISOString(),updated_at:new Date().toISOString()}).eq("user_id",I.user.id).eq("used",!1)}catch(I){console.error("Error marking tokens as used:",I)}u(!0),await y.auth.signOut(),setTimeout(()=>{s("/login",{state:{message:"Password reset successful. Please log in with your new password."}})},3e3)}catch(F){console.error("Error resetting password:",F),b("An unexpected error occurred. Please try again.")}finally{h(!1)}};if(p)return e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-green-50 to-emerald-100 flex items-center justify-center p-4",children:e.jsxs(Re.div,{initial:{opacity:0},animate:{opacity:1},className:"bg-white rounded-2xl shadow-xl p-8 w-full max-w-md text-center",children:[e.jsx("div",{className:"w-8 h-8 border-2 border-green-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),e.jsx("p",{className:"text-gray-600",children:"Validating reset link..."})]})});if(g)return e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-green-50 to-emerald-100 flex items-center justify-center p-4",children:e.jsxs(Re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-white rounded-2xl shadow-xl p-8 w-full max-w-md text-center",children:[e.jsx("div",{className:"mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-6",children:e.jsx(V,{className:"w-8 h-8 text-green-600"})}),e.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Password Reset Successful!"}),e.jsx("p",{className:"text-gray-600 mb-6",children:"Your password has been successfully updated. You will be redirected to the login page shortly."}),e.jsx(R,{to:"/login",className:"inline-block bg-green-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-green-700 transition-colors",children:"Go to Login"})]})});if(!N)return e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-red-50 to-rose-100 flex items-center justify-center p-4",children:e.jsxs(Re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-white rounded-2xl shadow-xl p-8 w-full max-w-md text-center",children:[e.jsx("div",{className:"mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-6",children:e.jsx(we,{className:"w-8 h-8 text-red-600"})}),e.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Invalid Reset Link"}),e.jsx("p",{className:"text-gray-600 mb-6",children:j||"This password reset link is invalid or has expired. Please request a new one."}),e.jsxs("div",{className:"space-y-3",children:[e.jsx(R,{to:"/forgot-password",className:"block w-full bg-red-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-red-700 transition-colors",children:"Request New Reset Link"}),e.jsx(R,{to:"/login",className:"block w-full text-center text-gray-600 hover:text-gray-800 transition-colors",children:"Back to Login"})]})]})});const S=_(r);return e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-green-50 to-emerald-100 flex items-center justify-center p-4",children:e.jsxs(Re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-white rounded-2xl shadow-xl p-8 w-full max-w-md",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx("div",{className:"mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-6",children:e.jsx(ae,{className:"w-8 h-8 text-green-600"})}),e.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Reset Your Password"}),A&&e.jsxs("p",{className:"text-gray-600",children:["Setting new password for ",e.jsx("strong",{children:A.email})]})]}),e.jsxs("form",{onSubmit:w,className:"space-y-6",children:[j&&e.jsxs(Re.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"bg-red-50 border border-red-200 rounded-lg p-4 flex items-start space-x-3",children:[e.jsx(we,{className:"w-5 h-5 text-red-500 mt-0.5 flex-shrink-0"}),e.jsx("p",{className:"text-sm text-red-700",children:j})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-2",children:"New Password"}),e.jsxs("div",{className:"relative",children:[e.jsx(Os,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),e.jsx("input",{id:"password",type:i?"text":"password",value:r,onChange:C=>a(C.target.value),className:"w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all",placeholder:"Enter new password",required:!0,disabled:m}),e.jsx("button",{type:"button",onClick:()=>c(!i),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:i?e.jsx(hs,{className:"w-5 h-5"}):e.jsx(ne,{className:"w-5 h-5"})})]}),r&&e.jsxs("div",{className:"mt-2 space-y-1",children:[S.map((C,T)=>e.jsxs("div",{className:"flex items-center space-x-2 text-xs",children:[e.jsx("div",{className:"w-2 h-2 rounded-full bg-red-400"}),e.jsx("span",{className:"text-red-600",children:C})]},T)),S.length===0&&e.jsxs("div",{className:"flex items-center space-x-2 text-xs",children:[e.jsx(V,{className:"w-3 h-3 text-green-600"}),e.jsx("span",{className:"text-green-600",children:"Password meets requirements"})]})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 mb-2",children:"Confirm New Password"}),e.jsxs("div",{className:"relative",children:[e.jsx(Os,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),e.jsx("input",{id:"confirmPassword",type:o?"text":"password",value:l,onChange:C=>n(C.target.value),className:"w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all",placeholder:"Confirm new password",required:!0,disabled:m}),e.jsx("button",{type:"button",onClick:()=>d(!o),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:o?e.jsx(hs,{className:"w-5 h-5"}):e.jsx(ne,{className:"w-5 h-5"})})]}),l&&e.jsx("div",{className:"mt-2",children:r===l?e.jsxs("div",{className:"flex items-center space-x-2 text-xs",children:[e.jsx(V,{className:"w-3 h-3 text-green-600"}),e.jsx("span",{className:"text-green-600",children:"Passwords match"})]}):e.jsxs("div",{className:"flex items-center space-x-2 text-xs",children:[e.jsx("div",{className:"w-2 h-2 rounded-full bg-red-400"}),e.jsx("span",{className:"text-red-600",children:"Passwords do not match"})]})})]}),e.jsx("button",{type:"submit",disabled:m||S.length>0||r!==l||!r||!l,className:"w-full bg-green-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all flex items-center justify-center space-x-2",children:m?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"}),e.jsx("span",{children:"Updating Password..."})]}):e.jsxs(e.Fragment,{children:[e.jsx(ae,{className:"w-5 h-5"}),e.jsx("span",{children:"Update Password"})]})})]}),e.jsx("div",{className:"mt-8 text-center",children:e.jsx(R,{to:"/login",className:"text-gray-600 hover:text-gray-800 transition-colors text-sm",children:"Remember your password? Sign in"})})]})})},Mi=()=>{const{user:t,refreshUser:s}=J(),[r,a]=f.useState({walletBalance:0,totalOrders:0,referralCount:0,totalSpent:0,referralEarnings:0}),[l,n]=f.useState([]),[i,c]=f.useState([]),[o,d]=f.useState(!0),[m,h]=f.useState(!1);f.useEffect(()=>{t?.id&&p()},[t?.id]);const p=async()=>{try{d(!0),await Promise.all([x(),g(),u()])}catch(w){console.error("Error loading dashboard data:",w),k.error("Failed to load dashboard data")}finally{d(!1)}},x=async()=>{if(t?.id)try{const{data:w,error:S}=await y.from("users").select("wallet_balance").eq("id",t.id).single();if(S)throw S;const C=w.wallet_balance||0,T=0,{count:F}=await y.from("referrals").select("*",{count:"exact",head:!0}).eq("referrer_id",t.id),{data:I}=await y.from("wallet_transactions").select("amount").eq("user_id",t.id).eq("type","debit"),v=I?.reduce((z,G)=>z+G.amount,0)||0,{data:$}=await y.from("wallet_transactions").select("amount").eq("user_id",t.id).eq("reference_type","referral"),O=$?.reduce((z,G)=>z+G.amount,0)||0;a({walletBalance:C,totalOrders:T,referralCount:F||0,totalSpent:v,referralEarnings:O})}catch(w){console.error("Error loading stats:",w)}},g=async()=>{if(t?.id)try{const{data:w,error:S}=await y.from("wallet_transactions").select("id, type, amount, description, created_at").eq("user_id",t.id).order("created_at",{ascending:!1}).limit(5);if(S)throw S;n(w||[])}catch(w){console.error("Error loading recent transactions:",w)}},u=async()=>{if(t?.id)try{const{data:w,error:S}=await y.from("referrals").select(`
          id,
          referred_user_id,
          bonus_amount,
          status,
          created_at,
          referred_user:users!referrals_referred_user_id_fkey(full_name, email)
        `).eq("referrer_id",t.id).order("created_at",{ascending:!1}).limit(3);if(S)throw S;c(w||[])}catch(w){console.error("Error loading recent referrals:",w)}},j=async()=>{h(!0),await s(),await p(),h(!1),k.success("Dashboard refreshed!")},b=()=>{t?.referral_code&&(navigator.clipboard.writeText(t.referral_code),k.success("Referral code copied to clipboard!"))},N=()=>{if(t?.referral_code){const w=`Join Start Juicce with my referral code: ${t.referral_code}`;navigator.share?navigator.share({title:"Join Start Juicce",text:w,url:window.location.origin}):(navigator.clipboard.writeText(w),k.success("Referral message copied to clipboard!"))}},P=()=>{switch(t?.kyc_status||"pending"){case"approved":return{icon:V,color:"text-green-600",bgColor:"bg-green-100",text:"KYC Approved",description:"Your account is fully verified"};case"under_review":return{icon:Ne,color:"text-yellow-600",bgColor:"bg-yellow-100",text:"Under Review",description:"We're reviewing your documents"};case"rejected":return{icon:Ns,color:"text-red-600",bgColor:"bg-red-100",text:"KYC Rejected",description:"Please resubmit your documents"};default:return{icon:we,color:"text-orange-600",bgColor:"bg-orange-100",text:"KYC Pending",description:"Complete your verification"}}},A=w=>new Date(w).toLocaleDateString("en-IN",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),E=w=>`₹${w.toFixed(2)}`;if(o)return e.jsx("div",{className:"flex items-center justify-center py-12",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"}),e.jsx("p",{className:"text-gray-600",children:"Loading your dashboard..."})]})});const _=P();return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between",children:[e.jsxs("div",{children:[e.jsxs("h1",{className:"text-2xl sm:text-3xl font-bold text-gray-900",children:["Welcome back, ",t?.full_name?.split(" ")[0]||"User","! 👋"]}),e.jsx("p",{className:"text-gray-600 mt-1",children:"Here's what's happening with your Start Juicce account"})]}),e.jsxs("button",{onClick:j,disabled:m,className:"mt-4 sm:mt-0 inline-flex items-center px-4 py-2 bg-white border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors touch-target",children:[e.jsx(le,{className:`w-4 h-4 mr-2 ${m?"animate-spin":""}`}),"Refresh"]})]}),e.jsx("div",{className:`${_.bgColor} rounded-xl p-4`,children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(_.icon,{className:`w-6 h-6 ${_.color}`}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:`font-semibold ${_.color}`,children:_.text}),e.jsx("p",{className:"text-sm text-gray-600",children:_.description})]}),t?.kyc_status!=="approved"&&e.jsx(R,{to:"/dashboard/kyc",className:"bg-white px-4 py-2 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors touch-target",children:"Complete KYC"})]})}),e.jsxs("div",{className:"grid grid-cols-2 lg:grid-cols-4 gap-4",children:[t?.is_premium?e.jsxs(R,{to:"/dashboard/wallet",className:"bg-white rounded-xl shadow-lg p-6 col-span-2 lg:col-span-1 hover:shadow-xl transition-shadow",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("div",{className:"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center",children:e.jsx(K,{className:"w-6 h-6 text-green-600"})}),e.jsx(re,{className:"w-5 h-5 text-green-600"})]}),e.jsx("h3",{className:"text-2xl font-bold text-gray-900",children:E(r.walletBalance)}),e.jsx("p",{className:"text-sm text-gray-600",children:"Wallet Balance"})]}):e.jsxs(R,{to:"/dashboard/premium",className:"bg-gradient-to-br from-yellow-50 to-amber-100 border-2 border-yellow-300 rounded-xl shadow-lg p-6 col-span-2 lg:col-span-1 hover:shadow-xl transition-shadow",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("div",{className:"w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center",children:e.jsx(L,{className:"w-6 h-6 text-yellow-600"})}),e.jsx(re,{className:"w-5 h-5 text-yellow-600"})]}),e.jsx("h3",{className:"text-lg font-bold text-yellow-700",children:"Upgrade to Premium"}),e.jsx("p",{className:"text-sm text-yellow-600",children:"Unlock wallet features"})]}),e.jsxs(R,{to:"/dashboard/orders",className:"bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow",children:[e.jsx("div",{className:"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4",children:e.jsx(Me,{className:"w-6 h-6 text-blue-600"})}),e.jsx("h3",{className:"text-2xl font-bold text-gray-900",children:r.totalOrders}),e.jsx("p",{className:"text-sm text-gray-600",children:"Total Orders"})]}),t?.is_premium?e.jsxs(R,{to:"/dashboard/referrals",className:"bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow",children:[e.jsx("div",{className:"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4",children:e.jsx(H,{className:"w-6 h-6 text-purple-600"})}),e.jsx("h3",{className:"text-2xl font-bold text-gray-900",children:r.referralCount}),e.jsx("p",{className:"text-sm text-gray-600",children:"Referrals"})]}):e.jsxs(R,{to:"/dashboard/premium",className:"bg-gradient-to-br from-purple-50 to-purple-100 border-2 border-purple-300 rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow",children:[e.jsx("div",{className:"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4",children:e.jsx(L,{className:"w-6 h-6 text-purple-600"})}),e.jsx("h3",{className:"text-lg font-bold text-purple-700",children:"Premium Feature"}),e.jsx("p",{className:"text-sm text-purple-600",children:"Unlock referrals"})]}),e.jsxs(R,{to:"/dashboard/premium",className:"bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow",children:[e.jsx("div",{className:"w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mb-4",children:e.jsx(L,{className:"w-6 h-6 text-yellow-600"})}),e.jsx("h3",{className:"text-lg font-bold text-gray-900",children:t?.is_premium?"Premium":"Basic"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Membership"})]})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[e.jsxs("div",{className:"lg:col-span-2 space-y-6",children:[e.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[e.jsx("h2",{className:"text-xl font-bold text-gray-900 mb-6",children:"Quick Actions"}),e.jsxs("div",{className:"grid grid-cols-2 sm:grid-cols-3 gap-4",children:[t?.is_premium?e.jsxs(R,{to:"/dashboard/wallet",className:"flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:border-green-500 hover:bg-green-50 transition-colors touch-target",children:[e.jsx(re,{className:"w-8 h-8 text-green-600 mb-2"}),e.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Add Money"})]}):e.jsxs(R,{to:"/dashboard/premium",className:"flex flex-col items-center p-4 border border-yellow-300 bg-yellow-50 rounded-lg hover:border-yellow-500 hover:bg-yellow-100 transition-colors touch-target",children:[e.jsx(L,{className:"w-8 h-8 text-yellow-600 mb-2"}),e.jsx("span",{className:"text-sm font-medium text-yellow-700",children:"Add Money"}),e.jsx("span",{className:"text-xs text-yellow-600",children:"Premium"})]}),e.jsxs(R,{to:"/products",className:"flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-colors touch-target",children:[e.jsx(Me,{className:"w-8 h-8 text-blue-600 mb-2"}),e.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Place Order"})]}),e.jsxs(R,{to:"/register",className:"flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:border-purple-500 hover:bg-purple-50 transition-colors touch-target",children:[e.jsx(ra,{className:"w-8 h-8 text-purple-600 mb-2"}),e.jsx("span",{className:"text-sm font-medium text-gray-700",children:"View KYC"})]}),t?.is_premium?e.jsxs(R,{to:"/dashboard/referrals",className:"flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:border-yellow-500 hover:bg-yellow-50 transition-colors touch-target",children:[e.jsx(ce,{className:"w-8 h-8 text-yellow-600 mb-2"}),e.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Refer Friends"})]}):e.jsxs(R,{to:"/dashboard/premium",className:"flex flex-col items-center p-4 border border-yellow-300 bg-yellow-50 rounded-lg hover:border-yellow-500 hover:bg-yellow-100 transition-colors touch-target",children:[e.jsx(L,{className:"w-8 h-8 text-yellow-600 mb-2"}),e.jsx("span",{className:"text-sm font-medium text-yellow-700",children:"Refer Friends"}),e.jsx("span",{className:"text-xs text-yellow-600",children:"Premium"})]}),e.jsxs(R,{to:"/dashboard/orders",className:"flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:border-gray-500 hover:bg-gray-50 transition-colors touch-target",children:[e.jsx(et,{className:"w-8 h-8 text-gray-600 mb-2"}),e.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Order History"})]}),e.jsxs(R,{to:"/dashboard/premium",className:"flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:border-green-500 hover:bg-green-50 transition-colors touch-target",children:[e.jsx(Be,{className:"w-8 h-8 text-green-600 mb-2"}),e.jsx("span",{className:"text-sm font-medium text-gray-700",children:t?.is_premium?"Premium Active":"Go Premium"})]})]})]}),t?.is_premium?e.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsx("h2",{className:"text-xl font-bold text-gray-900",children:"Recent Transactions"}),e.jsxs(R,{to:"/dashboard/wallet",className:"text-green-600 hover:text-green-700 font-medium flex items-center space-x-1 touch-target",children:[e.jsx(ne,{className:"w-4 h-4"}),e.jsx("span",{children:"View All"})]})]}),l.length>0?e.jsx("div",{className:"space-y-4",children:l.map(w=>e.jsxs("div",{className:"flex items-center justify-between p-3 border border-gray-200 rounded-lg",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:`w-10 h-10 rounded-full flex items-center justify-center ${w.type==="credit"?"bg-green-100 text-green-600":"bg-red-100 text-red-600"}`,children:w.type==="credit"?e.jsx(re,{className:"w-5 h-5"}):e.jsx(Me,{className:"w-5 h-5"})}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-gray-900",children:w.description}),e.jsx("p",{className:"text-sm text-gray-500",children:A(w.created_at)})]})]}),e.jsxs("p",{className:`font-semibold ${w.type==="credit"?"text-green-600":"text-red-600"}`,children:[w.type==="credit"?"+":"-",E(w.amount)]})]},w.id))}):e.jsxs("div",{className:"text-center py-8",children:[e.jsx(K,{className:"w-16 h-16 text-gray-400 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No transactions yet"}),e.jsx("p",{className:"text-gray-500",children:"Your transaction history will appear here"})]})]}):e.jsx("div",{className:"bg-gradient-to-br from-yellow-50 to-amber-100 border-2 border-yellow-300 rounded-xl shadow-lg p-6",children:e.jsxs("div",{className:"text-center py-8",children:[e.jsx(L,{className:"w-16 h-16 text-yellow-600 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-yellow-800 mb-2",children:"Premium Feature"}),e.jsx("p",{className:"text-yellow-700 mb-4",children:"Upgrade to premium to view your transaction history"}),e.jsxs(R,{to:"/dashboard/premium",className:"inline-flex items-center px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors touch-target",children:[e.jsx(L,{className:"w-4 h-4 mr-2"}),"Upgrade Now"]})]})})]}),e.jsxs("div",{className:"space-y-6",children:[t?.is_premium?e.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[e.jsx("h2",{className:"text-xl font-bold text-gray-900 mb-6",children:"Referral Program"}),t?.referral_code?e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"bg-green-50 rounded-lg p-4",children:[e.jsx("p",{className:"text-sm text-gray-600 mb-2",children:"Your Referral Code"}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-xl font-bold text-green-600",children:t.referral_code}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsx("button",{onClick:b,className:"p-2 text-green-600 hover:bg-green-100 rounded-lg transition-colors touch-target",children:e.jsx(rr,{className:"w-4 h-4"})}),e.jsx("button",{onClick:N,className:"p-2 text-green-600 hover:bg-green-100 rounded-lg transition-colors touch-target",children:e.jsx(tr,{className:"w-4 h-4"})})]})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("p",{className:"text-xl font-bold text-gray-900",children:r.referralCount}),e.jsx("p",{className:"text-sm text-gray-600",children:"People Referred"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("p",{className:"text-xl font-bold text-green-600",children:E(r.referralEarnings)}),e.jsx("p",{className:"text-sm text-gray-600",children:"Bonuses Earned"})]})]}),i.length>0&&e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold text-gray-900 mb-3",children:"Recent Referrals"}),e.jsx("div",{className:"space-y-2",children:i.map(w=>e.jsxs("div",{className:"flex items-center justify-between text-sm",children:[e.jsx("span",{className:"text-gray-600",children:w.referred_user?.full_name||"New User"}),e.jsxs("span",{className:"text-green-600 font-medium",children:["+",E(w.bonus_amount)]})]},w.id))})]}),e.jsx(R,{to:"/dashboard/referrals",className:"block w-full text-center bg-green-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-green-700 transition-colors touch-target",children:"View All Referrals"})]}):e.jsxs("div",{className:"text-center py-6",children:[e.jsx(H,{className:"w-16 h-16 text-gray-400 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Referral Code"}),e.jsx("p",{className:"text-gray-500 mb-4",children:"Complete your KYC to get a referral code"}),e.jsx(R,{to:"/register",className:"bg-green-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-green-700 transition-colors touch-target",children:"Complete KYC"})]})]}):e.jsx("div",{className:"bg-gradient-to-br from-purple-50 to-purple-100 border-2 border-purple-300 rounded-xl shadow-lg p-6",children:e.jsxs("div",{className:"text-center py-8",children:[e.jsx(L,{className:"w-16 h-16 text-purple-600 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-purple-800 mb-2",children:"Premium Feature"}),e.jsx("p",{className:"text-purple-700 mb-4",children:"Upgrade to premium to access the referral program and start earning"}),e.jsxs(R,{to:"/dashboard/premium",className:"inline-flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors touch-target",children:[e.jsx(L,{className:"w-4 h-4 mr-2"}),"Upgrade Now"]})]})}),e.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[e.jsx("h2",{className:"text-xl font-bold text-gray-900 mb-6",children:"Order Summary"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Total Orders"}),e.jsx("span",{className:"font-semibold text-gray-900",children:r.totalOrders})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Total Spent"}),e.jsx("span",{className:"font-semibold text-gray-900",children:E(r.totalSpent)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Average Order"}),e.jsx("span",{className:"font-semibold text-gray-900",children:r.totalOrders>0?E(r.totalSpent/r.totalOrders):"₹0.00"})]})]}),e.jsxs(R,{to:"/products",className:"w-full mt-6 bg-green-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-green-700 transition-colors flex items-center justify-center space-x-2 touch-target",children:[e.jsx(Me,{className:"w-5 h-5"}),e.jsx("span",{children:"Browse Products"})]})]})]})]})]})};function Li(){const{user:t,setUser:s}=J(),[r,a]=f.useState(!1),[l,n]=f.useState(!1),[i,c]=f.useState({full_name:t?.full_name||"",phone:t?.phone||"",address:t?.address||""}),o=async()=>{if(t){n(!0);try{const{data:m,error:h}=await y.from("users").update(i).eq("id",t.id).select().single();if(h)throw h;s({...t,...m}),a(!1),k.success("Profile updated successfully!")}catch(m){console.error("Error updating profile:",m),k.error("Failed to update profile")}finally{n(!1)}}},d=()=>{c({full_name:t?.full_name||"",phone:t?.phone||"",address:t?.address||""}),a(!1)};return t?e.jsxs("div",{className:"max-w-2xl",children:[e.jsxs("div",{className:"flex items-center justify-between mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Profile"}),r?e.jsxs("div",{className:"flex gap-2",children:[e.jsxs("button",{onClick:o,disabled:l,className:"flex items-center gap-2 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50",children:[e.jsx(cs,{className:"h-4 w-4"}),l?"Saving...":"Save"]}),e.jsxs("button",{onClick:d,className:"flex items-center gap-2 bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors",children:[e.jsx(oe,{className:"h-4 w-4"}),"Cancel"]})]}):e.jsxs("button",{onClick:()=>a(!0),className:"flex items-center gap-2 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors",children:[e.jsx(ta,{className:"h-4 w-4"}),"Edit Profile"]})]}),e.jsx("div",{className:"bg-white rounded-lg shadow-sm p-6",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[e.jsx(Ee,{className:"h-4 w-4 inline mr-2"}),"Full Name"]}),r?e.jsx("input",{type:"text",value:i.full_name,onChange:m=>c({...i,full_name:m.target.value}),className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"}):e.jsx("p",{className:"text-gray-900 p-3 bg-gray-50 rounded-lg",children:t.full_name})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[e.jsx(Ie,{className:"h-4 w-4 inline mr-2"}),"Email"]}),e.jsx("p",{className:"text-gray-900 p-3 bg-gray-50 rounded-lg",children:t.email}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Email cannot be changed"})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[e.jsx(Bs,{className:"h-4 w-4 inline mr-2"}),"Phone Number"]}),r?e.jsx("input",{type:"tel",value:i.phone,onChange:m=>c({...i,phone:m.target.value}),className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent",placeholder:"Enter your phone number"}):e.jsx("p",{className:"text-gray-900 p-3 bg-gray-50 rounded-lg",children:t.phone||"Not provided"})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[e.jsx(rs,{className:"h-4 w-4 inline mr-2"}),"Address"]}),r?e.jsx("textarea",{value:i.address,onChange:m=>c({...i,address:m.target.value}),rows:3,className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent",placeholder:"Enter your complete address"}):e.jsx("p",{className:"text-gray-900 p-3 bg-gray-50 rounded-lg",children:t.address||"Not provided"})]}),e.jsxs("div",{className:"border-t border-gray-200 pt-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Account Information"}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Account Status"}),e.jsx("div",{className:"flex items-center gap-2 mt-1",children:t.is_premium?e.jsx("span",{className:"bg-gradient-to-r from-yellow-400 to-yellow-600 text-white px-3 py-1 rounded-full text-sm font-medium",children:"Premium Member"}):e.jsx("span",{className:"bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm font-medium",children:"Regular Member"})})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Member Since"}),e.jsx("p",{className:"text-gray-900 font-medium mt-1",children:new Date(t.created_at).toLocaleDateString()})]})]})]})]})})]}):null}function qi(){const{user:t}=J(),[s,r]=f.useState([]),[a,l]=f.useState(null),[n,i]=f.useState(!0),[c,o]=f.useState("list");f.useEffect(()=>{t&&d()},[t]);const d=async()=>{try{i(!0);const{data:u,error:j}=await y.from("orders").select(`
          id,
          order_number,
          status,
          total_amount,
          wallet_used,
          shipping_cost,
          delivery_address,
          created_at,
          updated_at,
          order_items (
            id,
            quantity,
            price,
            products (
              id,
              name,
              image_url,
              sku
            )
          )
        `).eq("user_id",t.id).order("created_at",{ascending:!1});if(j)throw j;r(u||[])}catch(u){console.error("Error loading orders:",u),B.error("Failed to load orders")}finally{i(!1)}},m=async u=>{try{const{data:j,error:b}=await y.rpc("get_order_details",{p_order_id:u});if(b)throw b;j&&!j.error?(l({...j.order,items:j.items||[],status_history:j.status_history||[],shipment:j.shipment}),o("details")):B.error("Order details not found")}catch(j){console.error("Error loading order details:",j),B.error("Failed to load order details")}},h=u=>{switch(u){case"pending":return e.jsx(Ne,{className:"h-5 w-5 text-yellow-500"});case"confirmed":return e.jsx(V,{className:"h-5 w-5 text-blue-500"});case"processing":return e.jsx(Z,{className:"h-5 w-5 text-purple-500"});case"shipped":return e.jsx(De,{className:"h-5 w-5 text-orange-500"});case"delivered":return e.jsx(V,{className:"h-5 w-5 text-green-500"});case"cancelled":return e.jsx(Ns,{className:"h-5 w-5 text-red-500"});default:return e.jsx(Ne,{className:"h-5 w-5 text-gray-500"})}},p=u=>{switch(u){case"pending":return"bg-yellow-100 text-yellow-800";case"confirmed":return"bg-blue-100 text-blue-800";case"processing":return"bg-purple-100 text-purple-800";case"shipped":return"bg-orange-100 text-orange-800";case"delivered":return"bg-green-100 text-green-800";case"cancelled":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},x=u=>new Date(u).toLocaleDateString("en-IN",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),g=u=>`₹${u.toFixed(2)}`;return n?e.jsxs("div",{className:"flex items-center justify-center p-8",children:[e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),e.jsx("span",{className:"ml-2",children:"Loading orders..."})]}):c==="details"&&a?e.jsxs("div",{className:"max-w-4xl mx-auto p-4 sm:p-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between mb-6 gap-4",children:[e.jsxs("button",{onClick:()=>o("list"),className:"flex items-center gap-2 text-blue-600 hover:text-blue-800 touch-target",children:[e.jsx(zs,{className:"h-4 w-4"}),"Back to Orders"]}),e.jsxs("h1",{className:"text-xl sm:text-2xl font-bold text-gray-900",children:["Order ",a.order_number]})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border p-4 sm:p-6 mb-4 sm:mb-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between mb-4 gap-3",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[h(a.status),e.jsx("span",{className:`px-3 py-1 rounded-full text-sm font-medium ${p(a.status)}`,children:a.status.charAt(0).toUpperCase()+a.status.slice(1)})]}),e.jsxs("div",{className:"text-left sm:text-right",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Order Total"}),e.jsx("p",{className:"text-lg sm:text-xl font-bold text-gray-900",children:g(a.total_amount)})]})]}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mt-6",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(fr,{className:"h-5 w-5 text-gray-400"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Order Date"}),e.jsx("p",{className:"font-medium",children:x(a.created_at)})]})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(xs,{className:"h-5 w-5 text-gray-400"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Payment"}),e.jsx("p",{className:"font-medium",children:a.wallet_used>0?"Wallet":"Cash on Delivery"})]})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(rs,{className:"h-5 w-5 text-gray-400"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Delivery Address"}),e.jsx("p",{className:"font-medium text-sm",children:a.delivery_address})]})]})]})]}),a.shipment&&e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border p-4 sm:p-6 mb-4 sm:mb-6",children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Tracking Information"}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Tracking Number"}),e.jsx("p",{className:"font-mono font-medium",children:a.shipment.tracking_number})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Carrier"}),e.jsx("p",{className:"font-medium",children:a.shipment.carrier})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Estimated Delivery"}),e.jsx("p",{className:"font-medium",children:a.shipment.estimated_delivery?new Date(a.shipment.estimated_delivery).toLocaleDateString("en-IN"):"Not available"})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Shipment Status"}),e.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${p(a.shipment.status)}`,children:a.shipment.status})]})]})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border p-4 sm:p-6 mb-4 sm:mb-6",children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Order Items"}),e.jsx("div",{className:"space-y-4",children:(a.items||a.order_items||[]).map(u=>e.jsxs("div",{className:"flex items-center gap-3 sm:gap-4 p-3 sm:p-4 border rounded-lg",children:[e.jsx("img",{src:u.product?.image_url||"/placeholder-product.jpg",alt:u.product?.name||"Product",className:"w-12 h-12 sm:w-16 sm:h-16 object-cover rounded-lg flex-shrink-0"}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h4",{className:"font-medium text-sm sm:text-base truncate",children:u.product?.name||"Product"}),e.jsxs("p",{className:"text-xs sm:text-sm text-gray-500",children:["SKU: ",u.product?.sku||"N/A"]}),e.jsxs("p",{className:"text-xs sm:text-sm text-gray-500",children:["Quantity: ",u.quantity]})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("p",{className:"font-medium text-sm sm:text-base",children:g(u.price)}),e.jsx("p",{className:"text-xs sm:text-sm text-gray-500",children:"per item"})]})]},u.id))})]}),a.status_history&&a.status_history.length>0&&e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Order Timeline"}),e.jsx("div",{className:"space-y-4",children:a.status_history.map((u,j)=>e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("div",{className:"flex-shrink-0",children:h(u.status)}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${p(u.status)}`,children:u.status.charAt(0).toUpperCase()+u.status.slice(1)}),e.jsx("span",{className:"text-sm text-gray-500",children:x(u.created_at)})]}),u.notes&&e.jsx("p",{className:"text-sm text-gray-600 mt-1",children:u.notes}),e.jsxs("p",{className:"text-xs text-gray-500 mt-1",children:["Updated by: ",u.changed_by]})]})]},j))})]})]}):e.jsxs("div",{className:"max-w-6xl mx-auto p-4 sm:p-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between mb-6 gap-4",children:[e.jsx("h1",{className:"text-xl sm:text-2xl font-bold text-gray-900",children:"My Orders"}),e.jsxs("button",{onClick:d,className:"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 touch-target",children:[e.jsx(Z,{className:"h-4 w-4"}),"Refresh"]})]}),s.length===0?e.jsxs("div",{className:"text-center py-12",children:[e.jsx(Z,{className:"h-16 w-16 text-gray-300 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No orders yet"}),e.jsx("p",{className:"text-gray-500",children:"Start shopping to see your orders here!"})]}):e.jsx("div",{className:"grid gap-4 sm:gap-6",children:s.map(u=>e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border p-4 sm:p-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between mb-4 gap-3",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3",children:[e.jsx("span",{className:"font-mono text-sm font-medium text-gray-600",children:u.order_number||`ORD${u.id.slice(0,8)}`}),e.jsx("span",{className:`px-3 py-1 rounded-full text-sm font-medium ${p(u.status)} w-fit`,children:u.status.charAt(0).toUpperCase()+u.status.slice(1)})]}),e.jsxs("button",{onClick:()=>m(u.id),className:"flex items-center gap-2 px-3 py-2 text-blue-600 hover:bg-blue-50 rounded-lg touch-target w-fit",children:[e.jsx(ne,{className:"h-4 w-4"}),e.jsx("span",{className:"hidden sm:inline",children:"View Details"}),e.jsx("span",{className:"sm:hidden",children:"Details"})]})]}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-4",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Order Date"}),e.jsx("p",{className:"font-medium",children:x(u.created_at)})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Total Amount"}),e.jsx("p",{className:"font-medium",children:g(u.total_amount)})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Payment Method"}),e.jsx("p",{className:"font-medium",children:u.wallet_used>0?"Wallet":"Cash on Delivery"})]})]}),e.jsxs("div",{className:"flex items-center justify-between pt-4 border-t",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[h(u.status),e.jsxs("span",{className:"text-sm text-gray-600",children:["Last updated: ",x(u.updated_at)]})]}),e.jsxs("div",{className:"text-sm text-gray-500",children:[u.order_items?.length||0," item(s)"]})]})]},u.id))})]})}const Bi=async(t,s,r,a)=>{try{if(s<=0)return{success:!1,error:"Amount must be greater than 0"};if(s>1e5)return{success:!1,error:"Maximum amount is ₹1,00,000 per transaction"};const{data:l,error:n}=await y.from("users").select("wallet_balance").eq("id",t).single();if(n)return{success:!1,error:"User not found"};const c=(l.wallet_balance||0)+s,{error:o}=await y.from("wallet_transactions").insert({user_id:t,type:"credit",amount:s,description:r,reference_type:"admin",reference_id:`ADMIN_${a}_${Date.now()}`});if(o)return{success:!1,error:"Failed to create transaction record"};const{error:d}=await y.from("users").update({wallet_balance:c,updated_at:new Date().toISOString()}).eq("id",t);return d?{success:!1,error:"Failed to update wallet balance"}:{success:!0,newBalance:c}}catch(l){return console.error("Error adding money to wallet:",l),{success:!1,error:"An unexpected error occurred"}}},pe=t=>`₹${t.toFixed(2)}`,qs=t=>new Date(t).toLocaleDateString("en-IN",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"});function zi(){const{user:t,refreshUser:s}=J(),[r,a]=f.useState([]),[l,n]=f.useState(!0),[i,c]=f.useState("all"),o=f.useCallback(async()=>{if(t?.id)try{n(!0);const{data:p,error:x}=await y.from("wallet_transactions").select("*").eq("user_id",t.id).order("created_at",{ascending:!1});if(x)throw x;a(p||[])}catch(p){console.error("Error fetching transactions:",p),k.error("Failed to load transaction history")}finally{n(!1)}},[t?.id]);if(f.useEffect(()=>{t?.id&&o()},[t?.id,o]),!t?.is_premium)return e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsxs("div",{className:"text-center",children:[e.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Premium Required"}),e.jsx("p",{className:"text-gray-600",children:"You need a premium subscription to access the wallet."})]})});const d=r.filter(p=>i==="all"?!0:p.type===i),m=p=>p==="credit"?e.jsx(re,{className:"h-5 w-5 text-green-500"}):e.jsx(ws,{className:"h-5 w-5 text-red-500"}),h=p=>p==="credit"?"text-green-600":"text-red-600";return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4",children:[e.jsx("h1",{className:"text-2xl sm:text-3xl font-bold text-gray-900",children:"My Wallet"}),e.jsxs("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 w-full sm:w-auto",children:[e.jsxs("div",{className:"flex items-center gap-2 text-blue-700",children:[e.jsx(we,{className:"h-4 w-4"}),e.jsx("span",{className:"text-sm font-medium",children:"Need to add money?"})]}),e.jsx("p",{className:"text-xs text-blue-600 mt-1",children:"Contact administrator to add funds to your wallet"})]})]}),e.jsx("div",{className:"bg-gradient-to-r from-green-600 to-green-700 text-white p-6 sm:p-8 rounded-2xl",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-green-100 mb-2 text-sm sm:text-base",children:"Available Balance"}),e.jsx("p",{className:"text-3xl sm:text-4xl font-bold",children:pe(t?.wallet_balance||0)})]}),e.jsx(K,{className:"h-12 w-12 sm:h-16 sm:w-16 text-green-200"})]})}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm p-6",children:[e.jsx("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Wallet Features"}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"flex items-center gap-3 p-3 bg-gray-50 rounded-lg",children:[e.jsx("div",{className:"w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center",children:e.jsx(xs,{className:"w-5 h-5 text-green-600"})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:"Instant Payments"}),e.jsx("p",{className:"text-xs text-gray-500",children:"Quick checkout"})]})]}),e.jsxs("div",{className:"flex items-center gap-3 p-3 bg-gray-50 rounded-lg",children:[e.jsx("div",{className:"w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center",children:e.jsx(K,{className:"w-5 h-5 text-blue-600"})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:"Secure Storage"}),e.jsx("p",{className:"text-xs text-gray-500",children:"Protected funds"})]})]}),e.jsxs("div",{className:"flex items-center gap-3 p-3 bg-gray-50 rounded-lg",children:[e.jsx("div",{className:"w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center",children:e.jsx(ce,{className:"w-5 h-5 text-purple-600"})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:"Cashback"}),e.jsx("p",{className:"text-xs text-gray-500",children:"Earn rewards"})]})]}),e.jsxs("div",{className:"flex items-center gap-3 p-3 bg-gray-50 rounded-lg",children:[e.jsx("div",{className:"w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center",children:e.jsx(et,{className:"w-5 h-5 text-orange-600"})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:"Track History"}),e.jsx("p",{className:"text-xs text-gray-500",children:"Full audit trail"})]})]})]})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-4 mb-6",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(hr,{className:"h-5 w-5 text-gray-500"}),e.jsx("span",{className:"text-sm font-medium text-gray-700 sm:hidden",children:"Filter:"})]}),e.jsx("div",{className:"flex flex-wrap gap-2",children:[{key:"all",label:"All Transactions"},{key:"credit",label:"Money In"},{key:"debit",label:"Money Out"}].map(p=>e.jsx("button",{onClick:()=>c(p.key),className:`px-3 sm:px-4 py-2 rounded-lg text-sm font-medium transition-colors touch-target ${i===p.key?"bg-green-100 text-green-700":"bg-gray-100 text-gray-600 hover:bg-gray-200"}`,children:p.label},p.key))})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm",children:[e.jsx("div",{className:"p-4 sm:p-6 border-b border-gray-200",children:e.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"Transaction History"})}),l?e.jsx("div",{className:"flex items-center justify-center py-12",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"})}):d.length===0?e.jsxs("div",{className:"text-center py-12 px-4",children:[e.jsx(K,{className:"h-12 w-12 sm:h-16 sm:w-16 text-gray-400 mx-auto mb-4"}),e.jsx("h3",{className:"text-base sm:text-lg font-medium text-gray-900 mb-2",children:"No transactions yet"}),e.jsx("p",{className:"text-sm sm:text-base text-gray-500",children:"Your wallet transactions will appear here"})]}):e.jsx("div",{className:"divide-y divide-gray-200",children:d.map(p=>e.jsxs("div",{className:"p-4 sm:p-6",children:[e.jsxs("div",{className:"sm:hidden",children:[e.jsxs("div",{className:"flex items-start gap-3 mb-3",children:[m(p.type),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h3",{className:"font-medium text-gray-900 text-sm truncate",children:p.description}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:qs(p.created_at)}),p.reference_type&&e.jsx("p",{className:"text-xs text-gray-500 capitalize mt-1",children:p.reference_type})]})]}),e.jsx("div",{className:"text-right",children:e.jsxs("p",{className:`font-semibold text-lg ${h(p.type)}`,children:[p.type==="credit"?"+":"-",pe(p.amount)]})})]}),e.jsxs("div",{className:"hidden sm:flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[m(p.type),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-gray-900",children:p.description}),e.jsx("p",{className:"text-sm text-gray-500",children:qs(p.created_at)})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("p",{className:`font-semibold ${h(p.type)}`,children:[p.type==="credit"?"+":"-",pe(p.amount)]}),p.reference_type&&e.jsx("p",{className:"text-xs text-gray-500 capitalize",children:p.reference_type})]})]})]},p.id))})]})]})}class Wi{async generateReferralCode(s){try{const{data:r}=await y.from("users").select("referral_code").eq("id",s).single();if(r?.referral_code)return r.referral_code;const{data:a,error:l}=await y.rpc("generate_referral_code");if(l)throw l;const n=a,{error:i}=await y.from("users").update({referral_code:n}).eq("id",s);if(i)throw i;return await this.logReferralAction(s,"code_generated",{code:n}),n}catch(r){return console.error("Error generating referral code:",r),null}}async createReferral(s,r,a){try{if(console.log("Multi-level referral processing started:",{referralCode:s,newUserId:r,newUserEmail:a}),!await Fe.isMultiLevelReferralEnabled())return console.log("Multi-level referral system is disabled"),!1;const{data:n,error:i}=await y.from("users").select("id, email, is_premium, referred_by").eq("id",r).single();if(i||!n)return console.error("New user not found:",i),!1;if(!n.is_premium)return console.log("User is not premium - referral processing skipped"),!1;const c=await Fe.processMultiLevelReferral(r,a,s);return c.success?(console.log("Multi-level referral processed successfully:",{totalBonusDistributed:c.totalBonusDistributed,levelsProcessed:c.levelsProcessed,referralId:c.referralId}),await this.logReferralAction(r,"multi_level_referral_completed",{referral_code:s,total_bonus_distributed:c.totalBonusDistributed,levels_processed:c.levelsProcessed,chain_users:c.chainUsers.length}),!0):(console.error("Multi-level referral processing failed:",c.errors),!1)}catch(l){return console.error("Error in multi-level referral processing:",l),!1}}async completeReferral(s){try{console.log("Referral completion triggered for premium user:",s);const{data:r,error:a}=await y.from("users").select("id, email, full_name, is_premium, referred_by").eq("id",s).single();if(a||!r){console.error("User not found for referral completion:",a);return}if(!r.is_premium){console.log("User is not premium - referral completion skipped");return}if(!r.referred_by){console.log("User has no referral code - referral completion skipped");return}const{data:l,error:n}=await y.from("referrals").select("id, status").eq("referred_user_id",s).single();if(!n&&l&&l.status==="completed"){console.log("Referral already processed for user:",s);return}await this.createReferral(r.referred_by,s,r.email)?console.log("Multi-level referral completion successful for user:",s):console.error("Multi-level referral completion failed for user:",s)}catch(r){console.error("Error completing referral:",r)}}async addWalletFunds(s,r,a,l="referral",n){try{const{data:i}=await y.from("users").select("wallet_balance").eq("id",s).single(),o=(i?.wallet_balance||0)+r;await y.from("users").update({wallet_balance:o}).eq("id",s),await y.from("wallet_transactions").insert({user_id:s,type:"credit",amount:r,description:a,reference_type:l,reference_id:n,referral_id:n})}catch(i){console.error("Error adding wallet funds:",i)}}async checkEwalletUnlock(s){}async getReferralStats(s){try{const{data:r}=await y.from("users").select("premium_referral_count, ewallet_unlocked, referral_special_status").eq("id",s).single(),{data:a}=await y.from("referrals").select("status, bonus_amount").eq("referrer_id",s),{data:l}=await y.from("wallet_transactions").select("amount").eq("user_id",s).eq("reference_type","referral"),n=a?.length||0,i=a?.filter(d=>d.status==="completed").length||0,c=a?.filter(d=>d.status==="pending").length||0,o=l?.reduce((d,m)=>d+m.amount,0)||0;return console.log("Referral Stats Debug:",{userId:s,totalReferrals:n,completedReferrals:i,pendingReferrals:c,premiumReferrals:r?.premium_referral_count||0,totalEarnings:o,ewalletUnlocked:r?.ewallet_unlocked||!1}),{totalReferrals:n,completedReferrals:i,pendingReferrals:c,totalEarnings:o,premiumReferrals:r?.premium_referral_count||0,ewalletUnlocked:r?.ewallet_unlocked||!1,specialStatus:r?.referral_special_status||!1}}catch(r){return console.error("Error getting referral stats:",r),{totalReferrals:0,completedReferrals:0,pendingReferrals:0,totalEarnings:0,premiumReferrals:0,ewalletUnlocked:!1,specialStatus:!1}}}async getReferralNetwork(s){try{const{data:r,error:a}=await y.from("referrals").select(`
          *,
          referred_user:users!referrals_referred_user_id_fkey(full_name, email, phone)
        `).eq("referrer_id",s).order("created_at",{ascending:!1});if(a)throw a;return r||[]}catch(r){return console.error("Error getting referral network:",r),[]}}async getReferralStatisticsDetailed(s){try{console.log("Getting detailed multi-level referral statistics for user:",s);const{data:r}=await y.from("users").select("id, email, full_name, is_premium, referral_code, ewallet_unlocked").eq("id",s).single();if(!r)return null;const a=await this.getReferralStats(s),{data:l}=await y.from("wallet_transactions").select("amount, description, metadata, created_at").eq("user_id",s).eq("reference_type","multi_level_referral").order("created_at",{ascending:!1}),n={};let i=0;l?.forEach(o=>{const d=o.metadata?.referral_level;d&&(n[d]=(n[d]||0)+o.amount,i+=o.amount)});const c=await this.calculateNetworkDepth(s);return{user:{id:r.id,email:r.email,full_name:r.full_name,is_premium:r.is_premium,referral_code:r.referral_code,ewallet_unlocked:r.ewallet_unlocked},statistics:a,multiLevelEarnings:{total:i,byLevel:n,transactions:l||[]},networkInfo:{depth:c,canRefer:r.is_premium}}}catch(r){return console.error("Error getting detailed referral statistics:",r),null}}async calculateNetworkDepth(s){try{const{count:r}=await y.from("referrals").select("*",{count:"exact",head:!0}).eq("referrer_id",s);return r||0}catch(r){return console.error("Error calculating network depth:",r),0}}async simulateNextReferralBonus(s){try{console.log("Multi-level referral bonus simulation for user:",s);const{data:r,error:a}=await y.from("users").select("referral_code, is_premium").eq("id",s).single();if(a||!r||!r.is_premium)return{canRefer:!1,reason:"User is not premium or not found"};const l=await Fe.getConfigurableBonusAmounts();return{canRefer:!0,userReferralCode:r.referral_code,directBonus:l[1]||250,multiLevelDistribution:Object.entries(l).slice(0,10).map(([i,c])=>({level:parseInt(i),amount:c,description:`Level ${i} referrer receives ₹${c}`})),totalPossibleDistribution:Object.values(l).slice(0,10).reduce((i,c)=>i+c,0),note:"Bonuses are only distributed when the referred user becomes premium"}}catch(r){return console.error("Error simulating referral bonus:",r),null}}async logReferralAction(s,r,a){try{await y.from("referral_audit_log").insert({user_id:s,action:r,details:a,created_at:new Date().toISOString()})}catch(l){console.error("Error logging referral action:",l)}}}const dr=new Wi;class Yi{async isEwalletUnlocked(s){try{const{data:r}=await y.from("users").select("is_premium").eq("id",s).single();return r?.is_premium||!1}catch(r){return console.error("Error checking premium status:",r),!1}}async getBalance(s){try{const{data:r}=await y.from("users").select("wallet_balance").eq("id",s).single(),a=r?.wallet_balance||0;return{balance:a,locked_balance:0,available_balance:a}}catch(r){return console.error("Error getting wallet balance:",r),{balance:0,locked_balance:0,available_balance:0}}}async addFunds(s,r,a,l="admin_credit",n,i,c=1){try{const{data:o}=await y.from("users").select("wallet_balance").eq("id",s).single();if(!o)throw new Error("User not found");const d=(o.wallet_balance||0)+r,{error:m}=await y.from("users").update({wallet_balance:d}).eq("id",s);if(m)throw m;const{error:h}=await y.from("wallet_transactions").insert({user_id:s,type:"credit",amount:r,description:a,reference_type:l,reference_id:n,referral_id:i,bonus_level:c});if(h)throw h;return!0}catch(o){return console.error("Error adding funds:",o),!1}}async deductFunds(s,r,a,l="purchase",n){try{const{data:i}=await y.from("users").select("is_premium").eq("id",s).single();if(!i?.is_premium)throw new Error("Wallet access requires premium membership.");const{data:c}=await y.from("users").select("wallet_balance").eq("id",s).single();if(!c)throw new Error("User not found");const o=c.wallet_balance||0;if(o<r)throw new Error("Insufficient balance");const d=o-r,{error:m}=await y.from("users").update({wallet_balance:d}).eq("id",s);if(m)throw m;const{error:h}=await y.from("wallet_transactions").insert({user_id:s,type:"debit",amount:r,description:a,reference_type:l,reference_id:n});if(h)throw h;return!0}catch(i){return console.error("Error deducting funds:",i),!1}}async withdrawFunds(s,r){try{const{data:a}=await y.from("users").select("is_premium").eq("id",s).single();if(!a?.is_premium)throw new Error("Wallet access requires premium membership.");const{data:l}=await y.from("admin_settings").select("setting_value").eq("setting_key","minimum_withdrawal_amount").single(),n=parseFloat(l?.setting_value||"1");if(r<n)throw new Error(`Minimum withdrawal amount is ₹${n}`);const{data:i}=await y.from("admin_settings").select("setting_value").eq("setting_key","withdrawal_fee_percentage").single(),c=parseFloat(i?.setting_value||"0"),o=r*c/100,d=r+o,{data:m}=await y.from("users").select("wallet_balance").eq("id",s).single();if(!m)throw new Error("User not found");const h=m.wallet_balance||0;if(h<d)throw new Error("Insufficient balance for withdrawal including fees");const p=h-d,{error:x}=await y.from("users").update({wallet_balance:p}).eq("id",s);if(x)throw x;const{error:g}=await y.from("wallet_transactions").insert({user_id:s,type:"debit",amount:r,description:`Withdrawal of ₹${r}`,reference_type:"withdrawal"});if(g)throw g;return o>0&&await y.from("wallet_transactions").insert({user_id:s,type:"debit",amount:o,description:`Withdrawal fee (${c}%)`,reference_type:"withdrawal_fee"}),!0}catch(a){return console.error("Error withdrawing funds:",a),!1}}async getTransactionHistory(s,r=50,a=0){try{const{data:l,error:n}=await y.from("wallet_transactions").select("*").eq("user_id",s).order("created_at",{ascending:!1}).range(a,a+r-1);if(n)throw n;return l||[]}catch(l){return console.error("Error getting transaction history:",l),[]}}async getReferralTransactions(s){try{const{data:r,error:a}=await y.from("wallet_transactions").select("*").eq("user_id",s).in("reference_type",["referral","referral_special"]).order("created_at",{ascending:!1});if(a)throw a;return r||[]}catch(r){return console.error("Error getting referral transactions:",r),[]}}async getUnlockProgress(s){try{const{data:r}=await y.from("users").select("is_premium").eq("id",s).single();return{current:1,required:1,unlocked:r?.is_premium||!1,progress:100}}catch(r){return console.error("Error checking premium status:",r),{current:1,required:1,unlocked:!1,progress:0}}}}const Vi=new Yi;class Gi{constructor(){this.DEFAULT_PREFIX="HERB",this.DEFAULT_LENGTH=8,this.MAX_ATTEMPTS=10}async generateUniqueReferralCode(s,r,a={}){try{const{prefix:l=this.DEFAULT_PREFIX,length:n=this.DEFAULT_LENGTH,includeUserInfo:i=!0,customPattern:c}=a;let o=0,d;do{if(o++,c?d=this.generateCustomPatternCode(c,s,r):i?d=this.generatePersonalizedCode(l,s,r,n):d=this.generateRandomCode(l,n),await this.isCodeUnique(d))return{success:!0,referralCode:d};if(o>=this.MAX_ATTEMPTS)throw new Error("Failed to generate unique referral code after maximum attempts")}while(o<this.MAX_ATTEMPTS);return{success:!1,error:"Failed to generate unique referral code"}}catch(l){return console.error("Error generating referral code:",l),{success:!1,error:l instanceof Error?l.message:"Unknown error"}}}generatePersonalizedCode(s,r,a,l){const n=a.split("@")[0].toUpperCase().replace(/[^A-Z0-9]/g,""),i=r.replace(/-/g,"").toUpperCase(),c=Date.now().toString(36).toUpperCase(),d=(n+i+c).substring(0,l-s.length);return`${s}${d}`}generateRandomCode(s,r){const a="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";let l=s;for(let n=s.length;n<r;n++)l+=a.charAt(Math.floor(Math.random()*a.length));return l}generateCustomPatternCode(s,r,a){const l={"{USER_ID}":r.substring(0,8).toUpperCase(),"{EMAIL}":a.split("@")[0].toUpperCase().substring(0,6),"{TIMESTAMP}":Date.now().toString(36).toUpperCase(),"{RANDOM}":Math.random().toString(36).substring(2,8).toUpperCase()};let n=s;return Object.entries(l).forEach(([i,c])=>{n=n.replace(new RegExp(i,"g"),c)}),n}async isCodeUnique(s){try{const{data:r,error:a}=await y.from("users").select("id").eq("referral_code",s).limit(1);return a?(console.error("Error checking code uniqueness:",a),!1):!r||r.length===0}catch(r){return console.error("Error in uniqueness check:",r),!1}}async assignReferralCodeToUser(s,r){try{const{error:a}=await y.from("users").update({referral_code:r,updated_at:new Date().toISOString()}).eq("id",s);return a?(console.error("Error assigning referral code:",a),!1):!0}catch(a){return console.error("Error in assignReferralCodeToUser:",a),!1}}async generateAndAssignForPremiumUser(s,r){try{const{data:a,error:l}=await y.from("users").select("referral_code, is_premium").eq("id",s).single();if(l)return{success:!1,error:"Failed to check existing user data"};if(a.referral_code)return{success:!0,referralCode:a.referral_code};if(!a.is_premium)return{success:!1,error:"User is not premium"};const n=await this.generateUniqueReferralCode(s,r,{prefix:"HERB",length:10,includeUserInfo:!0});return!n.success||!n.referralCode?n:await this.assignReferralCodeToUser(s,n.referralCode)?(await this.logReferralCodeGeneration(s,n.referralCode),{success:!0,referralCode:n.referralCode}):{success:!1,error:"Failed to assign referral code to user"}}catch(a){return console.error("Error in generateAndAssignForPremiumUser:",a),{success:!1,error:a instanceof Error?a.message:"Unknown error"}}}async logReferralCodeGeneration(s,r){try{await y.from("referral_logs").insert({user_id:s,action:"code_generated",details:{referral_code:r,generated_at:new Date().toISOString(),generation_method:"automatic_premium_upgrade"},created_at:new Date().toISOString()})}catch(a){console.error("Error logging referral code generation:",a)}}async generateCodesForExistingPremiumUsers(){try{const{data:s,error:r}=await y.from("users").select("id, email, full_name").eq("is_premium",!0).is("referral_code",null);if(r)throw new Error("Failed to fetch premium users");if(!s||s.length===0)return{processed:0,successful:0,errors:[]};let a=0;const l=[];for(const n of s)try{const i=await this.generateAndAssignForPremiumUser(n.id,n.email);i.success?a++:l.push(`User ${n.email}: ${i.error}`)}catch(i){l.push(`User ${n.email}: ${i instanceof Error?i.message:"Unknown error"}`)}return{processed:s.length,successful:a,errors:l}}catch(s){return console.error("Error in bulk generation:",s),{processed:0,successful:0,errors:[s instanceof Error?s.message:"Unknown error"]}}}static validateReferralCodeFormat(s){return/^[A-Z0-9]{6,12}$/.test(s)}async getReferralCodeStats(){try{const{data:s,error:r}=await y.from("users").select("referral_code, is_premium").not("referral_code","is",null);if(r)throw r;const a=s?.length||0,l=s?.filter(i=>i.is_premium).length||0,n=a-l;return{total_codes:a,premium_user_codes:l,non_premium_user_codes:n,code_coverage:a>0?(l/a*100).toFixed(2):0}}catch(s){return console.error("Error getting referral code stats:",s),null}}}const Ze=new Gi;class Hi{async getPremiumPlans(){try{const{data:s}=await y.from("admin_settings").select("setting_value").eq("setting_key","premium_monthly_price").single(),r=parseFloat(s?.setting_value||"299"),a=r*12*.8;return[{id:"monthly",name:"Monthly Premium",price:r,duration:1,features:["Generate referral codes","Earn ₹100 per referral","Access to premium products","Priority customer support","Special discounts","Basic wallet access"]},{id:"yearly",name:"Yearly Premium",price:a,duration:12,features:["All monthly features","20% discount (2 months free)","Extended referral bonuses","VIP customer support","Exclusive product access","Advanced analytics"],popular:!0}]}catch(s){return console.error("Error getting premium plans:",s),[]}}async isPremiumActive(s){try{const{data:r}=await y.from("users").select("is_premium, premium_lifetime_access").eq("id",s).single();return r?.is_premium?r.premium_lifetime_access===!0:!1}catch(r){return console.error("Error checking premium status:",r),!1}}async subscribeToPremium(s,r,a,l,n){try{const c=(await this.getPremiumPlans()).find(m=>m.id===r);if(!c)throw new Error("Invalid plan selected");const{data:o,error:d}=await y.rpc("purchase_premium_subscription",{p_user_id:s,p_amount:c.price,p_payment_method:a,p_payment_id:l,p_transaction_id:n});if(d)throw console.error("Premium subscription error:",d),new Error(d.message);if(!o?.success)throw new Error(o?.message||"Failed to activate premium subscription");return console.log("Phase 1 Premium Subscription:",{subscriptionId:o.subscription_id,expiresAt:o.expires_at,amountPaid:o.amount_paid,refundEligible:o.refund_eligible,policy:"NO_REFUNDS_ALLOWED"}),await dr.completeReferral(s),await this.ensureReferralCodeForPremiumUser(s),!0}catch(i){return console.error("Error subscribing to premium:",i),!1}}async expirePremium(s){try{await y.from("users").update({is_premium:!1,premium_lifetime_access:!1}).eq("id",s),await y.from("premium_subscriptions").update({payment_status:"cancelled"}).eq("user_id",s).eq("payment_status","completed")}catch(r){console.error("Error expiring premium:",r)}}async ensureReferralCodeForPremiumUser(s){try{const{data:r,error:a}=await y.from("users").select("id, email, referral_code, is_premium").eq("id",s).single();if(a||!r){console.error("Error fetching user for referral code generation:",a);return}if(!r.is_premium){console.log("User is not premium, skipping referral code generation");return}if(r.referral_code){console.log("User already has referral code:",r.referral_code);return}const l=await Ze.generateAndAssignForPremiumUser(r.id,r.email);l.success?console.log("✅ Referral code generated for premium user:",{userId:r.id,email:r.email,referralCode:l.referralCode}):console.error("❌ Failed to generate referral code:",l.error)}catch(r){console.error("Error in ensureReferralCodeForPremiumUser:",r)}}async getUserSubscription(s){try{const{data:r,error:a}=await y.from("premium_subscriptions").select("*").eq("user_id",s).eq("status","active").order("created_at",{ascending:!1}).limit(1).single();return a?null:r}catch(r){return console.error("Error getting user subscription:",r),null}}async getSubscriptionHistory(s){try{const{data:r,error:a}=await y.from("premium_subscriptions").select("*").eq("user_id",s).order("created_at",{ascending:!1});if(a)throw a;return r||[]}catch(r){return console.error("Error getting subscription history:",r),[]}}async cancelSubscription(s){try{const{error:r}=await y.from("premium_subscriptions").update({payment_status:"cancelled"}).eq("user_id",s).eq("payment_status","completed");if(r)throw r;return await y.from("users").update({is_premium:!1,premium_lifetime_access:!1,ewallet_unlocked:!1,wallet_unlocked:!1}).eq("id",s),!0}catch(r){return console.error("Error cancelling subscription:",r),!1}}async getPremiumStats(){try{const{data:s}=await y.from("premium_subscriptions").select("id",{count:"exact"}),{data:r}=await y.from("premium_subscriptions").select("id",{count:"exact"}).eq("status","active"),a=new Date;a.setDate(a.getDate()-30);const{data:l}=await y.from("premium_subscriptions").select("amount").gte("created_at",a.toISOString()),n=new Date;n.setFullYear(n.getFullYear()-1);const{data:i}=await y.from("premium_subscriptions").select("amount").gte("created_at",n.toISOString()),{data:c}=await y.from("users").select("id",{count:"exact"}),o=l?.reduce((h,p)=>h+p.amount,0)||0,d=i?.reduce((h,p)=>h+p.amount,0)||0,m=c?.length?(r?.length||0)/c.length*100:0;return{totalSubscribers:s?.length||0,activeSubscribers:r?.length||0,monthlyRevenue:o,yearlyRevenue:d,conversionRate:m}}catch(s){return console.error("Error getting premium stats:",s),{totalSubscribers:0,activeSubscribers:0,monthlyRevenue:0,yearlyRevenue:0,conversionRate:0}}}async processPremiumUpgrade(s,r,a){try{const l=`TXN_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,n=!0;return await this.subscribeToPremium(s,r,a.gateway,l)?{success:!0,transactionId:l}:{success:!1,error:"Failed to activate premium subscription"}}catch(l){return console.error("Error processing premium upgrade:",l),{success:!1,error:"Payment processing failed"}}}}const Jr=new Hi,Ki=()=>{const{user:t,refreshUser:s}=J(),[r,a]=f.useState("overview"),[l,n]=f.useState(""),[i,c]=f.useState({totalReferrals:0,completedReferrals:0,pendingReferrals:0,totalEarnings:0,premiumReferrals:0,ewalletUnlocked:!1,specialStatus:!1}),[o,d]=f.useState({current:0,required:3,unlocked:!1,progress:0}),[m,h]=f.useState(!0),[p,x]=f.useState(!1),[g,u]=f.useState(!1);f.useEffect(()=>{t?.id&&(j(),t.is_premium?b():h(!1))},[t?.id]);const j=async()=>{if(t?.id)try{const w=await Jr.isPremiumActive(t.id);u(w)}catch(w){console.error("Error checking premium status:",w),u(!1)}},b=async()=>{if(t?.id)try{h(!0),await s();const w=await dr.getReferralStats(t.id);c(w);const S=await Vi.getUnlockProgress(t.id);d(S),t.referral_code&&n(t.referral_code),console.log("EnhancedReferralPage loaded stats:",{totalReferrals:w.totalReferrals,premiumReferrals:w.premiumReferrals,totalEarnings:w.totalEarnings,ewalletUnlocked:w.ewalletUnlocked,unlockProgress:S})}catch(w){console.error("Error loading referral data:",w),k.error("Failed to load referral data")}finally{h(!1)}},N=async()=>{if(!t?.id)return;if(!await Jr.isPremiumActive(t.id)){k.error("Only premium users can generate referral codes");return}x(!0);try{const S=await dr.generateReferralCode(t.id);S?(n(S),k.success("Referral code generated successfully!")):k.error("Failed to generate referral code")}catch(S){console.error("Error generating referral code:",S),k.error("Failed to generate referral code")}finally{x(!1)}},P=()=>{l&&(navigator.clipboard.writeText(l),k.success("Referral code copied to clipboard!"))},A=()=>{if(l){const w=`${window.location.origin}/register?ref=${l}`;navigator.clipboard.writeText(w),k.success("Referral link copied to clipboard!")}},E=()=>e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-6 border border-green-200",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 flex items-center gap-2",children:[e.jsx(ce,{className:"w-5 h-5 text-green-600"}),"Your Referral Code"]}),i.specialStatus&&e.jsxs("div",{className:"flex items-center gap-1 bg-purple-100 text-purple-800 px-2 py-1 rounded-full text-sm",children:[e.jsx(L,{className:"w-4 h-4"}),"Special Status"]})]}),l?e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"bg-white rounded-lg p-4 border-2 border-dashed border-green-300",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-mono font-bold text-green-600 mb-2",children:l}),e.jsxs("div",{className:"flex justify-center gap-2",children:[e.jsxs("button",{onClick:P,className:"flex items-center gap-1 bg-green-600 text-white px-3 py-1 rounded-md text-sm hover:bg-green-700",children:[e.jsx(rr,{className:"w-4 h-4"}),"Copy Code"]}),e.jsxs("button",{onClick:A,className:"flex items-center gap-1 bg-blue-600 text-white px-3 py-1 rounded-md text-sm hover:bg-blue-700",children:[e.jsx(tr,{className:"w-4 h-4"}),"Share Link"]})]})]})}),e.jsx("div",{className:"text-sm text-gray-600 text-center",children:"Share this code with friends to earn ₹100 per premium referral!"})]}):e.jsxs("div",{className:"text-center",children:[e.jsx("button",{onClick:N,disabled:p,className:"bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 disabled:opacity-50",children:p?"Generating...":"Generate Referral Code"}),e.jsx("p",{className:"text-sm text-gray-600 mt-2",children:"Premium membership required to generate referral codes"})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[e.jsx("div",{className:"bg-white rounded-lg p-4 border border-gray-200",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Total Referrals"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900",children:i.totalReferrals})]}),e.jsx(H,{className:"w-8 h-8 text-blue-600"})]})}),e.jsx("div",{className:"bg-white rounded-lg p-4 border border-gray-200",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Premium Referrals"}),e.jsx("p",{className:"text-2xl font-bold text-green-600",children:i.premiumReferrals})]}),e.jsx(L,{className:"w-8 h-8 text-green-600"})]})}),e.jsx("div",{className:"bg-white rounded-lg p-4 border border-gray-200",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Total Earnings"}),e.jsxs("p",{className:"text-2xl font-bold text-purple-600",children:["₹",i.totalEarnings]})]}),e.jsx(ze,{className:"w-8 h-8 text-purple-600"})]})}),e.jsx("div",{className:"bg-white rounded-lg p-4 border border-gray-200",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:"E-Wallet Status"}),e.jsx("p",{className:"text-sm font-semibold text-green-600",children:"Unlocked"})]}),e.jsx(Xs,{className:"w-8 h-8 text-green-600"})]})})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"bg-white rounded-lg p-4 border border-gray-200",children:[e.jsxs("h4",{className:"font-semibold text-gray-900 mb-3 flex items-center gap-2",children:[e.jsx(V,{className:"w-5 h-5 text-green-600"}),"Completed Referrals"]}),e.jsx("div",{className:"text-2xl font-bold text-green-600 mb-1",children:i.completedReferrals}),e.jsx("p",{className:"text-sm text-gray-600",children:"Users who became premium members"})]}),e.jsxs("div",{className:"bg-white rounded-lg p-4 border border-gray-200",children:[e.jsxs("h4",{className:"font-semibold text-gray-900 mb-3 flex items-center gap-2",children:[e.jsx(Ne,{className:"w-5 h-5 text-yellow-600"}),"Pending Referrals"]}),e.jsx("div",{className:"text-2xl font-bold text-yellow-600 mb-1",children:i.pendingReferrals}),e.jsx("p",{className:"text-sm text-gray-600",children:"Users who haven't upgraded to premium yet"})]})]})]}),_=()=>e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"bg-green-50 border border-green-200 rounded-lg p-6",children:[e.jsxs("h3",{className:"text-lg font-semibold text-green-900 mb-4 flex items-center gap-2",children:[e.jsx(K,{className:"w-5 h-5 text-green-600"}),"E-Wallet Status"]}),e.jsx("div",{className:"space-y-4",children:e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"flex items-center justify-center gap-2 text-green-600 mb-3",children:[e.jsx(Xs,{className:"w-8 h-8"}),e.jsx("span",{className:"text-xl font-bold",children:"E-Wallet Unlocked!"})]}),e.jsx("p",{className:"text-green-800",children:"Your e-wallet is automatically unlocked as a premium member."}),e.jsx("p",{className:"text-sm text-green-600 mt-2",children:"Enjoy full access to all wallet features including transactions, withdrawals, and referral bonuses."})]})})]}),e.jsxs("div",{className:"bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-6 border border-blue-200",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2",children:[e.jsx(Ce,{className:"w-5 h-5 text-yellow-500"}),"Referral Benefits"]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsx("h4",{className:"font-medium text-gray-900",children:"Current Benefits:"}),e.jsxs("ul",{className:"space-y-2 text-sm",children:[e.jsxs("li",{className:"flex items-center gap-2",children:[e.jsx(V,{className:"w-4 h-4 text-green-500"}),"₹100 per premium referral"]}),e.jsxs("li",{className:"flex items-center gap-2",children:[e.jsx(V,{className:"w-4 h-4 text-green-500"}),"Unlimited referral levels"]}),e.jsxs("li",{className:"flex items-center gap-2",children:[e.jsx(V,{className:"w-4 h-4 text-green-500"}),"Real-time bonus tracking"]})]})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("h4",{className:"font-medium text-gray-900",children:"Unlock at 3 Referrals:"}),e.jsxs("ul",{className:"space-y-2 text-sm",children:[e.jsxs("li",{className:"flex items-center gap-2",children:[e.jsx(L,{className:"w-4 h-4 text-purple-500"}),"Full e-wallet access"]}),e.jsxs("li",{className:"flex items-center gap-2",children:[e.jsx(L,{className:"w-4 h-4 text-purple-500"}),"Zero withdrawal fees"]}),e.jsxs("li",{className:"flex items-center gap-2",children:[e.jsx(L,{className:"w-4 h-4 text-purple-500"}),"Special referral status"]})]})]})]})]})]});return m?e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"})}):!g&&!t?.is_premium?e.jsxs("div",{className:"max-w-6xl mx-auto p-6 relative",children:[e.jsxs("div",{className:"blur-sm pointer-events-none select-none",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Multi-Level Referral System"}),e.jsx("p",{className:"text-gray-600",children:"Refer friends and earn unlimited bonuses through our advanced referral program"})]}),e.jsx("div",{className:"border-b border-gray-200 mb-6",children:e.jsx("nav",{className:"-mb-px flex space-x-8",children:[{id:"overview",label:"Overview",icon:Be},{id:"progress",label:"Progress",icon:K}].map(w=>e.jsxs("button",{className:"flex items-center gap-2 py-2 px-1 border-b-2 font-medium text-sm border-green-500 text-green-600",children:[e.jsx(w.icon,{className:"w-4 h-4"}),w.label]},w.id))})}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-6 border border-green-200",children:[e.jsx("div",{className:"flex items-center justify-between mb-4",children:e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 flex items-center gap-2",children:[e.jsx(ce,{className:"w-5 h-5 text-green-600"}),"Your Referral Code"]})}),e.jsx("div",{className:"bg-white rounded-lg p-4 border-2 border-dashed border-green-300",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-mono font-bold text-green-600 mb-2",children:"PREMIUM24"}),e.jsxs("div",{className:"flex justify-center gap-2",children:[e.jsxs("button",{className:"flex items-center gap-1 bg-green-600 text-white px-3 py-1 rounded-md text-sm",children:[e.jsx(rr,{className:"w-4 h-4"}),"Copy Code"]}),e.jsxs("button",{className:"flex items-center gap-1 bg-blue-600 text-white px-3 py-1 rounded-md text-sm",children:[e.jsx(tr,{className:"w-4 h-4"}),"Share Link"]})]})]})})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[e.jsx("div",{className:"bg-white rounded-lg p-4 border border-gray-200",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Total Referrals"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900",children:"15"})]}),e.jsx(H,{className:"w-8 h-8 text-blue-600"})]})}),e.jsx("div",{className:"bg-white rounded-lg p-4 border border-gray-200",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Premium Referrals"}),e.jsx("p",{className:"text-2xl font-bold text-green-600",children:"8"})]}),e.jsx(L,{className:"w-8 h-8 text-green-600"})]})}),e.jsx("div",{className:"bg-white rounded-lg p-4 border border-gray-200",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Total Earnings"}),e.jsx("p",{className:"text-2xl font-bold text-purple-600",children:"₹1,200"})]}),e.jsx(ze,{className:"w-8 h-8 text-purple-600"})]})}),e.jsx("div",{className:"bg-white rounded-lg p-4 border border-gray-200",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:"E-Wallet Status"}),e.jsx("p",{className:"text-sm font-semibold text-gray-900",children:"Unlocked"})]}),e.jsx(Xs,{className:"w-8 h-8 text-green-600"})]})})]})]})]}),e.jsx("div",{className:"absolute inset-0 flex items-center justify-center bg-white/80 backdrop-blur-sm",children:e.jsx("div",{className:"bg-white rounded-2xl shadow-2xl p-8 max-w-md w-full mx-4 border border-gray-200",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"w-16 h-16 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4",children:e.jsx(L,{className:"w-8 h-8 text-white"})}),e.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Premium Required"}),e.jsx("p",{className:"text-gray-600 mb-6",children:"Unlock the powerful multi-level referral system and start earning unlimited bonuses!"}),e.jsxs("div",{className:"space-y-4 mb-6",children:[e.jsxs("div",{className:"flex items-center gap-3 text-left",children:[e.jsx("div",{className:"w-6 h-6 bg-green-100 rounded-full flex items-center justify-center",children:e.jsx(V,{className:"w-4 h-4 text-green-600"})}),e.jsx("span",{className:"text-sm text-gray-700",children:"Generate unique referral codes"})]}),e.jsxs("div",{className:"flex items-center gap-3 text-left",children:[e.jsx("div",{className:"w-6 h-6 bg-green-100 rounded-full flex items-center justify-center",children:e.jsx(V,{className:"w-4 h-4 text-green-600"})}),e.jsx("span",{className:"text-sm text-gray-700",children:"Earn ₹100 per premium referral"})]}),e.jsxs("div",{className:"flex items-center gap-3 text-left",children:[e.jsx("div",{className:"w-6 h-6 bg-green-100 rounded-full flex items-center justify-center",children:e.jsx(V,{className:"w-4 h-4 text-green-600"})}),e.jsx("span",{className:"text-sm text-gray-700",children:"Unlock e-wallet after 3 referrals"})]}),e.jsxs("div",{className:"flex items-center gap-3 text-left",children:[e.jsx("div",{className:"w-6 h-6 bg-green-100 rounded-full flex items-center justify-center",children:e.jsx(V,{className:"w-4 h-4 text-green-600"})}),e.jsx("span",{className:"text-sm text-gray-700",children:"Multi-level bonus system"})]})]}),e.jsxs(R,{to:"/dashboard/premium",className:"w-full bg-gradient-to-r from-purple-600 to-blue-600 text-white py-3 px-6 rounded-lg font-semibold hover:from-purple-700 hover:to-blue-700 transition-all duration-200 flex items-center justify-center gap-2 group",children:[e.jsx(Te,{className:"w-5 h-5"}),"Upgrade to Premium",e.jsx(Se,{className:"w-4 h-4 group-hover:translate-x-1 transition-transform"})]}),e.jsx("p",{className:"text-xs text-gray-500 mt-3",children:"Only ₹299/month • Non-refundable • Start earning immediately"})]})})})]}):e.jsxs("div",{className:"max-w-6xl mx-auto p-6",children:[e.jsxs("div",{className:"mb-6 flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Multi-Level Referral System"}),e.jsx("p",{className:"text-gray-600",children:"Refer friends and earn unlimited bonuses through our advanced referral program"})]}),e.jsx("button",{onClick:b,className:"p-3 bg-green-100 hover:bg-green-200 rounded-lg transition-colors",title:"Refresh data",children:e.jsx(le,{className:"h-5 w-5 text-green-600"})})]}),e.jsx("div",{className:"border-b border-gray-200 mb-6",children:e.jsx("nav",{className:"-mb-px flex space-x-8",children:[{id:"overview",label:"Overview",icon:Be},{id:"progress",label:"Progress",icon:K}].map(w=>e.jsxs("button",{onClick:()=>a(w.id),className:`flex items-center gap-2 py-2 px-1 border-b-2 font-medium text-sm ${r===w.id?"border-green-500 text-green-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:[e.jsx(w.icon,{className:"w-4 h-4"}),w.label]},w.id))})}),r==="overview"&&E(),r==="progress"&&_()]})};function Ji(){const{user:t,setUser:s}=J(),[r,a]=f.useState(!1),l=[{icon:e.jsx(Ce,{className:"h-6 w-6"}),title:"Exclusive Products",description:"Access to premium-only herbal products and limited editions"},{icon:e.jsx(H,{className:"h-6 w-6"}),title:"Referral Program",description:"Earn money by referring friends and family"},{icon:e.jsx(ce,{className:"h-6 w-6"}),title:"Special Discounts",description:"Get exclusive discounts on all products"},{icon:e.jsx(Te,{className:"h-6 w-6"}),title:"Priority Support",description:"Get priority customer support and faster delivery"}],n=async()=>{if(t){a(!0);try{const{data:i,error:c}=await y.from("users").update({is_premium:!0,premium_lifetime_access:!0,premium_purchase_confirmed:!0,premium_no_refund_accepted:!0,premium_purchased_at:new Date().toISOString(),ewallet_unlocked:!0,wallet_unlocked:!0}).eq("id",t.id).select().single();if(c)throw c;const o=await Ze.generateAndAssignForPremiumUser(t.id,t.email);o.success?(s({...t,...i,referral_code:o.referralCode}),k.success(`Welcome to Lifetime Premium! 🎉
Your referral code: ${o.referralCode}`)):(s({...t,...i}),k.success(`Welcome to Lifetime Premium! 🎉
(Referral code will be generated shortly)`))}catch(i){console.error("Error upgrading to premium:",i),k.error("Failed to upgrade to premium")}finally{a(!1)}}};return t?.is_premium?e.jsxs("div",{children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsxs("div",{className:"inline-flex items-center gap-2 bg-gradient-to-r from-yellow-400 to-yellow-600 text-white px-6 py-3 rounded-full text-lg font-semibold mb-4",children:[e.jsx(L,{className:"h-6 w-6 fill-current"}),"Premium Member"]}),e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"You're a Premium Member!"}),e.jsx("p",{className:"text-gray-600",children:"Enjoy all the exclusive benefits and features available to premium members."})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8",children:l.map((i,c)=>e.jsx("div",{className:"bg-white p-6 rounded-lg shadow-sm border border-gray-200",children:e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("div",{className:"p-3 bg-yellow-100 text-yellow-600 rounded-lg",children:i.icon}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold text-gray-900 mb-2",children:i.title}),e.jsx("p",{className:"text-gray-600",children:i.description})]})]})},c))}),e.jsxs("div",{className:"bg-gradient-to-r from-yellow-50 to-yellow-100 p-6 rounded-lg border border-yellow-200",children:[e.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Premium Status"}),e.jsx("p",{className:"text-gray-700 mb-4",children:"Your lifetime premium membership is active - no expiration!"}),t.premium_purchased_at&&e.jsxs("p",{className:"text-sm text-gray-600",children:["Purchased on: ",new Date(t.premium_purchased_at).toLocaleDateString()]}),e.jsx("div",{className:"mt-2",children:e.jsx("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:"Lifetime Access"})})]})]}):e.jsxs("div",{children:[e.jsxs("div",{className:"text-center mb-12",children:[e.jsxs("div",{className:"inline-flex items-center gap-2 bg-gradient-to-r from-yellow-400 to-yellow-600 text-white px-6 py-3 rounded-full text-lg font-semibold mb-6",children:[e.jsx(L,{className:"h-6 w-6 fill-current"}),"Upgrade to Premium"]}),e.jsx("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"Unlock Exclusive Benefits"}),e.jsx("p",{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:"Get access to premium products, referral program, and exclusive discounts with our premium membership."})]}),e.jsx("div",{className:"max-w-md mx-auto mb-12",children:e.jsxs("div",{className:"bg-white rounded-2xl shadow-xl border-2 border-yellow-200 overflow-hidden",children:[e.jsxs("div",{className:"bg-gradient-to-r from-yellow-400 to-yellow-600 text-white p-6 text-center",children:[e.jsx(L,{className:"h-12 w-12 mx-auto mb-4 fill-current"}),e.jsx("h2",{className:"text-2xl font-bold mb-2",children:"Premium Membership"}),e.jsx("div",{className:"text-4xl font-bold mb-2",children:"₹999"}),e.jsx("p",{className:"text-yellow-100",children:"per year"})]}),e.jsxs("div",{className:"p-6",children:[e.jsx("ul",{className:"space-y-4 mb-8",children:l.map((i,c)=>e.jsxs("li",{className:"flex items-start gap-3",children:[e.jsx(Us,{className:"h-5 w-5 text-green-500 mt-0.5 flex-shrink-0"}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-gray-900",children:i.title}),e.jsx("p",{className:"text-sm text-gray-600",children:i.description})]})]},c))}),e.jsx("button",{onClick:n,disabled:r,className:"w-full bg-gradient-to-r from-yellow-400 to-yellow-600 text-white py-4 rounded-lg font-semibold text-lg hover:from-yellow-500 hover:to-yellow-700 transition-all disabled:opacity-50 disabled:cursor-not-allowed",children:r?"Processing...":"Upgrade Now"}),e.jsx("p",{className:"text-xs text-gray-500 mt-4 text-center",children:"In a real application, this would integrate with Stripe or Razorpay for secure payments."})]})]})}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:l.map((i,c)=>e.jsx("div",{className:"bg-white p-8 rounded-lg shadow-sm border border-gray-200",children:e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("div",{className:"p-4 bg-yellow-100 text-yellow-600 rounded-xl",children:i.icon}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:i.title}),e.jsx("p",{className:"text-gray-600 leading-relaxed",children:i.description})]})]})},c))})]})}const Xi=()=>{const{user:t,refreshUser:s}=J(),[r,a]=f.useState({aadhaarFront:null,aadhaarBack:null,panCard:null,accountNumber:"",ifscCode:"",accountHolderName:"",bankName:""}),[l,n]=f.useState(!1),[i,c]=f.useState(!1),o=()=>{switch(t?.kyc_status||"pending"){case"approved":return{icon:V,color:"text-green-600",bgColor:"bg-green-100",text:"KYC Approved",description:"Your account is fully verified"};case"under_review":return{icon:Ne,color:"text-yellow-600",bgColor:"bg-yellow-100",text:"Under Review",description:"We're reviewing your documents"};case"rejected":return{icon:Ns,color:"text-red-600",bgColor:"bg-red-100",text:"KYC Rejected",description:"Please resubmit your documents"};default:return{icon:we,color:"text-orange-600",bgColor:"bg-orange-100",text:"KYC Pending",description:"Complete your verification"}}},d=(x,g)=>{const u=x.target.files?.[0];if(u){if(u.size>5*1024*1024){k.error("File size must be less than 5MB");return}if(!["image/jpeg","image/jpg","image/png","application/pdf"].includes(u.type)){k.error("Only JPG, PNG, and PDF files are allowed");return}a(b=>({...b,[g]:u}))}},m=async(x,g)=>{const u=x.name.split(".").pop(),j=`${t?.id}/${g}_${Date.now()}.${u}`,{data:b,error:N}=await y.storage.from("kyc-documents").upload(j,x);if(N)throw new Error(`Failed to upload ${g}: ${N.message}`);const{data:{publicUrl:P}}=y.storage.from("kyc-documents").getPublicUrl(j);return P},h=async x=>{if(x.preventDefault(),!r.aadhaarFront||!r.aadhaarBack||!r.panCard){k.error("Please upload all required documents");return}if(!r.accountNumber||!r.ifscCode||!r.accountHolderName||!r.bankName){k.error("Please fill in all banking details");return}n(!0),c(!0);try{const[g,u,j]=await Promise.all([m(r.aadhaarFront,"aadhaar_front"),m(r.aadhaarBack,"aadhaar_back"),m(r.panCard,"pan_card")]);c(!1);const{error:b}=await y.from("users").update({aadhaar_front_url:g,aadhaar_back_url:u,pan_card_url:j,account_number_encrypted:r.accountNumber,ifsc_code:r.ifscCode,account_holder_name:r.accountHolderName,bank_name:r.bankName,kyc_status:"under_review",kyc_submitted_at:new Date().toISOString()}).eq("id",t?.id);if(b)throw new Error(b.message);k.success("KYC documents submitted successfully! We will review them within 24-48 hours."),await s()}catch(g){console.error("KYC submission error:",g),k.error(g instanceof Error?g.message:"Failed to submit KYC documents")}finally{n(!1),c(!1)}},p=o();return t?.kyc_status==="approved"?e.jsx("div",{className:"max-w-2xl mx-auto p-6",children:e.jsxs("div",{className:`${p.bgColor} rounded-xl p-6 text-center`,children:[e.jsx(p.icon,{className:`w-16 h-16 ${p.color} mx-auto mb-4`}),e.jsx("h2",{className:`text-2xl font-bold ${p.color} mb-2`,children:p.text}),e.jsx("p",{className:"text-gray-600",children:p.description})]})}):t?.kyc_status==="under_review"?e.jsx("div",{className:"max-w-2xl mx-auto p-6",children:e.jsxs("div",{className:`${p.bgColor} rounded-xl p-6 text-center`,children:[e.jsx(p.icon,{className:`w-16 h-16 ${p.color} mx-auto mb-4`}),e.jsx("h2",{className:`text-2xl font-bold ${p.color} mb-2`,children:p.text}),e.jsx("p",{className:"text-gray-600 mb-4",children:p.description}),e.jsxs("p",{className:"text-sm text-gray-500",children:["Submitted on: ",t?.kyc_submitted_at?new Date(t.kyc_submitted_at).toLocaleDateString():"Unknown"]})]})}):e.jsxs("div",{className:"max-w-2xl mx-auto p-6",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx(ae,{className:"w-16 h-16 text-green-600 mx-auto mb-4"}),e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Complete KYC Verification"}),e.jsx("p",{className:"text-gray-600",children:"Upload your documents to verify your identity and unlock all features"})]}),e.jsx("div",{className:`${p.bgColor} rounded-xl p-4 mb-8`,children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(p.icon,{className:`w-6 h-6 ${p.color}`}),e.jsxs("div",{children:[e.jsx("h3",{className:`font-semibold ${p.color}`,children:p.text}),e.jsx("p",{className:"text-sm text-gray-600",children:p.description})]})]})}),e.jsxs("form",{onSubmit:h,className:"space-y-8",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Upload Documents"}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Aadhaar Card (Front) *"}),e.jsxs("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-green-400 transition-colors relative",children:[r.aadhaarFront?e.jsxs("div",{className:"space-y-2",children:[e.jsx(he,{className:"w-8 h-8 text-green-600 mx-auto"}),e.jsx("p",{className:"text-sm text-gray-600",children:r.aadhaarFront.name}),e.jsx("button",{type:"button",onClick:()=>a(x=>({...x,aadhaarFront:null})),className:"text-red-600 hover:text-red-700 text-sm",children:"Remove"})]}):e.jsxs("div",{className:"space-y-2",children:[e.jsx(qe,{className:"w-8 h-8 text-gray-400 mx-auto"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Click to upload or drag and drop"}),e.jsx("p",{className:"text-xs text-gray-500",children:"JPG, PNG, PDF (max 5MB)"})]}),e.jsx("input",{type:"file",accept:".jpg,.jpeg,.png,.pdf",onChange:x=>d(x,"aadhaarFront"),className:"absolute inset-0 w-full h-full opacity-0 cursor-pointer"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Aadhaar Card (Back) *"}),e.jsxs("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-green-400 transition-colors relative",children:[r.aadhaarBack?e.jsxs("div",{className:"space-y-2",children:[e.jsx(he,{className:"w-8 h-8 text-green-600 mx-auto"}),e.jsx("p",{className:"text-sm text-gray-600",children:r.aadhaarBack.name}),e.jsx("button",{type:"button",onClick:()=>a(x=>({...x,aadhaarBack:null})),className:"text-red-600 hover:text-red-700 text-sm",children:"Remove"})]}):e.jsxs("div",{className:"space-y-2",children:[e.jsx(qe,{className:"w-8 h-8 text-gray-400 mx-auto"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Click to upload or drag and drop"}),e.jsx("p",{className:"text-xs text-gray-500",children:"JPG, PNG, PDF (max 5MB)"})]}),e.jsx("input",{type:"file",accept:".jpg,.jpeg,.png,.pdf",onChange:x=>d(x,"aadhaarBack"),className:"absolute inset-0 w-full h-full opacity-0 cursor-pointer"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"PAN Card *"}),e.jsxs("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-green-400 transition-colors relative",children:[r.panCard?e.jsxs("div",{className:"space-y-2",children:[e.jsx(he,{className:"w-8 h-8 text-green-600 mx-auto"}),e.jsx("p",{className:"text-sm text-gray-600",children:r.panCard.name}),e.jsx("button",{type:"button",onClick:()=>a(x=>({...x,panCard:null})),className:"text-red-600 hover:text-red-700 text-sm",children:"Remove"})]}):e.jsxs("div",{className:"space-y-2",children:[e.jsx(qe,{className:"w-8 h-8 text-gray-400 mx-auto"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Click to upload or drag and drop"}),e.jsx("p",{className:"text-xs text-gray-500",children:"JPG, PNG, PDF (max 5MB)"})]}),e.jsx("input",{type:"file",accept:".jpg,.jpeg,.png,.pdf",onChange:x=>d(x,"panCard"),className:"absolute inset-0 w-full h-full opacity-0 cursor-pointer"})]})]})]})]}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Banking Details"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Account Number *"}),e.jsx("input",{type:"text",value:r.accountNumber,onChange:x=>a(g=>({...g,accountNumber:x.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent",placeholder:"Enter account number",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"IFSC Code *"}),e.jsx("input",{type:"text",value:r.ifscCode,onChange:x=>a(g=>({...g,ifscCode:x.target.value.toUpperCase()})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent",placeholder:"Enter IFSC code",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Account Holder Name *"}),e.jsx("input",{type:"text",value:r.accountHolderName,onChange:x=>a(g=>({...g,accountHolderName:x.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent",placeholder:"Enter account holder name",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Bank Name *"}),e.jsx("input",{type:"text",value:r.bankName,onChange:x=>a(g=>({...g,bankName:x.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent",placeholder:"Enter bank name",required:!0})]})]})]}),e.jsx("button",{type:"submit",disabled:l||!r.aadhaarFront||!r.aadhaarBack||!r.panCard,className:"w-full bg-green-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors touch-target",children:i?"Uploading Documents...":l?"Submitting...":"Submit KYC Documents"})]})]})};function Qi(){const t=js(),{user:s,logout:r}=J(),{balance:a,fetchBalance:l}=Ct(),[n,i]=f.useState(!1);f.useEffect(()=>{s?.id&&l(s.id)},[s?.id,l]);const c=[{name:"Overview",href:"/dashboard",icon:pr,current:t.pathname==="/dashboard"},{name:"Profile",href:"/dashboard/profile",icon:Ee,current:t.pathname==="/dashboard/profile"},{name:"Orders",href:"/dashboard/orders",icon:Z,current:t.pathname==="/dashboard/orders"},{name:"Premium",href:"/dashboard/premium",icon:L,current:t.pathname==="/dashboard/premium"},{name:"Referrals",href:"/dashboard/referrals",icon:H,current:t.pathname==="/dashboard/referrals"},...s?.kyc_status!=="approved"?[{name:"Complete KYC",href:"/dashboard/kyc",icon:ae,current:t.pathname==="/dashboard/kyc"}]:[],...s?.is_premium?[{name:"Wallet",href:"/dashboard/wallet",icon:K,current:t.pathname==="/dashboard/wallet"}]:[]];return s?e.jsx("div",{className:"min-h-screen bg-gray-50",children:e.jsxs("div",{className:"flex flex-col lg:flex-row",children:[e.jsxs("div",{className:"dashboard-mobile-header lg:hidden bg-white shadow-sm p-4 flex items-center justify-between relative z-50",children:[e.jsxs(R,{to:"/",className:"dashboard-logo flex items-center gap-2 hover:opacity-80 transition-opacity touch-target",onClick:()=>i(!1),children:[e.jsx("div",{className:"logo-icon w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center transition-transform",children:e.jsx("span",{className:"text-white font-bold text-sm",children:"🌿"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold text-gray-900 text-sm",children:"Start Juicce"}),e.jsx("p",{className:"text-xs text-gray-500",children:"Dashboard"})]})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[s.is_premium&&e.jsxs("div",{className:"flex items-center gap-1 bg-gradient-to-r from-yellow-400 to-yellow-600 text-white px-2 py-1 rounded-full text-xs font-medium",children:[e.jsx(Ce,{className:"h-3 w-3 fill-current"}),"Premium"]}),e.jsx("button",{onClick:o=>{o.preventDefault(),o.stopPropagation(),console.log("Hamburger clicked, current state:",n),i(!n)},className:"dashboard-hamburger relative z-50 p-3 text-gray-800 hover:text-green-600 hover:bg-green-50 rounded-lg transition-all duration-200 touch-target bg-white border-2 border-gray-300 hover:border-green-400 shadow-md hover:shadow-lg active:scale-95 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2",style:{minWidth:"44px",minHeight:"44px",backgroundColor:"#ffffff",border:"2px solid #10b981",boxShadow:"0 4px 12px rgba(16, 185, 129, 0.3)",zIndex:9999},type:"button","aria-label":n?"Close menu":"Open menu",children:n?e.jsx(oe,{className:"h-6 w-6 mx-auto"}):e.jsx(xr,{className:"h-6 w-6 mx-auto"})})]})]}),e.jsx("div",{className:`${n?"block":"hidden"} lg:block w-full lg:w-64 bg-white shadow-sm lg:min-h-screen relative z-40 lg:z-auto`,children:e.jsxs("div",{className:"p-4 lg:p-6",children:[e.jsxs(R,{to:"/",className:"dashboard-logo flex items-center gap-3 mb-6 p-3 rounded-lg hover:bg-gray-50 transition-colors group touch-target",onClick:()=>i(!1),children:[e.jsx("div",{className:"logo-icon w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center group-hover:scale-105 transition-transform",children:e.jsx("span",{className:"text-white font-bold text-lg",children:"🌿"})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h2",{className:"text-lg font-bold text-gray-900 group-hover:text-green-600 transition-colors",children:"Start Juicce"}),e.jsx("p",{className:"text-xs text-gray-500",children:"Back to Store"})]}),e.jsx(Cr,{className:"h-4 w-4 text-gray-400 group-hover:text-green-600 transition-colors"})]}),e.jsxs("div",{className:"hidden lg:flex items-center gap-3 mb-6",children:[e.jsx("div",{className:"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center",children:e.jsx(Ee,{className:"h-6 w-6 text-green-600"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold text-gray-900",children:s.full_name}),e.jsx("div",{className:"flex items-center gap-2",children:s.is_premium&&e.jsxs("div",{className:"flex items-center gap-1 bg-gradient-to-r from-yellow-400 to-yellow-600 text-white px-2 py-1 rounded-full text-xs font-medium",children:[e.jsx(Ce,{className:"h-3 w-3 fill-current"}),"Premium"]})})]})]}),s.is_premium&&e.jsx("div",{className:"bg-green-50 p-4 rounded-lg mb-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-green-600 font-medium",children:"Wallet Balance"}),e.jsxs("p",{className:"text-xl lg:text-2xl font-bold text-green-700",children:["₹",a]})]}),e.jsx(K,{className:"h-6 w-6 lg:h-8 lg:w-8 text-green-600"})]})}),!s.is_premium&&e.jsx("div",{className:"bg-gradient-to-r from-yellow-50 to-amber-50 p-4 rounded-lg mb-6 border border-yellow-200",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-yellow-700 font-medium",children:"Upgrade to Premium"}),e.jsx("p",{className:"text-xs text-yellow-600",children:"Unlock full access"})]}),e.jsx(L,{className:"h-6 w-6 lg:h-8 lg:w-8 text-yellow-600"})]})}),e.jsxs("nav",{className:"space-y-1 lg:space-y-2",children:[e.jsxs(R,{to:"/",onClick:()=>i(!1),className:"flex items-center gap-3 px-3 py-3 lg:py-2 rounded-lg text-sm font-medium text-gray-600 hover:bg-blue-50 hover:text-blue-700 transition-colors touch-target border border-blue-200 hover:border-blue-300",children:[e.jsx(Cr,{className:"h-5 w-5"}),"Back to Store"]}),c.map(o=>e.jsxs(R,{to:o.href,onClick:()=>i(!1),className:`flex items-center gap-3 px-3 py-3 lg:py-2 rounded-lg text-sm font-medium transition-colors touch-target ${o.current?"bg-green-100 text-green-700":"text-gray-600 hover:bg-gray-100 hover:text-gray-900"}`,children:[e.jsx(o.icon,{className:"h-5 w-5"}),o.name]},o.name)),e.jsxs("button",{onClick:()=>{r(),i(!1)},className:"w-full flex items-center gap-3 px-3 py-3 lg:py-2 rounded-lg text-sm font-medium text-red-600 hover:bg-red-50 transition-colors touch-target",children:[e.jsx(st,{className:"h-5 w-5"}),"Logout"]})]})]})}),n&&e.jsx("div",{className:"lg:hidden fixed inset-0 bg-black bg-opacity-50 z-30",onClick:()=>i(!1)}),e.jsx("div",{className:"flex-1 p-4 lg:p-8",children:e.jsxs(mr,{children:[e.jsx(M,{path:"/",element:e.jsx(Mi,{})}),e.jsx(M,{path:"/profile",element:e.jsx(Li,{})}),e.jsx(M,{path:"/orders",element:e.jsx(qi,{})}),e.jsx(M,{path:"/premium",element:e.jsx(Ji,{})}),e.jsx(M,{path:"/referrals",element:e.jsx(Ki,{})}),e.jsx(M,{path:"/kyc",element:e.jsx(Xi,{})}),s.is_premium&&e.jsx(e.Fragment,{children:e.jsx(M,{path:"/wallet",element:e.jsx(zi,{})})})]})})]})}):e.jsx("div",{className:"min-h-screen flex items-center justify-center p-4",children:e.jsxs("div",{className:"text-center",children:[e.jsx("h2",{className:"text-xl sm:text-2xl font-bold text-gray-900 mb-4",children:"Please login to access dashboard"}),e.jsx(R,{to:"/login",className:"bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors touch-target",children:"Login"})]})})}class Zi{constructor(){this.notifications=[],this.listeners=[],this.initializeRealTimeSubscriptions()}initializeRealTimeSubscriptions(){y.channel("admin-orders").on("postgres_changes",{event:"INSERT",schema:"public",table:"orders"},s=>this.handleNewOrder(s.new)).subscribe(),y.channel("admin-users").on("postgres_changes",{event:"INSERT",schema:"public",table:"users"},s=>this.handleNewUser(s.new)).subscribe(),y.channel("admin-kyc").on("postgres_changes",{event:"UPDATE",schema:"public",table:"users",filter:"kyc_status=eq.under_review"},s=>this.handleKYCSubmission(s.new)).subscribe(),y.channel("admin-wallet").on("postgres_changes",{event:"INSERT",schema:"public",table:"wallet_transactions"},s=>this.handleWalletTransaction(s.new)).subscribe(),y.channel("admin-products").on("postgres_changes",{event:"UPDATE",schema:"public",table:"products",filter:"stock_quantity=lte.5"},s=>this.handleLowStock(s.new)).subscribe(),y.channel("admin-referrals").on("postgres_changes",{event:"INSERT",schema:"public",table:"referrals"},s=>this.handleNewReferral(s.new)).subscribe()}handleNewOrder(s){this.addNotification({type:"order",title:"New Order Received",message:`Order #${s.id} for ₹${s.total_amount} from ${s.customer_email}`,data:{orderId:s.id,amount:s.total_amount},priority:"medium"})}handleNewUser(s){this.addNotification({type:"user",title:"New User Registration",message:`${s.full_name||s.email} just registered`,data:{userId:s.id,email:s.email},priority:"low"})}handleKYCSubmission(s){this.addNotification({type:"kyc",title:"KYC Submission",message:`${s.full_name||s.email} submitted KYC documents for review`,data:{userId:s.id,email:s.email},priority:"high"})}handleWalletTransaction(s){s.amount>1e3&&this.addNotification({type:"wallet",title:"Large Wallet Transaction",message:`₹${s.amount} ${s.type} transaction`,data:{transactionId:s.id,amount:s.amount},priority:"medium"})}handleLowStock(s){this.addNotification({type:"alert",title:"Low Stock Alert",message:`${s.name} is running low (${s.stock_quantity} left)`,data:{productId:s.id,stock:s.stock_quantity},priority:"urgent"})}handleNewReferral(s){this.addNotification({type:"referral",title:"New Referral",message:"New referral registered in the system",data:{referralId:s.id},priority:"low"})}addNotification(s){const r={id:`notif_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,type:s.type,title:s.title,message:s.message,data:s.data,read:!1,created_at:new Date().toISOString(),priority:s.priority||"medium"};this.notifications.unshift(r),this.notifications.length>50&&(this.notifications=this.notifications.slice(0,50)),this.notifyListeners()}getNotifications(){return this.notifications}getUnreadCount(){return this.notifications.filter(s=>!s.read).length}markAsRead(s){const r=this.notifications.find(a=>a.id===s);r&&(r.read=!0,this.notifyListeners())}markAllAsRead(){this.notifications.forEach(s=>s.read=!0),this.notifyListeners()}clearNotification(s){this.notifications=this.notifications.filter(r=>r.id!==s),this.notifyListeners()}clearAllNotifications(){this.notifications=[],this.notifyListeners()}subscribe(s){return this.listeners.push(s),()=>{this.listeners=this.listeners.filter(r=>r!==s)}}notifyListeners(){this.listeners.forEach(s=>s(this.notifications))}createNotification(s){this.addNotification(s)}getNotificationsByType(s){return this.notifications.filter(r=>r.type===s)}getNotificationsByPriority(s){return this.notifications.filter(r=>r.priority===s)}initializeSampleNotifications(){[{type:"order",title:"New Order Received",message:"Order #1001 for ₹2,500 from <EMAIL>",priority:"medium",data:{orderId:"1001",amount:2500}},{type:"user",title:"New User Registration",message:"John Doe just registered",priority:"low",data:{userId:"user123",email:"<EMAIL>"}},{type:"kyc",title:"KYC Submission",message:"Sarah Smith submitted KYC documents for review",priority:"high",data:{userId:"user456",email:"<EMAIL>"}},{type:"alert",title:"Low Stock Alert",message:"Green Tea Extract is running low (3 left)",priority:"urgent",data:{productId:"prod789",stock:3}},{type:"wallet",title:"Large Wallet Transaction",message:"₹5,000 credit transaction",priority:"medium",data:{transactionId:"tx123",amount:5e3}}].forEach(r=>{this.addNotification(r)})}}const ke=new Zi,Et=()=>{const[t,s]=f.useState([]),[r,a]=f.useState(0);return f.useEffect(()=>{const d=ke.subscribe(m=>{s(m),a(ke.getUnreadCount())});return s(ke.getNotifications()),a(ke.getUnreadCount()),d},[]),{notifications:t,unreadCount:r,markAsRead:d=>{ke.markAsRead(d)},markAllAsRead:()=>{ke.markAllAsRead()},clearNotification:d=>{ke.clearNotification(d)},clearAllNotifications:()=>{ke.clearAllNotifications()},createNotification:d=>{ke.createNotification(d)}}};function eo({stats:t}){const[s,r]=f.useState("7d"),[a,l]=f.useState("line"),n=[{title:"Total Revenue",value:`₹${t.total_revenue}`,icon:Be,color:"green",details:[]},{title:"Total Users",value:t.total_users,icon:H,color:"blue",details:[]},{title:"Premium Users",value:t.premium_users,icon:Z,color:"yellow",details:[]},{title:"Total Orders",value:t.total_orders,icon:ge,color:"purple",details:[]},{title:"Wallet Transactions",value:`₹${t.wallet_transactions_total}`,icon:K,color:"indigo",details:[]},{title:"Total Withdrawals",value:`₹${t.total_withdrawals}`,icon:aa,color:"red",details:[]},{title:"Referral Bonuses",value:`₹${t.referral_bonuses_paid}`,icon:ce,color:"pink",details:[]}],i=c=>{const o={green:"bg-green-50 text-green-600 border-green-200",blue:"bg-blue-50 text-blue-600 border-blue-200",yellow:"bg-yellow-50 text-yellow-600 border-yellow-200",purple:"bg-purple-50 text-purple-600 border-purple-200",indigo:"bg-indigo-50 text-indigo-600 border-indigo-200",red:"bg-red-50 text-red-600 border-red-200",pink:"bg-pink-50 text-pink-600 border-pink-200"};return o[c]||o.green};return e.jsxs("div",{children:[e.jsx("div",{className:"mb-6 sm:mb-8",children:e.jsxs("div",{className:"flex flex-col lg:flex-row lg:items-center justify-between gap-4",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl sm:text-3xl font-bold text-gray-900 mb-2",children:"Dashboard Overview"}),e.jsx("p",{className:"text-gray-600",children:"Welcome to the HerbalStore admin dashboard"})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row items-stretch sm:items-center gap-3 sm:gap-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(hr,{className:"h-5 w-5 text-gray-400"}),e.jsxs("select",{value:s,onChange:c=>r(c.target.value),className:"flex-1 sm:flex-none border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500",children:[e.jsx("option",{value:"7d",children:"Last 7 days"}),e.jsx("option",{value:"30d",children:"Last 30 days"}),e.jsx("option",{value:"90d",children:"Last 90 days"}),e.jsx("option",{value:"1y",children:"This year"})]})]}),e.jsxs("button",{className:"flex items-center justify-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:[e.jsx(br,{className:"h-5 w-5"}),e.jsx("span",{className:"hidden sm:inline",children:"Export Report"}),e.jsx("span",{className:"sm:hidden",children:"Export"})]})]})]})}),e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 mb-6 sm:mb-8",children:n.map((c,o)=>e.jsxs("div",{className:"bg-white p-4 sm:p-6 rounded-lg shadow-sm border border-gray-200",children:[e.jsx("div",{className:"flex items-center mb-3 sm:mb-4",children:e.jsx("div",{className:`p-2 sm:p-3 rounded-lg ${i(c.color)}`,children:e.jsx(c.icon,{className:"h-5 w-5 sm:h-6 sm:w-6"})})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-xs sm:text-sm text-gray-500 mb-1",children:c.title}),e.jsx("p",{className:"text-xl sm:text-2xl font-bold text-gray-900",children:c.value})]})]},o))}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 lg:gap-8 mb-6 sm:mb-8",children:[e.jsxs("div",{className:"bg-white p-4 sm:p-6 rounded-lg shadow-sm border border-gray-200",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-3 sm:gap-0 mb-4 sm:mb-6",children:[e.jsx("h2",{className:"text-base sm:text-lg font-semibold text-gray-900",children:"Revenue Trends"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("button",{onClick:()=>l("line"),className:`p-2 rounded-lg ${a==="line"?"bg-green-100 text-green-600":"text-gray-400 hover:text-gray-600"}`,children:e.jsx(la,{className:"h-4 w-4 sm:h-5 sm:w-5"})}),e.jsx("button",{onClick:()=>l("bar"),className:`p-2 rounded-lg ${a==="bar"?"bg-green-100 text-green-600":"text-gray-400 hover:text-gray-600"}`,children:e.jsx(na,{className:"h-4 w-4 sm:h-5 sm:w-5"})})]})]}),e.jsx("div",{className:"h-48 sm:h-64 bg-gray-50 rounded-lg flex items-center justify-center",children:e.jsx("p",{className:"text-sm sm:text-base text-gray-500 text-center px-4",children:"Chart visualization will be implemented here"})})]}),e.jsxs("div",{className:"bg-white p-4 sm:p-6 rounded-lg shadow-sm border border-gray-200",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-3 sm:gap-0 mb-4 sm:mb-6",children:[e.jsx("h2",{className:"text-base sm:text-lg font-semibold text-gray-900",children:"User Distribution"}),e.jsx("button",{className:"text-sm text-green-600 hover:text-green-700 self-start sm:self-auto",children:"View Details"})]}),e.jsx("div",{className:"h-48 sm:h-64 bg-gray-50 rounded-lg flex items-center justify-center",children:e.jsx("p",{className:"text-sm sm:text-base text-gray-500 text-center px-4",children:"Pie chart visualization will be implemented here"})})]})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 lg:gap-8 mb-6 sm:mb-8",children:[e.jsxs("div",{className:"bg-white p-4 sm:p-6 rounded-lg shadow-sm border border-gray-200",children:[e.jsx("h2",{className:"text-base sm:text-lg font-semibold text-gray-900 mb-4",children:"Revenue Overview"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(ze,{className:"h-5 w-5 text-green-600"}),e.jsx("span",{className:"text-gray-600",children:"Average Order Value"})]}),e.jsxs("span",{className:"font-medium text-gray-900",children:["₹",(t.total_revenue/t.total_orders).toFixed(2)]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(rt,{className:"h-5 w-5 text-blue-600"}),e.jsx("span",{className:"text-gray-600",children:"Premium User Ratio"})]}),e.jsxs("span",{className:"font-medium text-gray-900",children:[(t.premium_users/t.total_users*100).toFixed(1),"%"]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(K,{className:"h-5 w-5 text-purple-600"}),e.jsx("span",{className:"text-gray-600",children:"Average Wallet Balance"})]}),e.jsxs("span",{className:"font-medium text-gray-900",children:["₹",(t.wallet_transactions_total/t.total_users).toFixed(2)]})]})]})]}),e.jsxs("div",{className:"bg-white p-4 sm:p-6 rounded-lg shadow-sm border border-gray-200",children:[e.jsx("h2",{className:"text-base sm:text-lg font-semibold text-gray-900 mb-4",children:"Quick Actions"}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("button",{className:"w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors touch-target",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(Z,{className:"h-5 w-5 text-green-600"}),e.jsx("span",{className:"font-medium text-sm sm:text-base",children:"Add New Product"})]})}),e.jsx("button",{className:"w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors touch-target",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(H,{className:"h-5 w-5 text-blue-600"}),e.jsx("span",{className:"font-medium text-sm sm:text-base",children:"Manage Users"})]})}),e.jsx("button",{className:"w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors touch-target",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(ge,{className:"h-5 w-5 text-purple-600"}),e.jsx("span",{className:"font-medium text-sm sm:text-base",children:"View Orders"})]})})]})]})]}),e.jsxs("div",{className:"bg-white p-4 sm:p-6 rounded-lg shadow-sm border border-gray-200",children:[e.jsx("h2",{className:"text-base sm:text-lg font-semibold text-gray-900 mb-4",children:"System Status"}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6",children:[e.jsxs("div",{className:"p-3 sm:p-4 bg-green-50 rounded-lg",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[e.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),e.jsx("span",{className:"font-medium text-gray-900 text-sm sm:text-base",children:"Database"})]}),e.jsx("p",{className:"text-xs sm:text-sm text-gray-600",children:"All systems operational"})]}),e.jsxs("div",{className:"p-3 sm:p-4 bg-green-50 rounded-lg",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[e.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),e.jsx("span",{className:"font-medium text-gray-900 text-sm sm:text-base",children:"Payment Gateway"})]}),e.jsx("p",{className:"text-xs sm:text-sm text-gray-600",children:"Processing transactions normally"})]}),e.jsxs("div",{className:"p-3 sm:p-4 bg-green-50 rounded-lg",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[e.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),e.jsx("span",{className:"font-medium text-gray-900 text-sm sm:text-base",children:"Email Service"})]}),e.jsx("p",{className:"text-xs sm:text-sm text-gray-600",children:"Delivering emails successfully"})]})]})]})]})}const so="https://zvsyozsltpczzuwonczs.supabase.co",ro="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp2c3lvenNsdHBjenp1d29uY3pzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk3NDIxMDEsImV4cCI6MjA2NTMxODEwMX0.fjnDFYSyWl3lzm8WM0EU2Gq5EgE5WkdnGO9pUKAlnzQ",us={url:so,anonKey:ro,options:{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0},global:{headers:{"x-application-name":"herbal-store"}},db:{schema:"public"}}};ur(us.url,us.anonKey,us.options);const Xr=us.anonKey,xe=(()=>{if(typeof window<"u"&&window.__supabase_admin_client__)return window.__supabase_admin_client__;if(typeof global<"u"&&global.__supabase_admin_client__)return global.__supabase_admin_client__;const t=ur(us.url,Xr,{auth:{persistSession:!1,autoRefreshToken:!1,detectSessionInUrl:!1,storageKey:"sb-admin-zvsyozsltpczzuwonczs-static",debug:!1},realtime:{params:{eventsPerSecond:1}},global:{headers:{apikey:Xr,"X-Client-Info":"herbal-store-admin@1.0.0"}}});return typeof window<"u"?window.__supabase_admin_client__=t:typeof global<"u"&&(global.__supabase_admin_client__=t),t})(),is=3,me=1e3;class to{static async fixMissingAuthUsers(){try{console.log("🔧 Starting auth user fix process...");const{data:s,error:r}=await y.from("users").select("id, email, full_name, phone, username").order("created_at",{ascending:!0});if(r)return console.error("❌ Failed to fetch database users:",r),{success:!1,error:r.message};if(!s||s.length===0)return console.log("ℹ️ No users found in database"),{success:!0,message:"No users to fix"};console.log(`📊 Found ${s.length} users in database`);let a=null,l=null;for(let o=0;o<is;o++)try{const d=await xe.auth.admin.listUsers();if(d.data){a=d.data;break}l=d.error||new Error("No data returned"),await new Promise(m=>setTimeout(m,me))}catch(d){l=d,await new Promise(m=>setTimeout(m,me))}if(!a||l)return console.error("❌ Failed to fetch auth users:",l),{success:!1,error:l?.message||"Failed to fetch auth users"};const n=new Set(a.users.map(o=>o.email));console.log(`📊 Found ${a.users.length} users in auth`);const i=s.filter(o=>!n.has(o.email));if(i.length===0)return console.log("✅ All users have auth accounts"),{success:!0,message:"All users are properly synchronized"};console.log(`🔧 Found ${i.length} users missing auth accounts`);const c={total:i.length,fixed:0,failed:0,errors:[]};for(const o of i){let d=!1,m=null;for(let h=0;h<is&&!d;h++)try{console.log(`🔧 Creating auth user for: ${o.email} (attempt ${h+1})`);const p="TempPass123!",{data:x,error:g}=await xe.auth.admin.createUser({email:o.email,password:p,email_confirm:!0,user_metadata:{full_name:o.full_name,phone:o.phone,username:o.username,user_id:o.id,auth_fixed:!0,temp_password:!0}});if(g){m=g,console.error(`❌ Failed to create auth user for ${o.email} (attempt ${h+1}):`,g),await new Promise(j=>setTimeout(j,me));continue}if(!x?.user){m=new Error("No user data returned"),console.error(`❌ No user data returned for ${o.email} (attempt ${h+1})`),await new Promise(j=>setTimeout(j,me));continue}const{error:u}=await y.from("users").update({id:x.user.id,updated_at:new Date().toISOString()}).eq("email",o.email);if(u){m=u,console.error(`❌ Failed to update database for ${o.email} (attempt ${h+1}):`,u),await new Promise(j=>setTimeout(j,me));continue}console.log(`✅ Successfully fixed auth for: ${o.email}`),d=!0,c.fixed++}catch(p){m=p,console.error(`❌ Unexpected error fixing ${o.email} (attempt ${h+1}):`,p),await new Promise(x=>setTimeout(x,me))}d||(c.failed++,c.errors.push(`${o.email}: ${m?.message||"Unknown error"}`))}return console.log(`🎉 Auth fix completed: ${c.fixed} fixed, ${c.failed} failed`),{success:!0,message:`Fixed ${c.fixed} users, ${c.failed} failed`,results:c}}catch(s){return console.error("❌ Auth fix service failed:",s),{success:!1,error:s.message}}}static async fixUserAuth(s,r){try{console.log(`🔧 Fixing auth for user: ${s}`);const{data:a,error:l}=await y.from("users").select("*").eq("email",s).single();if(l||!a)return console.error("❌ User not found in database:",l),{success:!1,error:"User not found in database"};let n=null,i=null;for(let h=0;h<is;h++)try{const p=await xe.auth.admin.listUsers();if(p.data){n=p.data;break}i=p.error||new Error("No data returned"),await new Promise(x=>setTimeout(x,me))}catch(p){i=p,await new Promise(x=>setTimeout(x,me))}if(!n||i)return console.error("❌ Failed to check existing auth users:",i),{success:!1,error:"Failed to verify auth status"};const c=n.users.find(h=>h.email===s);if(c){if(console.log("⚠️ Auth user already exists, attempting to fix sync..."),a.id!==c.id){const{error:h}=await y.from("users").update({id:c.id,updated_at:new Date().toISOString()}).eq("email",s);if(h)return console.error("❌ Failed to sync user ID:",h),{success:!1,error:"Failed to synchronize user IDs"};console.log("✅ User ID synchronized successfully")}return{success:!0,message:"User synchronized successfully"}}let o=null,d=null;for(let h=0;h<is;h++)try{console.log("📝 Creating new auth user...");const p=await xe.auth.admin.createUser({email:s,password:r,email_confirm:!0,user_metadata:{full_name:a.full_name,phone:a.mobile_number,username:a.username,auth_fixed:!0}});if(p.data?.user){o=p.data;break}d=p.error||new Error("No user data returned"),await new Promise(x=>setTimeout(x,me))}catch(p){d=p,await new Promise(x=>setTimeout(x,me))}if(!o?.user||d)return console.error("❌ Failed to create auth user:",d),{success:!1,error:d?.message||"Failed to create auth user"};const{error:m}=await y.from("users").update({id:o.user.id,updated_at:new Date().toISOString()}).eq("email",s);if(m){console.error("❌ Failed to update database:",m);try{await xe.auth.admin.deleteUser(o.user.id)}catch(h){console.error("⚠️ Failed to clean up auth user:",h)}return{success:!1,error:"Failed to synchronize user data"}}return console.log("✅ Auth user created and synchronized successfully"),{success:!0,message:"Auth account created successfully",user:o.user}}catch(a){return console.error("❌ Auth fix failed:",a),{success:!1,error:a.message||"An unexpected error occurred"}}}static async fixUserSync(s){try{console.log("🔄 Starting user sync fix for:",s);let r=null,a=null;for(let c=0;c<is;c++)try{const o=await xe.auth.admin.listUsers();if(o.data){r=o.data;break}a=o.error||new Error("No data returned"),await new Promise(d=>setTimeout(d,me))}catch(o){a=o,await new Promise(d=>setTimeout(d,me))}if(!r||a)return console.error("❌ Failed to list users:",a),{success:!1,error:"Failed to list users"};const l=r.users.find(c=>c.email===s);if(!l)return console.error("❌ Auth user not found"),{success:!1,error:"Auth user not found"};const{data:n,error:i}=await y.from("users").select("*").eq("email",s).maybeSingle();if(i&&i.code!=="PGRST116")return console.error("❌ Database error:",i),{success:!1,error:"Database error"};if(!n){console.log("📝 Creating database user");const{data:c,error:o}=await y.from("users").insert({id:l.id,email:s,full_name:l.user_metadata?.full_name||s,username:s.split("@")[0],referral_code:"REF"+Math.random().toString(36).substring(2,8).toUpperCase(),is_premium:!1,wallet_balance:0,ewallet_unlocked:!1}).select().single();return o?(console.error("❌ Failed to create database user:",o),{success:!1,error:"Failed to create database user"}):(console.log("✅ Database user created successfully"),{success:!0,message:"Database user created successfully",user:c})}if(n.id!==l.id){console.log("🔄 Syncing user IDs");const{error:c}=await y.from("users").update({id:l.id,updated_at:new Date().toISOString()}).eq("email",s);if(c)return console.error("❌ Failed to sync user IDs:",c),{success:!1,error:"Failed to sync user IDs"};console.log("✅ User IDs synchronized successfully")}return{success:!0,message:"User synchronized successfully",user:n}}catch(r){return console.error("❌ User sync failed:",r),{success:!1,error:r.message||"An unexpected error occurred"}}}}function ao(){const[t,s]=f.useState([]),[r,a]=f.useState(!0),[l,n]=f.useState(""),[i,c]=f.useState("all");f.useEffect(()=>{o()},[]);const o=async()=>{try{const{data:g,error:u}=await y.from("users").select("*").order("created_at",{ascending:!1});if(u)throw u;s(g||[])}catch(g){console.error("Error fetching users:",g)}finally{a(!1)}},d=async(g,u)=>{try{const j=!u,{error:b}=await y.from("users").update({is_premium:j,updated_at:new Date().toISOString()}).eq("id",g);if(b)throw b;if(j){const N=t.find(P=>P.id===g);if(N&&N.email){const P=await Ze.generateAndAssignForPremiumUser(g,N.email);P.success?k.success(`Premium activated! Referral code: ${P.referralCode}`):(console.error("Failed to generate referral code:",P.error),k.success("Premium activated! (Referral code will be generated shortly)"))}else k.success("Premium activated!")}else k.success("Premium access removed");o()}catch(j){console.error("Error updating user premium status:",j),k.error("Failed to update user status")}},m=async(g,u)=>{try{const{error:j}=await y.rpc("add_wallet_balance",{user_id:g,amount:u});if(j)throw j;await y.from("wallet_transactions").insert({user_id:g,type:u>0?"credit":"debit",amount:Math.abs(u),description:`Admin ${u>0?"credit":"debit"}`,reference_type:"admin"}),s(t.map(b=>b.id===g?{...b,wallet_balance:b.wallet_balance+u}:b)),k.success("Wallet updated successfully")}catch(j){console.error("Error updating wallet:",j),k.error("Failed to update wallet")}},h=async()=>{try{const{data:g,error:u}=await y.rpc("fix_missing_premium_bonuses");if(u)throw u;g?.success?(k.success(`Fixed ${g.processed_bonuses} missing premium bonuses (₹${g.premium_bonus_amount} each)`),o()):k.error(g?.error||"Failed to fix premium bonuses")}catch(g){console.error("Error fixing premium bonuses:",g),k.error("Failed to fix premium bonuses")}},p=async()=>{try{k.loading("Fixing auth users...",{id:"auth-fix"});const g=await to.fixMissingAuthUsers();g.success?(k.success(g.message||"Auth fix completed",{id:"auth-fix"}),g.results&&(console.log("Auth fix results:",g.results),g.results.fixed>0&&k.success(`✅ Fixed ${g.results.fixed} users! They can now login with password: TempPass123!`)),o()):k.error(g.error||"Failed to fix auth users",{id:"auth-fix"})}catch(g){console.error("Error fixing auth users:",g),k.error("Failed to fix auth users",{id:"auth-fix"})}},x=t.filter(g=>{const u=g.full_name.toLowerCase().includes(l.toLowerCase())||g.email.toLowerCase().includes(l.toLowerCase()),j=i==="all"||i==="premium"&&g.is_premium||i==="regular"&&!g.is_premium;return u&&j});return r?e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"})}):e.jsxs("div",{children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6 sm:mb-8",children:[e.jsx("h1",{className:"text-2xl sm:text-3xl font-bold text-gray-900",children:"User Management"}),e.jsxs("div",{className:"flex flex-col sm:flex-row items-stretch sm:items-center gap-3 sm:gap-4",children:[e.jsx("button",{onClick:h,className:"px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-sm font-medium",title:"Fix any missing premium referral bonuses",children:"Fix Premium Bonuses"}),e.jsxs("button",{onClick:p,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium flex items-center gap-2",title:"Fix users who can't login (create missing auth accounts)",children:[e.jsx(ae,{className:"h-4 w-4"}),"Fix Auth Issues"]}),e.jsxs("div",{className:"relative",children:[e.jsx(ue,{className:"h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),e.jsx("input",{type:"text",placeholder:"Search users...",value:l,onChange:g=>n(g.target.value),className:"w-full sm:w-auto pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"})]}),e.jsxs("select",{value:i,onChange:g=>c(g.target.value),className:"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent",children:[e.jsx("option",{value:"all",children:"All Users"}),e.jsx("option",{value:"premium",children:"Premium Users"}),e.jsx("option",{value:"regular",children:"Regular Users"})]})]})]}),e.jsxs("div",{className:"hidden lg:block bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden",children:[e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Wallet Balance"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Joined"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:x.map(g=>e.jsxs("tr",{className:"hover:bg-gray-50",children:[e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-10 h-10 bg-green-100 rounded-full flex items-center justify-center",children:e.jsx(H,{className:"h-5 w-5 text-green-600"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("div",{className:"text-sm font-medium text-gray-900",children:g.full_name}),e.jsx("div",{className:"text-sm text-gray-500",children:g.email})]})]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:g.is_premium?e.jsxs("span",{className:"inline-flex items-center gap-1 bg-gradient-to-r from-yellow-400 to-yellow-600 text-white px-3 py-1 rounded-full text-xs font-medium",children:[e.jsx(L,{className:"h-3 w-3 fill-current"}),"Premium"]}):e.jsx("span",{className:"bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-xs font-medium",children:"Regular"})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(K,{className:"h-4 w-4 text-green-600"}),e.jsxs("span",{className:"text-sm font-medium text-gray-900",children:["₹",g.wallet_balance]})]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(g.created_at).toLocaleDateString()}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("button",{onClick:()=>d(g.id,g.is_premium),className:`px-3 py-1 rounded text-xs font-medium transition-colors ${g.is_premium?"bg-red-100 text-red-700 hover:bg-red-200":"bg-yellow-100 text-yellow-700 hover:bg-yellow-200"}`,children:g.is_premium?"Remove Premium":"Make Premium"}),e.jsx("button",{onClick:()=>{const u=prompt("Enter amount to add/subtract (use negative for deduction):");u&&m(g.id,parseFloat(u))},className:"p-1 text-blue-600 hover:text-blue-800 transition-colors",title:"Update Wallet",children:e.jsx(gs,{className:"h-4 w-4"})})]})})]},g.id))})]})}),x.length===0&&e.jsxs("div",{className:"text-center py-12",children:[e.jsx(H,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No users found"}),e.jsx("p",{className:"text-gray-500",children:"Try adjusting your search or filter criteria"})]})]}),e.jsxs("div",{className:"lg:hidden space-y-4",children:[x.map(g=>e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-4",children:[e.jsxs("div",{className:"flex items-start justify-between mb-3",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-10 h-10 bg-green-100 rounded-full flex items-center justify-center",children:e.jsx(H,{className:"h-5 w-5 text-green-600"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-gray-900",children:g.full_name}),e.jsx("p",{className:"text-sm text-gray-500",children:g.email})]})]}),g.is_premium?e.jsxs("span",{className:"inline-flex items-center gap-1 bg-gradient-to-r from-yellow-400 to-yellow-600 text-white px-2 py-1 rounded-full text-xs font-medium",children:[e.jsx(L,{className:"h-3 w-3 fill-current"}),"Premium"]}):e.jsx("span",{className:"bg-gray-100 text-gray-700 px-2 py-1 rounded-full text-xs font-medium",children:"Regular"})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-500 mb-1",children:"Wallet Balance"}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(K,{className:"h-4 w-4 text-green-600"}),e.jsxs("span",{className:"text-sm font-medium text-gray-900",children:["₹",g.wallet_balance]})]})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-500 mb-1",children:"Joined"}),e.jsx("p",{className:"text-sm text-gray-900",children:new Date(g.created_at).toLocaleDateString()})]})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-2",children:[e.jsx("button",{onClick:()=>d(g.id,g.is_premium),className:`flex-1 px-3 py-2 rounded text-sm font-medium transition-colors ${g.is_premium?"bg-red-100 text-red-700 hover:bg-red-200":"bg-yellow-100 text-yellow-700 hover:bg-yellow-200"}`,children:g.is_premium?"Remove Premium":"Make Premium"}),e.jsxs("button",{onClick:()=>{const u=prompt("Enter amount to add/subtract (use negative for deduction):");u&&m(g.id,parseFloat(u))},className:"flex items-center justify-center gap-2 px-3 py-2 text-blue-600 hover:text-blue-800 border border-blue-200 hover:border-blue-300 rounded transition-colors",title:"Update Wallet",children:[e.jsx(gs,{className:"h-4 w-4"}),e.jsx("span",{className:"text-sm",children:"Edit Wallet"})]})]})]},g.id)),x.length===0&&e.jsxs("div",{className:"text-center py-12",children:[e.jsx(H,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No users found"}),e.jsx("p",{className:"text-gray-500",children:"Try adjusting your search or filter criteria"})]})]})]})}function lo(){J();const[t,s]=f.useState([]),[r,a]=f.useState(!0),[l,n]=f.useState(""),[i,c]=f.useState(!1),[o,d]=f.useState(null);f.useEffect(()=>{m()},[]);const m=async()=>{try{const{data:x,error:g}=await y.from("products").select("*").order("created_at",{ascending:!1});if(g)throw console.error("Error fetching products:",g),g.code==="PGRST301"?k.error("Authentication required. Please log in."):k.error("Failed to fetch products"),g;s(x||[])}catch(x){console.error("Error fetching products:",x)}finally{a(!1)}},h=async x=>{if(confirm("Are you sure you want to delete this product?"))try{const{error:g}=await y.from("products").delete().eq("id",x);if(g)throw g;s(t.filter(u=>u.id!==x)),k.success("Product deleted successfully")}catch(g){console.error("Error deleting product:",g),k.error("Failed to delete product")}},p=t.filter(x=>x.name.toLowerCase().includes(l.toLowerCase())||x.category.toLowerCase().includes(l.toLowerCase()));return r?e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"})}):e.jsxs("div",{className:"p-4 sm:p-6 lg:p-8",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between mb-6 sm:mb-8 gap-4",children:[e.jsx("h1",{className:"text-2xl sm:text-3xl font-bold text-gray-900",children:"Product Management"}),e.jsxs("div",{className:"flex flex-col sm:flex-row items-stretch sm:items-center gap-3 sm:gap-4",children:[e.jsxs("div",{className:"relative",children:[e.jsx(ue,{className:"h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),e.jsx("input",{type:"text",placeholder:"Search products...",value:l,onChange:x=>n(x.target.value),className:"w-full sm:w-auto pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-base"})]}),e.jsxs("button",{onClick:()=>c(!0),className:"flex items-center justify-center gap-2 bg-green-600 text-white px-4 py-3 rounded-lg hover:bg-green-700 transition-colors touch-target",children:[e.jsx(re,{className:"h-4 w-4"}),"Add Product"]})]})]}),e.jsx("div",{className:"responsive-grid",children:p.map(x=>e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden",children:[e.jsx("div",{className:"aspect-square bg-gray-100",children:e.jsx("img",{src:_r(Nr(x.image_url),400,400),alt:x.name,className:"w-full h-full object-cover",onError:g=>vr(g)})}),e.jsxs("div",{className:"p-4",children:[e.jsxs("div",{className:"flex items-start justify-between mb-2",children:[e.jsx("h3",{className:"font-semibold text-gray-900 line-clamp-2 text-sm sm:text-base",children:x.name}),x.is_premium_only&&e.jsx(Ce,{className:"h-4 w-4 text-yellow-500 fill-current flex-shrink-0"})]}),e.jsx("p",{className:"text-xs sm:text-sm text-gray-600 mb-2",children:x.category}),e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsxs("span",{className:"text-base sm:text-lg font-bold text-green-600",children:["₹",x.price]}),e.jsxs("span",{className:"text-xs sm:text-sm text-gray-500",children:["Stock: ",x.stock_quantity]})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row items-stretch sm:items-center gap-2",children:[e.jsxs("button",{onClick:()=>d(x),className:"flex-1 bg-blue-600 text-white px-3 py-2 rounded text-sm font-medium hover:bg-blue-700 transition-colors touch-target flex items-center justify-center",children:[e.jsx(gs,{className:"h-4 w-4 mr-1"}),"Edit"]}),e.jsxs("button",{onClick:()=>h(x.id),className:"flex-1 bg-red-600 text-white px-3 py-2 rounded text-sm font-medium hover:bg-red-700 transition-colors touch-target flex items-center justify-center",children:[e.jsx(gr,{className:"h-4 w-4 mr-1"}),"Delete"]})]})]})]},x.id))}),p.length===0&&e.jsxs("div",{className:"text-center py-12 px-4",children:[e.jsx(Z,{className:"h-12 w-12 sm:h-16 sm:w-16 text-gray-400 mx-auto mb-4"}),e.jsx("h3",{className:"text-base sm:text-lg font-medium text-gray-900 mb-2",children:"No products found"}),e.jsx("p",{className:"text-sm sm:text-base text-gray-500",children:"Try adjusting your search criteria or add a new product"})]}),(i||o)&&e.jsx(no,{product:o,onClose:()=>{c(!1),d(null)},onSave:()=>{m(),c(!1),d(null)}})]})}function no({product:t,onClose:s,onSave:r}){const[a,l]=f.useState({name:t?.name||"",description:t?.description||"",price:t?.price||0,image_url:t?.image_url||"",category:t?.category||"",ingredients:t?.ingredients?.join(", ")||"",stock_quantity:t?.stock_quantity||0,is_premium_only:t?.is_premium_only||!1}),[n,i]=f.useState(!1),c=async o=>{o.preventDefault(),i(!0);try{const d={...a,ingredients:a.ingredients.split(",").map(m=>m.trim()).filter(Boolean),rating:t?.rating||4.5,reviews_count:t?.reviews_count||0};if(t){const{error:m}=await y.from("products").update(d).eq("id",t.id);if(m)throw m;k.success("Product updated successfully")}else{const{error:m}=await y.from("products").insert(d);if(m)throw m;k.success("Product created successfully")}r()}catch(d){console.error("Error saving product:",d),k.error("Failed to save product")}finally{i(!1)}};return e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:e.jsx("div",{className:"bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:e.jsxs("div",{className:"p-6",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:t?"Edit Product":"Add New Product"}),e.jsxs("form",{onSubmit:c,className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Product Name"}),e.jsx("input",{type:"text",value:a.name,onChange:o=>l({...a,name:o.target.value}),className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Description"}),e.jsx("textarea",{value:a.description,onChange:o=>l({...a,description:o.target.value}),rows:3,className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent",required:!0})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Price (₹)"}),e.jsx("input",{type:"number",value:a.price,onChange:o=>l({...a,price:Number(o.target.value)}),className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent",required:!0,min:"0"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Stock Quantity"}),e.jsx("input",{type:"number",value:a.stock_quantity,onChange:o=>l({...a,stock_quantity:Number(o.target.value)}),className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent",required:!0,min:"0"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Category"}),e.jsxs("select",{value:a.category,onChange:o=>l({...a,category:o.target.value}),className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent",required:!0,children:[e.jsx("option",{value:"",children:"Select Category"}),e.jsx("option",{value:"Immunity",children:"Immunity"}),e.jsx("option",{value:"Digestion",children:"Digestion"}),e.jsx("option",{value:"Energy",children:"Energy"}),e.jsx("option",{value:"Wellness",children:"Wellness"}),e.jsx("option",{value:"Skincare",children:"Skincare"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Image URL"}),e.jsx("input",{type:"url",value:a.image_url,onChange:o=>l({...a,image_url:o.target.value}),className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Ingredients (comma-separated)"}),e.jsx("input",{type:"text",value:a.ingredients,onChange:o=>l({...a,ingredients:o.target.value}),className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent",placeholder:"Turmeric, Ginger, Honey",required:!0})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",id:"premium",checked:a.is_premium_only,onChange:o=>l({...a,is_premium_only:o.target.checked}),className:"h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"}),e.jsx("label",{htmlFor:"premium",className:"ml-2 block text-sm text-gray-900",children:"Premium Only Product"})]}),e.jsxs("div",{className:"flex gap-3 pt-4",children:[e.jsx("button",{type:"submit",disabled:n,className:"flex-1 bg-green-600 text-white py-3 rounded-lg font-medium hover:bg-green-700 transition-colors disabled:opacity-50",children:n?"Saving...":t?"Update Product":"Create Product"}),e.jsx("button",{type:"button",onClick:s,className:"flex-1 bg-gray-500 text-white py-3 rounded-lg font-medium hover:bg-gray-600 transition-colors",children:"Cancel"})]})]})]})})})}function io(){const[t,s]=f.useState([]),[r,a]=f.useState(!0),[l,n]=f.useState(""),[i,c]=f.useState("all");f.useEffect(()=>{o()},[]);const o=async()=>{try{const{data:x,error:g}=await y.from("orders").select(`
          *,
          user:users(full_name, email),
          order_items(
            *,
            product:products(name, image_url)
          )
        `).order("created_at",{ascending:!1});if(g)throw g;s(x||[])}catch(x){console.error("Error fetching orders:",x)}finally{a(!1)}},d=async(x,g)=>{try{const{error:u}=await y.from("orders").update({status:g}).eq("id",x);if(u)throw u;s(t.map(j=>j.id===x?{...j,status:g}:j)),k.success("Order status updated successfully")}catch(u){console.error("Error updating order status:",u),k.error("Failed to update order status")}},m=x=>{switch(x){case"pending":return e.jsx(Z,{className:"h-5 w-5 text-yellow-500"});case"confirmed":return e.jsx(V,{className:"h-5 w-5 text-blue-500"});case"shipped":return e.jsx(De,{className:"h-5 w-5 text-purple-500"});case"delivered":return e.jsx(V,{className:"h-5 w-5 text-green-500"});case"cancelled":return e.jsx(Ns,{className:"h-5 w-5 text-red-500"});default:return e.jsx(Z,{className:"h-5 w-5 text-gray-500"})}},h=x=>{switch(x){case"pending":return"bg-yellow-100 text-yellow-800";case"confirmed":return"bg-blue-100 text-blue-800";case"shipped":return"bg-purple-100 text-purple-800";case"delivered":return"bg-green-100 text-green-800";case"cancelled":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},p=t.filter(x=>{const g=x.id.toLowerCase().includes(l.toLowerCase())||x.user?.full_name?.toLowerCase().includes(l.toLowerCase())||x.user?.email?.toLowerCase().includes(l.toLowerCase()),u=i==="all"||x.status===i;return g&&u});return r?e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"})}):e.jsxs("div",{children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6 sm:mb-8",children:[e.jsx("h1",{className:"text-2xl sm:text-3xl font-bold text-gray-900",children:"Order Management"}),e.jsxs("div",{className:"flex flex-col sm:flex-row items-stretch sm:items-center gap-3 sm:gap-4",children:[e.jsxs("div",{className:"relative",children:[e.jsx(ue,{className:"h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),e.jsx("input",{type:"text",placeholder:"Search orders...",value:l,onChange:x=>n(x.target.value),className:"w-full sm:w-auto pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm sm:text-base"})]}),e.jsxs("select",{value:i,onChange:x=>c(x.target.value),className:"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm sm:text-base",children:[e.jsx("option",{value:"all",children:"All Orders"}),e.jsx("option",{value:"pending",children:"Pending"}),e.jsx("option",{value:"confirmed",children:"Confirmed"}),e.jsx("option",{value:"shipped",children:"Shipped"}),e.jsx("option",{value:"delivered",children:"Delivered"}),e.jsx("option",{value:"cancelled",children:"Cancelled"})]})]})]}),e.jsxs("div",{className:"hidden lg:block bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden",children:[e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Order"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Customer"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Total"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:p.map(x=>e.jsxs("tr",{className:"hover:bg-gray-50",children:[e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(ge,{className:"h-5 w-5 text-gray-400 mr-3"}),e.jsxs("div",{children:[e.jsxs("div",{className:"text-sm font-medium text-gray-900",children:["#",x.id.slice(0,8)]}),e.jsxs("div",{className:"text-sm text-gray-500",children:[x.items?.length||0," items"]})]})]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium text-gray-900",children:x.user?.full_name}),e.jsx("div",{className:"text-sm text-gray-500",children:x.user?.email})]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{className:"flex items-center gap-2",children:[m(x.status),e.jsx("span",{className:`px-3 py-1 rounded-full text-xs font-medium ${h(x.status)}`,children:x.status.charAt(0).toUpperCase()+x.status.slice(1)})]})}),e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap",children:[e.jsxs("div",{className:"text-sm font-medium text-gray-900",children:["₹",x.total_amount]}),x.wallet_used>0&&e.jsxs("div",{className:"text-xs text-green-600",children:["Wallet: ₹",x.wallet_used]})]}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(x.created_at).toLocaleDateString()}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:e.jsxs("select",{value:x.status,onChange:g=>d(x.id,g.target.value),className:"text-sm border border-gray-300 rounded px-2 py-1 focus:ring-2 focus:ring-green-500 focus:border-transparent",children:[e.jsx("option",{value:"pending",children:"Pending"}),e.jsx("option",{value:"confirmed",children:"Confirmed"}),e.jsx("option",{value:"shipped",children:"Shipped"}),e.jsx("option",{value:"delivered",children:"Delivered"}),e.jsx("option",{value:"cancelled",children:"Cancelled"})]})})]},x.id))})]})}),p.length===0&&e.jsxs("div",{className:"text-center py-12",children:[e.jsx(ge,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No orders found"}),e.jsx("p",{className:"text-gray-500",children:"Try adjusting your search or filter criteria"})]})]}),e.jsxs("div",{className:"lg:hidden space-y-4",children:[p.map(x=>e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-4",children:[e.jsxs("div",{className:"flex items-start justify-between mb-3",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(ge,{className:"h-5 w-5 text-gray-400"}),e.jsxs("div",{children:[e.jsxs("h3",{className:"font-medium text-gray-900",children:["#",x.id.slice(0,8)]}),e.jsxs("p",{className:"text-sm text-gray-500",children:[x.items?.length||0," items"]})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[m(x.status),e.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${h(x.status)}`,children:x.status.charAt(0).toUpperCase()+x.status.slice(1)})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-500 mb-1",children:"Customer"}),e.jsx("p",{className:"text-sm font-medium text-gray-900",children:x.user?.full_name}),e.jsx("p",{className:"text-xs text-gray-500",children:x.user?.email})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-500 mb-1",children:"Total"}),e.jsxs("p",{className:"text-sm font-medium text-gray-900",children:["₹",x.total_amount]}),x.wallet_used>0&&e.jsxs("p",{className:"text-xs text-green-600",children:["Wallet: ₹",x.wallet_used]})]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("p",{className:"text-xs text-gray-500",children:new Date(x.created_at).toLocaleDateString()}),e.jsxs("select",{value:x.status,onChange:g=>d(x.id,g.target.value),className:"text-sm border border-gray-300 rounded px-2 py-1 focus:ring-2 focus:ring-green-500 focus:border-transparent",children:[e.jsx("option",{value:"pending",children:"Pending"}),e.jsx("option",{value:"confirmed",children:"Confirmed"}),e.jsx("option",{value:"shipped",children:"Shipped"}),e.jsx("option",{value:"delivered",children:"Delivered"}),e.jsx("option",{value:"cancelled",children:"Cancelled"})]})]})]},x.id)),p.length===0&&e.jsxs("div",{className:"text-center py-12",children:[e.jsx(ge,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No orders found"}),e.jsx("p",{className:"text-gray-500",children:"Try adjusting your search or filter criteria"})]})]})]})}function oo(){const{user:t}=J(),[s,r]=f.useState([]),[a,l]=f.useState([]),[n,i]=f.useState(!0),[c,o]=f.useState(""),[d,m]=f.useState("all"),[h,p]=f.useState(!1),[x,g]=f.useState(null),[u,j]=f.useState(""),[b,N]=f.useState(""),[P,A]=f.useState(!1),[E,_]=f.useState({totalCredits:0,totalDebits:0,totalTransactions:0,totalWalletBalance:0});f.useEffect(()=>{w(),S()},[]);const w=async()=>{try{i(!0);const{data:v,error:$}=await y.from("wallet_transactions").select(`
          *,
          user:users(full_name, email)
        `).order("created_at",{ascending:!1});if($)throw $;r(v||[]);const O=v?.filter(Q=>Q.type==="credit").reduce((Q,ve)=>Q+ve.amount,0)||0,z=v?.filter(Q=>Q.type==="debit").reduce((Q,ve)=>Q+ve.amount,0)||0,{data:G}=await y.from("users").select("wallet_balance"),ee=G?.reduce((Q,ve)=>Q+(ve.wallet_balance||0),0)||0;_({totalCredits:O,totalDebits:z,totalTransactions:v?.length||0,totalWalletBalance:ee})}catch(v){console.error("Error fetching transactions:",v),k.error("Failed to fetch transactions")}finally{i(!1)}},S=async()=>{try{const{data:v,error:$}=await y.from("users").select("id, full_name, email, wallet_balance, is_premium").order("full_name");if($)throw $;l(v||[])}catch(v){console.error("Error fetching users:",v)}},C=async()=>{if(!x||!u||!b||!t){k.error("Please fill in all fields");return}const v=parseFloat(u);if(isNaN(v)||v<=0){k.error("Please enter a valid amount");return}A(!0);try{const $=await Bi(x.id,v,b,t.id);$.success?(await w(),await S(),k.success(`${pe(v)} added to ${x.full_name}'s wallet successfully!`),p(!1),g(null),j(""),N("")):k.error($.error||"Failed to add money")}catch($){console.error("Error adding money:",$),k.error("Failed to add money. Please try again.")}finally{A(!1)}},T=v=>v==="credit"?e.jsx(Er,{className:"h-5 w-5 text-green-500"}):e.jsx(Pr,{className:"h-5 w-5 text-red-500"}),F=v=>v==="credit"?"text-green-600":"text-red-600",I=s.filter(v=>{const $=v.description.toLowerCase().includes(c.toLowerCase())||v.user?.full_name?.toLowerCase().includes(c.toLowerCase())||v.user?.email?.toLowerCase().includes(c.toLowerCase()),O=d==="all"||v.type===d;return $&&O});return n?e.jsx("div",{className:"flex items-center justify-center min-h-screen",children:e.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-green-500"})}):e.jsxs("div",{children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between mb-8 gap-4",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Wallet Management"}),e.jsxs("div",{className:"flex flex-col sm:flex-row items-stretch sm:items-center gap-4",children:[e.jsxs("div",{className:"relative",children:[e.jsx(ue,{className:"h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),e.jsx("input",{type:"text",placeholder:"Search transactions...",value:c,onChange:v=>o(v.target.value),className:"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent w-full sm:w-auto"})]}),e.jsxs("select",{value:d,onChange:v=>m(v.target.value),className:"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent",children:[e.jsx("option",{value:"all",children:"All Transactions"}),e.jsx("option",{value:"credit",children:"Credits"}),e.jsx("option",{value:"debit",children:"Debits"})]}),e.jsxs("button",{onClick:()=>p(!0),className:"flex items-center justify-center gap-2 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors",children:[e.jsx(re,{className:"h-4 w-4"}),"Add Money to User"]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6 sm:mb-8",children:[e.jsx("div",{className:"bg-white p-4 sm:p-6 rounded-lg shadow-sm border border-gray-200",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Total Credits"}),e.jsx("p",{className:"text-xl sm:text-2xl font-bold text-green-600",children:pe(E.totalCredits)})]}),e.jsx(Er,{className:"h-6 w-6 sm:h-8 sm:w-8 text-green-500"})]})}),e.jsx("div",{className:"bg-white p-4 sm:p-6 rounded-lg shadow-sm border border-gray-200",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Total Debits"}),e.jsx("p",{className:"text-xl sm:text-2xl font-bold text-red-600",children:pe(E.totalDebits)})]}),e.jsx(Pr,{className:"h-6 w-6 sm:h-8 sm:w-8 text-red-500"})]})}),e.jsx("div",{className:"bg-white p-4 sm:p-6 rounded-lg shadow-sm border border-gray-200",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Total Wallet Balance"}),e.jsx("p",{className:"text-xl sm:text-2xl font-bold text-blue-600",children:pe(E.totalWalletBalance)})]}),e.jsx(K,{className:"h-6 w-6 sm:h-8 sm:w-8 text-blue-500"})]})}),e.jsx("div",{className:"bg-white p-4 sm:p-6 rounded-lg shadow-sm border border-gray-200",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Total Transactions"}),e.jsx("p",{className:"text-xl sm:text-2xl font-bold text-purple-600",children:E.totalTransactions})]}),e.jsx(H,{className:"h-6 w-6 sm:h-8 sm:w-8 text-purple-500"})]})})]}),e.jsxs("div",{className:"hidden lg:block bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden",children:[e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Transaction"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Type"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Amount"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:I.map(v=>e.jsxs("tr",{className:"hover:bg-gray-50",children:[e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium text-gray-900",children:v.user?.full_name}),e.jsx("div",{className:"text-sm text-gray-500",children:v.user?.email})]})}),e.jsx("td",{className:"px-6 py-4",children:e.jsxs("div",{className:"flex items-start gap-3",children:[T(v.type),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium text-gray-900",children:v.description}),v.reference_type&&e.jsx("div",{className:"text-xs text-gray-500 capitalize",children:v.reference_type})]})]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${v.type==="credit"?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:v.type.charAt(0).toUpperCase()+v.type.slice(1)})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("span",{className:`text-sm font-medium ${F(v.type)}`,children:[v.type==="credit"?"+":"-",pe(v.amount)]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:qs(v.created_at)})]},v.id))})]})}),I.length===0&&e.jsxs("div",{className:"text-center py-12",children:[e.jsx(K,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No transactions found"}),e.jsx("p",{className:"text-gray-500",children:"Try adjusting your search or filter criteria"})]})]}),e.jsxs("div",{className:"lg:hidden space-y-4",children:[I.map(v=>e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-4",children:[e.jsxs("div",{className:"flex items-start justify-between mb-3",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[T(v.type),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-gray-900",children:v.user?.full_name}),e.jsx("p",{className:"text-sm text-gray-500",children:v.user?.email})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("span",{className:`text-lg font-bold ${F(v.type)}`,children:[v.type==="credit"?"+":"-",pe(v.amount)]}),e.jsx("span",{className:`block text-xs px-2 py-1 rounded-full mt-1 ${v.type==="credit"?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:v.type.charAt(0).toUpperCase()+v.type.slice(1)})]})]}),e.jsxs("div",{className:"mb-3",children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:v.description}),v.reference_type&&e.jsx("p",{className:"text-xs text-gray-500 capitalize mt-1",children:v.reference_type})]}),e.jsx("div",{className:"text-xs text-gray-500",children:qs(v.created_at)})]},v.id)),I.length===0&&e.jsxs("div",{className:"text-center py-12",children:[e.jsx(K,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No transactions found"}),e.jsx("p",{className:"text-gray-500",children:"Try adjusting your search or filter criteria"})]})]}),h&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:e.jsx("div",{className:"bg-white rounded-xl max-w-md w-full max-h-[90vh] overflow-y-auto",children:e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsx("h2",{className:"text-xl font-bold text-gray-900",children:"Add Money to User Wallet"}),e.jsx("button",{onClick:()=>{p(!1),g(null),j(""),N("")},className:"text-gray-400 hover:text-gray-600 p-1",children:"×"})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Select User"}),e.jsxs("select",{value:x?.id||"",onChange:v=>{const $=a.find(O=>O.id===v.target.value);g($||null)},className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent",required:!0,children:[e.jsx("option",{value:"",children:"Choose a user..."}),a.map(v=>e.jsxs("option",{value:v.id,children:[v.full_name," (",v.email,") - Current: ",pe(v.wallet_balance||0)]},v.id))]})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Amount to Add"}),e.jsxs("div",{className:"relative",children:[e.jsx("span",{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500",children:"₹"}),e.jsx("input",{type:"number",value:u,onChange:v=>j(v.target.value),className:"w-full pl-8 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent",placeholder:"Enter amount",min:"1",max:"100000",step:"0.01",required:!0})]}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Maximum: ₹1,00,000 per transaction"})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Description"}),e.jsx("input",{type:"text",value:b,onChange:v=>N(v.target.value),className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent",placeholder:"e.g., Admin credit, Refund, Bonus",required:!0})]}),e.jsxs("div",{className:"flex space-x-3",children:[e.jsx("button",{onClick:()=>{p(!1),g(null),j(""),N("")},className:"flex-1 bg-gray-100 text-gray-700 py-3 px-4 rounded-lg font-medium hover:bg-gray-200 transition-colors",children:"Cancel"}),e.jsx("button",{onClick:C,disabled:P||!x||!u||!b,className:"flex-1 bg-green-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center space-x-2",children:P?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),e.jsx("span",{children:"Processing..."})]}):e.jsxs(e.Fragment,{children:[e.jsx(re,{className:"w-4 h-4"}),e.jsxs("span",{children:["Add ₹",u||"0"]})]})})]})]})})})]})}const co=()=>{const[t,s]=f.useState([]),[r,a]=f.useState(!0),[l,n]=f.useState(""),[i,c]=f.useState("all"),[o,d]=f.useState(null),[m,h]=f.useState(!1),[p,x]=f.useState(""),[g,u]=f.useState(""),[j,b]=f.useState(!1),[N,P]=f.useState({total:0,pending:0,under_review:0,approved:0,rejected:0});f.useEffect(()=>{A()},[]);const A=async()=>{try{a(!0);const{data:C,error:T}=await xe.from("users").select(`
          id,
          full_name,
          email,
          mobile_number,
          kyc_status,
          kyc_submitted_at,
          kyc_reviewed_at,
          kyc_comments,
          aadhaar_front_url,
          aadhaar_back_url,
          pan_card_url,
          account_number_encrypted,
          ifsc_code,
          account_holder_name,
          bank_name
        `).not("kyc_submitted_at","is",null).order("kyc_submitted_at",{ascending:!1});if(T)throw T;const F=C||[];s(F);const I={total:F.length,pending:F.filter(v=>v.kyc_status==="pending").length,under_review:F.filter(v=>v.kyc_status==="under_review").length,approved:F.filter(v=>v.kyc_status==="approved").length,rejected:F.filter(v=>v.kyc_status==="rejected").length};P(I)}catch(C){console.error("Error fetching KYC applications:",C),k.error("Failed to fetch KYC applications")}finally{a(!1)}},E=async(C,T)=>{if(!g.trim()){k.error("Please add review comments");return}const F=T==="approved"?"Are you sure you want to approve this KYC application?":"Are you sure you want to reject this KYC application?";if(window.confirm(F)){b(!0);try{console.log("Updating KYC status for user:",C,"to:",T);const{error:I}=await xe.from("users").update({kyc_status:T,kyc_reviewed_at:new Date().toISOString(),kyc_comments:g}).eq("id",C);if(I)throw console.error("Error updating user KYC status:",I),I;if(console.log("KYC status updated successfully"),T==="approved"){const{error:$}=await xe.from("wallet_transactions").insert({user_id:C,type:"credit",amount:50,description:"KYC Approval Bonus",reference_type:"kyc_bonus",reference_id:`KYC_BONUS_${Date.now()}`});$&&console.error("Error creating KYC bonus:",$);const{data:O,error:z}=await xe.from("users").select("wallet_balance").eq("id",C).single();if(!z&&O){const G=(O.wallet_balance||0)+50,{error:ee}=await xe.from("users").update({wallet_balance:G}).eq("id",C);ee&&console.error("Error updating wallet balance:",ee)}}const v=T==="approved"?"KYC approved successfully! User has been notified and received a ₹50 bonus.":"KYC rejected successfully. User has been notified.";k.success(v),d(null),u(""),A()}catch(I){console.error("Error updating KYC status:",I),k.error("Failed to update KYC status")}finally{b(!1)}}},_=C=>{const F={pending:{color:"bg-yellow-100 text-yellow-800",icon:Ne},under_review:{color:"bg-blue-100 text-blue-800",icon:le},approved:{color:"bg-green-100 text-green-800",icon:V},rejected:{color:"bg-red-100 text-red-800",icon:we}}[C],I=F.icon;return e.jsxs("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${F.color}`,children:[e.jsx(I,{className:"w-3 h-3 mr-1"}),C.replace("_"," ").toUpperCase()]})},w=t.filter(C=>{const T=(C.full_name||"").toLowerCase().includes(l.toLowerCase())||(C.email||"").toLowerCase().includes(l.toLowerCase())||(C.mobile_number||"").includes(l),F=i==="all"||C.kyc_status===i;return T&&F}),S=C=>{x(C),h(!0)};return e.jsxs("div",{className:"p-4 sm:p-6 lg:p-8",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between mb-6 sm:mb-8 gap-4",children:[e.jsxs("div",{children:[e.jsxs("h1",{className:"text-2xl sm:text-3xl font-bold text-gray-900 flex items-center",children:[e.jsx(ae,{className:"w-8 h-8 text-green-600 mr-3"}),"KYC Management"]}),e.jsx("p",{className:"text-gray-600 mt-1",children:"Review and manage user verification applications"})]}),e.jsxs("button",{onClick:A,className:"flex items-center gap-2 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors touch-target",children:[e.jsx(le,{className:"w-4 h-4"}),"Refresh"]})]}),e.jsxs("div",{className:"grid grid-cols-2 lg:grid-cols-5 gap-4 mb-6",children:[e.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center",children:e.jsx(ae,{className:"w-5 h-5 text-blue-600"})}),e.jsxs("div",{className:"ml-3",children:[e.jsx("p",{className:"text-sm font-medium text-gray-500",children:"Total Applications"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900",children:N.total})]})]})}),e.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center",children:e.jsx(Ne,{className:"w-5 h-5 text-yellow-600"})}),e.jsxs("div",{className:"ml-3",children:[e.jsx("p",{className:"text-sm font-medium text-gray-500",children:"Pending"}),e.jsx("p",{className:"text-2xl font-bold text-yellow-600",children:N.pending})]})]})}),e.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center",children:e.jsx(le,{className:"w-5 h-5 text-blue-600"})}),e.jsxs("div",{className:"ml-3",children:[e.jsx("p",{className:"text-sm font-medium text-gray-500",children:"Under Review"}),e.jsx("p",{className:"text-2xl font-bold text-blue-600",children:N.under_review})]})]})}),e.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center",children:e.jsx(V,{className:"w-5 h-5 text-green-600"})}),e.jsxs("div",{className:"ml-3",children:[e.jsx("p",{className:"text-sm font-medium text-gray-500",children:"Approved"}),e.jsx("p",{className:"text-2xl font-bold text-green-600",children:N.approved})]})]})}),e.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center",children:e.jsx(we,{className:"w-5 h-5 text-red-600"})}),e.jsxs("div",{className:"ml-3",children:[e.jsx("p",{className:"text-sm font-medium text-gray-500",children:"Rejected"}),e.jsx("p",{className:"text-2xl font-bold text-red-600",children:N.rejected})]})]})})]}),e.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6",children:e.jsxs("div",{className:"flex flex-col sm:flex-row gap-4",children:[e.jsx("div",{className:"flex-1",children:e.jsxs("div",{className:"relative",children:[e.jsx(ue,{className:"h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),e.jsx("input",{type:"text",placeholder:"Search by name, email, or mobile...",value:l,onChange:C=>n(C.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"})]})}),e.jsx("div",{className:"sm:w-48",children:e.jsxs("select",{value:i,onChange:C=>c(C.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent",children:[e.jsx("option",{value:"all",children:"All Status"}),e.jsx("option",{value:"pending",children:"Pending"}),e.jsx("option",{value:"under_review",children:"Under Review"}),e.jsx("option",{value:"approved",children:"Approved"}),e.jsx("option",{value:"rejected",children:"Rejected"})]})})]})}),e.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden",children:r?e.jsx("div",{className:"flex items-center justify-center py-12",children:e.jsx(le,{className:"w-8 h-8 text-green-600 animate-spin"})}):w.length===0?e.jsxs("div",{className:"text-center py-12",children:[e.jsx(ae,{className:"w-16 h-16 text-gray-400 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No KYC Applications"}),e.jsx("p",{className:"text-gray-500",children:"No applications match your current filters"})]}):e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User Details"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Documents"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Submitted"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:w.map(C=>e.jsxs("tr",{className:"hover:bg-gray-50",children:[e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center",children:e.jsx(Ee,{className:"w-5 h-5 text-gray-500"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("div",{className:"text-sm font-medium text-gray-900",children:C.full_name||"No name provided"}),e.jsx("div",{className:"text-sm text-gray-500",children:C.email||"No email"}),e.jsx("div",{className:"text-sm text-gray-500",children:C.mobile_number||"No mobile"})]})]})}),e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap",children:[_(C.kyc_status),C.kyc_reviewed_at&&e.jsxs("div",{className:"text-xs text-gray-500 mt-1",children:["Reviewed: ",new Date(C.kyc_reviewed_at).toLocaleDateString()]})]}),e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap",children:[e.jsxs("div",{className:"flex space-x-1",children:[C.aadhaar_front_url&&e.jsx("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:"Aadhaar F"}),C.aadhaar_back_url&&e.jsx("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:"Aadhaar B"}),C.pan_card_url&&e.jsx("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800",children:"PAN"})]}),e.jsxs("div",{className:"text-xs text-gray-500 mt-1",children:[[C.aadhaar_front_url,C.aadhaar_back_url,C.pan_card_url].filter(Boolean).length,"/3 docs"]})]}),e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:[e.jsx("div",{children:new Date(C.kyc_submitted_at).toLocaleDateString()}),e.jsx("div",{className:"text-xs text-gray-400",children:new Date(C.kyc_submitted_at).toLocaleTimeString()})]}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:e.jsxs("div",{className:"flex space-x-2",children:[e.jsx("button",{onClick:()=>d(C),className:"text-green-600 hover:text-green-900 p-1 rounded hover:bg-green-50",title:"Review Application",children:e.jsx(ne,{className:"w-4 h-4"})}),C.kyc_status==="pending"&&e.jsxs(e.Fragment,{children:[e.jsx("button",{onClick:()=>{d(C),u("Quick approval - all documents verified"),setTimeout(()=>E(C.id,"approved"),100)},className:"text-green-600 hover:text-green-900 p-1 rounded hover:bg-green-50",title:"Quick Approve",children:e.jsx(Us,{className:"w-4 h-4"})}),e.jsx("button",{onClick:()=>{d(C),u("Quick rejection - documents need review"),setTimeout(()=>E(C.id,"rejected"),100)},className:"text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-50",title:"Quick Reject",children:e.jsx(oe,{className:"w-4 h-4"})})]})]})})]},C.id))})]})})}),o&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:e.jsx("div",{className:"bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto",children:e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("h2",{className:"text-xl font-bold text-gray-900",children:["KYC Application - ",o.full_name]}),e.jsx("button",{onClick:()=>d(null),className:"text-gray-400 hover:text-gray-600",children:e.jsx(oe,{className:"w-6 h-6"})})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8",children:[e.jsxs("div",{className:"bg-gray-50 rounded-lg p-4",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[e.jsx(Ee,{className:"w-5 h-5 mr-2"}),"Personal Information"]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Full Name"}),e.jsx("p",{className:"text-gray-900",children:o.full_name||"Not provided"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Email"}),e.jsx("p",{className:"text-gray-900",children:o.email||"Not provided"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Mobile Number"}),e.jsx("p",{className:"text-gray-900",children:o.mobile_number||"Not provided"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Current Status"}),e.jsx("div",{className:"mt-1",children:_(o.kyc_status)})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Submitted At"}),e.jsx("p",{className:"text-gray-900",children:new Date(o.kyc_submitted_at).toLocaleString()})]}),o.kyc_reviewed_at&&e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Reviewed At"}),e.jsx("p",{className:"text-gray-900",children:new Date(o.kyc_reviewed_at).toLocaleString()})]})]})]}),e.jsxs("div",{className:"bg-gray-50 rounded-lg p-4",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[e.jsx(he,{className:"w-5 h-5 mr-2"}),"Banking Information"]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Account Holder Name"}),e.jsx("p",{className:"text-gray-900",children:o.account_holder_name||"Not provided"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Bank Name"}),e.jsx("p",{className:"text-gray-900",children:o.bank_name||"Not provided"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-500",children:"IFSC Code"}),e.jsx("p",{className:"text-gray-900",children:o.ifsc_code||"Not provided"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Account Number"}),e.jsx("p",{className:"text-gray-900 font-mono",children:o.account_number_encrypted?"****"+o.account_number_encrypted.slice(-4):"Not provided"})]})]})]})]}),e.jsxs("div",{className:"mb-8",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[e.jsx(he,{className:"w-5 h-5 mr-2"}),"Uploaded Documents"]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{className:"border border-gray-200 rounded-lg p-4",children:[e.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"Aadhaar Card (Front)"}),o.aadhaar_front_url?e.jsxs("div",{className:"space-y-2",children:[e.jsx("div",{className:"bg-gray-100 rounded-lg h-32 flex items-center justify-center",children:e.jsx(he,{className:"w-8 h-8 text-gray-400"})}),e.jsxs("button",{onClick:()=>S(o.aadhaar_front_url),className:"w-full bg-blue-600 text-white py-2 px-3 rounded text-sm hover:bg-blue-700 flex items-center justify-center",children:[e.jsx(ne,{className:"w-4 h-4 mr-1"}),"View Document"]})]}):e.jsx("div",{className:"bg-gray-100 rounded-lg h-32 flex items-center justify-center",children:e.jsx("p",{className:"text-gray-500 text-sm",children:"Not uploaded"})})]}),e.jsxs("div",{className:"border border-gray-200 rounded-lg p-4",children:[e.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"Aadhaar Card (Back)"}),o.aadhaar_back_url?e.jsxs("div",{className:"space-y-2",children:[e.jsx("div",{className:"bg-gray-100 rounded-lg h-32 flex items-center justify-center",children:e.jsx(he,{className:"w-8 h-8 text-gray-400"})}),e.jsxs("button",{onClick:()=>S(o.aadhaar_back_url),className:"w-full bg-blue-600 text-white py-2 px-3 rounded text-sm hover:bg-blue-700 flex items-center justify-center",children:[e.jsx(ne,{className:"w-4 h-4 mr-1"}),"View Document"]})]}):e.jsx("div",{className:"bg-gray-100 rounded-lg h-32 flex items-center justify-center",children:e.jsx("p",{className:"text-gray-500 text-sm",children:"Not uploaded"})})]}),e.jsxs("div",{className:"border border-gray-200 rounded-lg p-4",children:[e.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"PAN Card"}),o.pan_card_url?e.jsxs("div",{className:"space-y-2",children:[e.jsx("div",{className:"bg-gray-100 rounded-lg h-32 flex items-center justify-center",children:e.jsx(he,{className:"w-8 h-8 text-gray-400"})}),e.jsxs("button",{onClick:()=>S(o.pan_card_url),className:"w-full bg-blue-600 text-white py-2 px-3 rounded text-sm hover:bg-blue-700 flex items-center justify-center",children:[e.jsx(ne,{className:"w-4 h-4 mr-1"}),"View Document"]})]}):e.jsx("div",{className:"bg-gray-100 rounded-lg h-32 flex items-center justify-center",children:e.jsx("p",{className:"text-gray-500 text-sm",children:"Not uploaded"})})]})]})]}),o.kyc_comments&&e.jsxs("div",{className:"mb-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Previous Review Comments"}),e.jsx("div",{className:"bg-gray-50 rounded-lg p-4",children:e.jsx("p",{className:"text-gray-700",children:o.kyc_comments})})]}),e.jsxs("div",{className:"border-t border-gray-200 pt-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Review Application"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Review Comments *"}),e.jsx("textarea",{value:g,onChange:C=>u(C.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent",rows:4,placeholder:"Add your review comments (required for approval/rejection)..."})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-3",children:[e.jsxs("button",{onClick:()=>E(o.id,"approved"),disabled:j||!g.trim(),className:"flex-1 bg-green-600 text-white py-3 px-4 rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center",children:[j?e.jsx(le,{className:"w-4 h-4 animate-spin mr-2"}):e.jsx(Us,{className:"w-4 h-4 mr-2"}),"Approve KYC"]}),e.jsxs("button",{onClick:()=>E(o.id,"rejected"),disabled:j||!g.trim(),className:"flex-1 bg-red-600 text-white py-3 px-4 rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center",children:[j?e.jsx(le,{className:"w-4 h-4 animate-spin mr-2"}):e.jsx(oe,{className:"w-4 h-4 mr-2"}),"Reject KYC"]}),e.jsx("button",{onClick:()=>{d(null),u("")},className:"flex-1 bg-gray-500 text-white py-3 px-4 rounded-lg hover:bg-gray-600 transition-colors",children:"Cancel"})]})]})]})]})})}),m&&p&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center p-4 z-50",children:e.jsxs("div",{className:"bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[e.jsxs("div",{className:"p-4 border-b border-gray-200 flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Document Viewer"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("button",{onClick:()=>window.open(p,"_blank"),className:"bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 flex items-center",children:[e.jsx(br,{className:"w-4 h-4 mr-1"}),"Download"]}),e.jsx("button",{onClick:()=>h(!1),className:"text-gray-400 hover:text-gray-600",children:e.jsx(oe,{className:"w-6 h-6"})})]})]}),e.jsx("div",{className:"p-4 max-h-[calc(90vh-120px)] overflow-auto",children:p.toLowerCase().includes(".pdf")?e.jsx("iframe",{src:p,className:"w-full h-96 border border-gray-300 rounded",title:"Document Viewer"}):e.jsx("img",{src:p,alt:"Document",className:"max-w-full h-auto mx-auto rounded",onError:C=>{const T=C.target;T.style.display="none";const F=document.createElement("div");F.className="text-center py-8 text-gray-500",F.innerHTML=`
                      <div class="flex flex-col items-center">
                        <svg class="w-16 h-16 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <p>Unable to display document</p>
                        <p class="text-sm">Click download to view the file</p>
                      </div>
                    `,T.parentNode?.appendChild(F)}})})]})})]})};function mo(){const[t,s]=f.useState([]),[r,a]=f.useState(!0),[l,n]=f.useState(""),[i,c]=f.useState("all"),[o,d]=f.useState(null),[m,h]=f.useState(!1);f.useEffect(()=>{p(),x()},[]);const p=async()=>{try{a(!0);const{data:b,error:N}=await y.from("users").select("id, email, full_name, is_premium, referral_code, created_at, premium_purchased_at").order("created_at",{ascending:!1});if(N)throw N;s(b||[])}catch(b){console.error("Error fetching users:",b),k.error("Failed to fetch users")}finally{a(!1)}},x=async()=>{try{const b=await Ze.getReferralCodeStats();d(b)}catch(b){console.error("Error fetching stats:",b)}},g=async(b,N)=>{try{const P=await Ze.generateAndAssignForPremiumUser(b,N);P.success?(k.success(`Referral code generated: ${P.referralCode}`),await p(),await x()):k.error(P.error||"Failed to generate referral code")}catch(P){console.error("Error generating code:",P),k.error("Failed to generate referral code")}},u=async()=>{try{h(!0);const b=await Ze.generateCodesForExistingPremiumUsers();k.success(`Processed ${b.processed} users. Generated ${b.successful} codes.`),b.errors.length>0&&(console.error("Bulk generation errors:",b.errors),k.error(`${b.errors.length} errors occurred. Check console for details.`)),await p(),await x()}catch(b){console.error("Error in bulk generation:",b),k.error("Failed to bulk generate codes")}finally{h(!1)}},j=t.filter(b=>{const N=b.email.toLowerCase().includes(l.toLowerCase())||b.full_name.toLowerCase().includes(l.toLowerCase())||b.referral_code&&b.referral_code.toLowerCase().includes(l.toLowerCase()),P=(()=>{switch(i){case"premium":return b.is_premium;case"no_code":return b.is_premium&&!b.referral_code;case"with_code":return b.referral_code!==null;default:return!0}})();return N&&P});return r?e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"})}):e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsxs("h1",{className:"text-2xl font-bold text-gray-900 flex items-center gap-2",children:[e.jsx(ce,{className:"h-6 w-6 text-green-600"}),"Referral Code Management"]}),e.jsx("p",{className:"text-gray-600 mt-1",children:"Manage and generate referral codes for premium users"})]}),e.jsxs("button",{onClick:u,disabled:m,className:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center gap-2 disabled:opacity-50",children:[m?e.jsx(le,{className:"h-4 w-4 animate-spin"}):e.jsx(re,{className:"h-4 w-4"}),"Bulk Generate for Premium Users"]})]}),o&&e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"bg-white p-4 rounded-lg border",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(ia,{className:"h-5 w-5 text-blue-600"}),e.jsx("span",{className:"text-sm font-medium text-gray-600",children:"Total Codes"})]}),e.jsx("p",{className:"text-2xl font-bold text-gray-900 mt-1",children:o.total_codes})]}),e.jsxs("div",{className:"bg-white p-4 rounded-lg border",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(H,{className:"h-5 w-5 text-green-600"}),e.jsx("span",{className:"text-sm font-medium text-gray-600",children:"Premium Codes"})]}),e.jsx("p",{className:"text-2xl font-bold text-gray-900 mt-1",children:o.premium_user_codes})]}),e.jsxs("div",{className:"bg-white p-4 rounded-lg border",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(ce,{className:"h-5 w-5 text-orange-600"}),e.jsx("span",{className:"text-sm font-medium text-gray-600",children:"Coverage"})]}),e.jsxs("p",{className:"text-2xl font-bold text-gray-900 mt-1",children:[o.code_coverage,"%"]})]}),e.jsxs("div",{className:"bg-white p-4 rounded-lg border",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(le,{className:"h-5 w-5 text-purple-600"}),e.jsx("span",{className:"text-sm font-medium text-gray-600",children:"Missing Codes"})]}),e.jsx("p",{className:"text-2xl font-bold text-gray-900 mt-1",children:j.filter(b=>b.is_premium&&!b.referral_code).length})]})]}),e.jsx("div",{className:"bg-white p-4 rounded-lg border",children:e.jsxs("div",{className:"flex flex-col sm:flex-row gap-4",children:[e.jsx("div",{className:"flex-1",children:e.jsxs("div",{className:"relative",children:[e.jsx(ue,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),e.jsx("input",{type:"text",placeholder:"Search by email, name, or referral code...",value:l,onChange:b=>n(b.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"})]})}),e.jsxs("select",{value:i,onChange:b=>c(b.target.value),className:"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent",children:[e.jsx("option",{value:"all",children:"All Users"}),e.jsx("option",{value:"premium",children:"Premium Users"}),e.jsx("option",{value:"no_code",children:"Premium Without Code"}),e.jsx("option",{value:"with_code",children:"Users With Code"})]})]})}),e.jsxs("div",{className:"bg-white rounded-lg border overflow-hidden",children:[e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Referral Code"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Premium Date"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:j.map(b=>e.jsxs("tr",{className:"hover:bg-gray-50",children:[e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium text-gray-900",children:b.full_name}),e.jsx("div",{className:"text-sm text-gray-500",children:b.email})]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${b.is_premium?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"}`,children:b.is_premium?"Premium":"Regular"})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:b.referral_code?e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("code",{className:"bg-gray-100 px-2 py-1 rounded text-sm font-mono",children:b.referral_code}),e.jsx("button",{onClick:()=>navigator.clipboard.writeText(b.referral_code),className:"text-gray-400 hover:text-gray-600",title:"Copy code",children:e.jsx(ne,{className:"h-4 w-4"})})]}):e.jsx("span",{className:"text-gray-400 text-sm",children:"No code"})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:b.premium_purchased_at?new Date(b.premium_purchased_at).toLocaleDateString():"-"}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:b.is_premium&&!b.referral_code&&e.jsx("button",{onClick:()=>g(b.id,b.email),className:"bg-green-600 text-white px-3 py-1 rounded text-xs hover:bg-green-700 transition-colors",children:"Generate Code"})})]},b.id))})]})}),j.length===0&&e.jsxs("div",{className:"text-center py-8",children:[e.jsx(ce,{className:"mx-auto h-12 w-12 text-gray-400"}),e.jsx("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No users found"}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Try adjusting your search or filter criteria."})]})]})]})}const Pt=()=>{const[t,s]=f.useState([]),[r,a]=f.useState(!0),[l,n]=f.useState(!1),[i,c]=f.useState({});f.useEffect(()=>{o()},[]);const o=async()=>{try{a(!0);const{data:u,error:j}=await y.from("admin_settings").select("*").in("setting_key",["referral_bonus_amount","premium_referral_bonus_amount"]).order("setting_key");if(j)throw j;s(u||[]);const b={};u?.forEach(N=>{b[N.setting_key]=N.setting_value}),c(b)}catch(u){console.error("Error loading settings:",u),B.error("Failed to load bonus settings")}finally{a(!1)}},d=(u,j)=>{c(b=>({...b,[u]:j}))},m=async()=>{try{n(!0);for(const[u,j]of Object.entries(i)){const b=parseFloat(j);if(isNaN(b)||b<0)throw new Error(`Invalid value for ${u}: must be a positive number`)}for(const[u,j]of Object.entries(i)){const{error:b}=await y.from("admin_settings").update({setting_value:j,updated_at:new Date().toISOString()}).eq("setting_key",u);if(b)throw b}B.success("Bonus settings updated successfully!"),o()}catch(u){console.error("Error saving settings:",u),B.error(u.message||"Failed to save settings")}finally{n(!1)}},h=()=>t.some(u=>i[u.setting_key]!==u.setting_value),p=u=>{const j=parseFloat(u);return isNaN(j)?"₹0.00":`₹${j.toFixed(2)}`},x=u=>{switch(u){case"referral_bonus_amount":return"Standard Referral Bonus";case"premium_referral_bonus_amount":return"Premium Referral Bonus";case"wallet_unlock_bonus_amount":return"E-wallet Unlock Bonus";default:return u}},g=u=>{switch(u){case"referral_bonus_amount":return"👥";case"premium_referral_bonus_amount":return"⭐";case"wallet_unlock_bonus_amount":return"🔓";default:return"⚙️"}};return r?e.jsxs("div",{className:"flex items-center justify-center p-8",children:[e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),e.jsx("span",{className:"ml-2",children:"Loading bonus settings..."})]}):e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Bonus Settings"}),e.jsx("p",{className:"text-gray-600",children:"Configure referral and wallet bonus amounts. Changes apply immediately to new transactions."})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:t.map(u=>e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("span",{className:"text-2xl mr-3",children:g(u.setting_key)}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:x(u.setting_key)}),e.jsx("p",{className:"text-sm text-gray-600",children:u.description})]})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Amount (₹)"}),e.jsx("input",{type:"number",step:"0.01",min:"0",value:i[u.setting_key]||"",onChange:j=>d(u.setting_key,j.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"0.00"})]}),e.jsxs("div",{className:"bg-gray-50 p-3 rounded-lg",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Current Value:"}),e.jsx("span",{className:"font-medium text-gray-900",children:p(u.setting_value)})]}),e.jsxs("div",{className:"flex justify-between items-center mt-1",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"New Value:"}),e.jsx("span",{className:"font-medium text-blue-600",children:p(i[u.setting_key]||"0")})]})]}),e.jsxs("div",{className:"text-xs text-gray-500",children:["Last updated: ",new Date(u.updated_at).toLocaleString("en-IN")]})]})]},u.setting_key))}),e.jsx("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Save Changes"}),e.jsx("p",{className:"text-sm text-gray-600",children:h()?"You have unsaved changes. Click save to apply them.":"No changes to save."})]}),e.jsxs("div",{className:"flex space-x-3",children:[e.jsx("button",{onClick:o,disabled:l,className:"px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50",children:"🔄 Reset"}),e.jsx("button",{onClick:m,disabled:!h()||l,className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:l?"Saving...":"💾 Save Settings"})]})]})}),e.jsxs("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-blue-900 mb-3",children:"💡 How These Settings Work"}),e.jsxs("div",{className:"space-y-2 text-blue-800",children:[e.jsxs("p",{children:[e.jsx("strong",{children:"Standard Referral Bonus:"})," Amount given to referrer when someone joins using their code"]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Premium Referral Bonus:"})," Additional amount when a referred user upgrades to premium"]}),e.jsxs("p",{children:[e.jsx("strong",{children:"E-wallet Unlock Bonus:"})," One-time bonus when user unlocks e-wallet (after 3 premium referrals)"]})]}),e.jsx("div",{className:"mt-4 p-3 bg-blue-100 rounded-lg",children:e.jsxs("p",{className:"text-sm text-blue-700",children:[e.jsx("strong",{children:"Note:"})," Changes apply immediately to new transactions. Existing transactions are not affected."]})})]})]})};function uo(){const[t,s]=f.useState({referrer_bonus:100,referred_bonus:50,max_referrals_per_user:10}),[r,a]=f.useState(!1);f.useEffect(()=>{l()},[]);const l=async()=>{try{const{data:i,error:c}=await y.from("referral_settings").select("*").single();if(c&&c.code!=="PGRST116")throw c;i&&s(i)}catch(i){console.error("Error fetching settings:",i)}},n=async()=>{a(!0);try{const{error:i}=await y.from("referral_settings").upsert(t);if(i)throw i;k.success("Referral settings updated successfully")}catch(i){console.error("Error saving referral settings:",i),k.error("Failed to update referral settings")}finally{a(!1)}};return e.jsxs("div",{children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Settings"}),e.jsx("p",{className:"text-gray-600",children:"Manage application settings and configurations"})]}),e.jsxs("div",{className:"space-y-8",children:[e.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-sm border border-gray-200",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-6",children:[e.jsx(ze,{className:"h-6 w-6 text-green-600"}),e.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Bonus Management"})]}),e.jsx(Pt,{})]}),e.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-sm border border-gray-200",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-6",children:[e.jsx(ce,{className:"h-6 w-6 text-green-600"}),e.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Referral Program Settings"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Referrer Bonus (₹)"}),e.jsx("input",{type:"number",value:t.referrer_bonus,onChange:i=>s({...t,referrer_bonus:Number(i.target.value)}),className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent",min:"0"}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Amount credited to the referrer when someone uses their code"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Referred User Bonus (₹)"}),e.jsx("input",{type:"number",value:t.referred_bonus,onChange:i=>s({...t,referred_bonus:Number(i.target.value)}),className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent",min:"0"}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Welcome bonus for new users who join via referral"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Max Referrals per User"}),e.jsx("input",{type:"number",value:t.max_referrals_per_user,onChange:i=>s({...t,max_referrals_per_user:Number(i.target.value)}),className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent",min:"1"}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Maximum number of referrals allowed per user (0 for unlimited)"})]})]}),e.jsx("div",{className:"mt-6",children:e.jsxs("button",{onClick:n,disabled:r,className:"flex items-center gap-2 bg-green-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-green-700 transition-colors disabled:opacity-50",children:[e.jsx(cs,{className:"h-4 w-4"}),r?"Saving...":"Save Referral Settings"]})})]}),e.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-sm border border-gray-200",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-6",children:[e.jsx(rt,{className:"h-6 w-6 text-yellow-600"}),e.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Premium Settings"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Premium Subscription Price (₹/year)"}),e.jsx("input",{type:"number",defaultValue:999,className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent",min:"0"}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Annual subscription price for premium membership"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Premium Discount Percentage"}),e.jsx("input",{type:"number",defaultValue:15,className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent",min:"0",max:"100"}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Discount percentage for premium members on all products"})]})]}),e.jsx("div",{className:"mt-6",children:e.jsxs("button",{className:"flex items-center gap-2 bg-yellow-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-yellow-700 transition-colors",children:[e.jsx(cs,{className:"h-4 w-4"}),"Save Premium Settings"]})})]}),e.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-sm border border-gray-200",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-6",children:[e.jsx(pr,{className:"h-6 w-6 text-blue-600"}),e.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"System Settings"})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-gray-900",children:"Email Notifications"}),e.jsx("p",{className:"text-sm text-gray-500",children:"Send email notifications for orders and updates"})]}),e.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[e.jsx("input",{type:"checkbox",defaultChecked:!0,className:"sr-only peer"}),e.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"})]})]}),e.jsxs("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-gray-900",children:"SMS Notifications"}),e.jsx("p",{className:"text-sm text-gray-500",children:"Send SMS notifications for order updates"})]}),e.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[e.jsx("input",{type:"checkbox",className:"sr-only peer"}),e.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"})]})]}),e.jsxs("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-gray-900",children:"Maintenance Mode"}),e.jsx("p",{className:"text-sm text-gray-500",children:"Enable maintenance mode for system updates"})]}),e.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[e.jsx("input",{type:"checkbox",className:"sr-only peer"}),e.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-red-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-600"})]})]})]}),e.jsx("div",{className:"mt-6",children:e.jsxs("button",{className:"flex items-center gap-2 bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors",children:[e.jsx(cs,{className:"h-4 w-4"}),"Save System Settings"]})})]})]})]})}const xo={name:"",description:"",price:0,original_price:0,stock_quantity:0,category:"",images:[],benefits:[""],ingredients:[""],dosage:"",warnings:"",is_featured:!1,is_premium_only:!1},ho=({onSuccess:t,onCancel:s})=>{const[r,a]=f.useState(xo),[l,n]=f.useState(!1),[i,c]=f.useState(null),[o,d]=f.useState([]),{addProduct:m}=Ge(),h=A=>{const{name:E,value:_,type:w}=A.target;a(S=>({...S,[E]:w==="number"?parseFloat(_)||0:_}))},p=A=>{const{name:E,checked:_}=A.target;a(w=>({...w,[E]:_}))},x=A=>{const E=Array.from(A.target.files||[]);a(w=>({...w,images:[...w.images,...E]}));const _=E.map(w=>URL.createObjectURL(w));d(w=>[...w,..._])},g=A=>{a(E=>({...E,images:E.images.filter((_,w)=>w!==A)})),d(E=>E.filter((_,w)=>w!==A))},u=(A,E,_)=>{a(w=>({...w,[_]:w[_].map((S,C)=>C===A?E:S)}))},j=A=>{a(E=>({...E,[A]:[...E[A],""]}))},b=(A,E)=>{a(_=>({..._,[E]:_[E].filter((w,S)=>S!==A)}))},N=async A=>{const E=A.map(async _=>{const w=_.name.split(".").pop(),S=`${Math.random().toString(36).substring(2)}-${Date.now()}.${w}`,{data:C,error:T}=await y.storage.from("products").upload(S,_,{cacheControl:"3600",upsert:!1});if(T)throw console.error("Error uploading image:",T),T;const{data:{publicUrl:F}}=y.storage.from("products").getPublicUrl(S);return F});return Promise.all(E)},P=async A=>{A.preventDefault(),n(!0),c(null);try{if(!r.name||!r.description||!r.category||r.images.length===0)throw new Error("Please fill in all required fields and add at least one image");const E=await N(r.images),_={name:r.name,description:r.description,price:r.price,original_price:r.original_price>0?r.original_price:void 0,stock_quantity:r.stock_quantity,category:r.category,image_url:E[0],images:E,benefits:r.benefits.filter(S=>S.trim()!==""),ingredients:r.ingredients.filter(S=>S.trim()!==""),dosage:r.dosage||void 0,warnings:r.warnings||void 0,is_featured:r.is_featured,is_premium_only:r.is_premium_only,rating:0,reviews_count:0},w=await m(_);if(w.success)k.success("Product added successfully!"),t();else throw new Error(w.error||"Failed to add product")}catch(E){console.error("Error adding product:",E),c(E.message||"An error occurred while adding the product"),k.error(E.message||"Failed to add product")}finally{n(!1)}};return e.jsxs("div",{className:"max-w-4xl mx-auto",children:[i&&e.jsxs("div",{className:"bg-red-50 text-red-600 p-4 rounded-lg mb-6 flex items-center gap-2",children:[e.jsx(we,{className:"h-5 w-5"}),i]}),e.jsxs("form",{onSubmit:P,className:"space-y-8",children:[e.jsxs("div",{className:"bg-white rounded-lg shadow p-6",children:[e.jsx("h2",{className:"text-lg font-semibold mb-4",children:"Basic Information"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Product Name *"}),e.jsx("input",{type:"text",name:"name",value:r.name,onChange:h,className:"w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-green-500",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Category *"}),e.jsxs("select",{name:"category",value:r.category,onChange:h,className:"w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-green-500",required:!0,children:[e.jsx("option",{value:"",children:"Select a category"}),e.jsx("option",{value:"Immunity",children:"Immunity"}),e.jsx("option",{value:"Digestion",children:"Digestion"}),e.jsx("option",{value:"Energy",children:"Energy"}),e.jsx("option",{value:"Wellness",children:"Wellness"}),e.jsx("option",{value:"Skincare",children:"Skincare"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Price (₹) *"}),e.jsx("input",{type:"number",name:"price",value:r.price,onChange:h,className:"w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-green-500",min:"0",step:"0.01",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Original Price (₹)"}),e.jsx("input",{type:"number",name:"original_price",value:r.original_price,onChange:h,className:"w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-green-500",min:"0",step:"0.01"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Stock Quantity *"}),e.jsx("input",{type:"number",name:"stock_quantity",value:r.stock_quantity,onChange:h,className:"w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-green-500",min:"0",required:!0})]}),e.jsxs("div",{className:"md:col-span-2",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Description *"}),e.jsx("textarea",{name:"description",value:r.description,onChange:h,rows:4,className:"w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-green-500",required:!0})]})]})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow p-6",children:[e.jsx("h2",{className:"text-lg font-semibold mb-4",children:"Product Images *"}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[o.map((A,E)=>e.jsxs("div",{className:"relative",children:[e.jsx("img",{src:A,alt:`Preview ${E+1}`,className:"w-full h-32 object-cover rounded-lg"}),e.jsx("button",{type:"button",onClick:()=>g(E),className:"absolute top-2 right-2 bg-red-500 text-white p-1 rounded-full hover:bg-red-600",children:e.jsx(oe,{size:16})})]},E)),e.jsxs("label",{className:"border-2 border-dashed border-gray-300 rounded-lg p-4 flex flex-col items-center justify-center cursor-pointer hover:border-green-500",children:[e.jsx(qe,{className:"w-8 h-8 text-gray-400 mb-2"}),e.jsx("span",{className:"text-sm text-gray-500",children:"Add Image"}),e.jsx("input",{type:"file",accept:"image/*",onChange:x,className:"hidden",multiple:!0})]})]})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow p-6",children:[e.jsx("h2",{className:"text-lg font-semibold mb-4",children:"Benefits"}),r.benefits.map((A,E)=>e.jsxs("div",{className:"flex gap-2 mb-2",children:[e.jsx("input",{type:"text",value:A,onChange:_=>u(E,_.target.value,"benefits"),className:"flex-1 px-4 py-2 border rounded-lg focus:ring-2 focus:ring-green-500",placeholder:"Enter benefit"}),e.jsx("button",{type:"button",onClick:()=>b(E,"benefits"),className:"px-3 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600",children:e.jsx(oe,{size:16})})]},E)),e.jsxs("button",{type:"button",onClick:()=>j("benefits"),className:"mt-2 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 flex items-center gap-2",children:[e.jsx(re,{size:16}),"Add Benefit"]})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow p-6",children:[e.jsx("h2",{className:"text-lg font-semibold mb-4",children:"Ingredients"}),r.ingredients.map((A,E)=>e.jsxs("div",{className:"flex gap-2 mb-2",children:[e.jsx("input",{type:"text",value:A,onChange:_=>u(E,_.target.value,"ingredients"),className:"flex-1 px-4 py-2 border rounded-lg focus:ring-2 focus:ring-green-500",placeholder:"Enter ingredient"}),e.jsx("button",{type:"button",onClick:()=>b(E,"ingredients"),className:"px-3 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600",children:e.jsx(oe,{size:16})})]},E)),e.jsxs("button",{type:"button",onClick:()=>j("ingredients"),className:"mt-2 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 flex items-center gap-2",children:[e.jsx(re,{size:16}),"Add Ingredient"]})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow p-6",children:[e.jsx("h2",{className:"text-lg font-semibold mb-4",children:"Additional Information"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Dosage Instructions"}),e.jsx("textarea",{name:"dosage",value:r.dosage,onChange:h,rows:3,className:"w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-green-500"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Warnings"}),e.jsx("textarea",{name:"warnings",value:r.warnings,onChange:h,rows:3,className:"w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-green-500"})]}),e.jsxs("div",{className:"flex gap-4",children:[e.jsxs("label",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",name:"is_featured",checked:r.is_featured,onChange:p,className:"w-4 h-4 text-green-500 rounded focus:ring-green-500"}),e.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Featured Product"})]}),e.jsxs("label",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",name:"is_premium_only",checked:r.is_premium_only,onChange:p,className:"w-4 h-4 text-green-500 rounded focus:ring-green-500"}),e.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Premium Only Product"})]})]})]})]}),e.jsxs("div",{className:"flex justify-end gap-4",children:[e.jsx("button",{type:"button",onClick:s,className:"px-6 py-3 bg-gray-500 text-white rounded-lg hover:bg-gray-600",children:"Cancel"}),e.jsxs("button",{type:"submit",disabled:l,className:"px-6 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 flex items-center gap-2 disabled:opacity-50",children:[e.jsx(cs,{size:16}),l?"Adding Product...":"Add Product"]})]})]})]})},go=()=>{const t=Oe(),{fetchCategories:s}=Ge();f.useEffect(()=>{s()},[s,t]);const r=()=>{t("/admin/products")},a=()=>{t("/admin/products")};return e.jsxs("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-8",children:[e.jsxs("div",{className:"mb-4 sm:mb-6",children:[e.jsx("h1",{className:"text-xl sm:text-2xl font-bold text-gray-900",children:"Add New Product"}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Fill out the form below to add a new product to the store."})]}),e.jsx(ho,{onSuccess:r,onCancel:a})]})};function fo(){return e.jsxs("div",{children:[e.jsxs("div",{className:"mb-6 sm:mb-8",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[e.jsx("div",{className:"p-2 bg-green-100 rounded-lg",children:e.jsx(ze,{className:"h-5 w-5 sm:h-6 sm:w-6 text-green-600"})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl sm:text-3xl font-bold text-gray-900",children:"Bonus Settings"}),e.jsx("p",{className:"text-gray-600 text-sm sm:text-base",children:"Configure referral and wallet bonus amounts"})]})]}),e.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-3 sm:p-4",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx(oa,{className:"h-4 w-4 sm:h-5 sm:w-5 text-blue-600 mt-0.5 flex-shrink-0"}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-blue-900 mb-1 text-sm sm:text-base",children:"How Bonus Settings Work"}),e.jsxs("div",{className:"text-xs sm:text-sm text-blue-800 space-y-1",children:[e.jsxs("p",{children:["• ",e.jsx("strong",{children:"Standard Referral Bonus:"})," Amount given when someone joins using a referral code"]}),e.jsxs("p",{children:["• ",e.jsx("strong",{children:"Premium Referral Bonus:"})," Additional amount when referred user upgrades to premium"]}),e.jsxs("p",{children:["• ",e.jsx("strong",{children:"E-wallet Unlock Bonus:"})," One-time bonus when user makes 3 premium referrals"]}),e.jsxs("p",{children:["• ",e.jsx("strong",{children:"No Welcome Bonus:"})," New users start with ₹0.00 - earnings only through referrals"]})]})]})]})})]}),e.jsx(Pt,{}),e.jsxs("div",{className:"mt-6 sm:mt-8 grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6",children:[e.jsxs("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 sm:p-6",children:[e.jsx("h3",{className:"font-semibold text-green-900 mb-3 text-sm sm:text-base",children:"✅ Current System Features"}),e.jsxs("ul",{className:"space-y-2 text-green-800 text-xs sm:text-sm",children:[e.jsx("li",{children:"• No welcome bonus for new users"}),e.jsx("li",{children:"• Referral-only earning system"}),e.jsx("li",{children:"• Admin-configurable bonus amounts"}),e.jsx("li",{children:"• Real-time updates to new transactions"}),e.jsx("li",{children:"• 3rd referral special rule active"}),e.jsx("li",{children:"• E-wallet unlock after 3 premium referrals"})]})]}),e.jsxs("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4 sm:p-6",children:[e.jsx("h3",{className:"font-semibold text-yellow-900 mb-3 text-sm sm:text-base",children:"⚠️ Important Notes"}),e.jsxs("ul",{className:"space-y-2 text-yellow-800 text-xs sm:text-sm",children:[e.jsx("li",{children:"• Changes apply immediately to new transactions"}),e.jsx("li",{children:"• Existing wallet balances remain unchanged"}),e.jsx("li",{children:"• Premium referral bonuses are additional to standard bonuses"}),e.jsx("li",{children:"• E-wallet unlock is a one-time bonus per user"}),e.jsx("li",{children:"• All amounts are in Indian Rupees (₹)"}),e.jsx("li",{children:"• Minimum value is ₹0.00 for all bonuses"})]})]})]}),e.jsxs("div",{className:"mt-6 sm:mt-8 bg-gray-50 rounded-lg p-4 sm:p-6",children:[e.jsx("h3",{className:"font-semibold text-gray-900 mb-4 text-sm sm:text-base",children:"Quick Actions"}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4",children:[e.jsxs("button",{onClick:()=>window.location.href="/admin/users",className:"flex items-center justify-center gap-2 p-3 sm:p-4 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors touch-target",children:[e.jsx("span",{className:"text-lg sm:text-xl",children:"👥"}),e.jsx("span",{className:"font-medium text-sm sm:text-base",children:"Manage Users"})]}),e.jsxs("button",{onClick:()=>window.location.href="/admin/wallet",className:"flex items-center justify-center gap-2 p-3 sm:p-4 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors touch-target",children:[e.jsx("span",{className:"text-lg sm:text-xl",children:"💰"}),e.jsx("span",{className:"font-medium text-sm sm:text-base",children:"Wallet Management"})]}),e.jsxs("button",{onClick:()=>window.location.href="/admin",className:"flex items-center justify-center gap-2 p-3 sm:p-4 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors touch-target sm:col-span-2 lg:col-span-1",children:[e.jsx("span",{className:"text-lg sm:text-xl",children:"📊"}),e.jsx("span",{className:"font-medium text-sm sm:text-base",children:"View Analytics"})]})]})]})]})}const po=()=>{const[t,s]=f.useState([]),[r,a]=f.useState(null),[l,n]=f.useState(!0),[i,c]=f.useState(""),[o,d]=f.useState("all"),[m,h]=f.useState(!1),[p,x]=f.useState(""),[g,u]=f.useState("");f.useEffect(()=>{j()},[]);const j=async()=>{try{n(!0);let S=y.from("orders").select("*").order("created_at",{ascending:!1});o!=="all"&&(S=S.eq("status",o));const{data:C,error:T}=await S;if(T)throw T;let I=await Promise.all((C||[]).map(async v=>{const{data:$}=await y.from("users").select("id, full_name, email").eq("id",v.user_id).single(),{data:O}=await y.from("order_items").select(`
              id,
              quantity,
              price,
              products (
                id,
                name,
                image_url,
                sku
              )
            `).eq("order_id",v.id);return{...v,users:$,order_items:O||[]}}));i&&(I=I.filter(v=>v.order_number?.toLowerCase().includes(i.toLowerCase())||v.users?.full_name?.toLowerCase().includes(i.toLowerCase())||v.users?.email?.toLowerCase().includes(i.toLowerCase()))),s(I)}catch(S){console.error("Error loading orders:",S),B.error("Failed to load orders")}finally{n(!1)}},b=async()=>{if(!(!r||!p))try{const{data:S,error:C}=await y.rpc("update_order_status",{p_order_id:r.id,p_new_status:p,p_notes:g||null});if(C)throw C;S?.success?(B.success(`Order status updated to ${p}`),h(!1),a(null),x(""),u(""),j()):B.error(S?.error||"Failed to update order status")}catch(S){console.error("Error updating order status:",S),B.error("Failed to update order status")}},N=async S=>{try{const{data:C,error:T}=await y.rpc("create_shipment",{p_order_id:S});if(T)throw T;C?.success?(B.success(`Shipment created! Tracking: ${C.tracking_number}`),j()):B.error("Failed to create shipment")}catch(C){console.error("Error creating shipment:",C),B.error("Failed to create shipment")}},P=S=>{switch(S){case"pending":return e.jsx(Ne,{className:"h-4 w-4 text-yellow-500"});case"confirmed":return e.jsx(V,{className:"h-4 w-4 text-blue-500"});case"processing":return e.jsx(Z,{className:"h-4 w-4 text-purple-500"});case"shipped":return e.jsx(De,{className:"h-4 w-4 text-orange-500"});case"delivered":return e.jsx(V,{className:"h-4 w-4 text-green-500"});case"cancelled":return e.jsx(Ns,{className:"h-4 w-4 text-red-500"});default:return e.jsx(Ne,{className:"h-4 w-4 text-gray-500"})}},A=S=>{switch(S){case"pending":return"bg-yellow-100 text-yellow-800";case"confirmed":return"bg-blue-100 text-blue-800";case"processing":return"bg-purple-100 text-purple-800";case"shipped":return"bg-orange-100 text-orange-800";case"delivered":return"bg-green-100 text-green-800";case"cancelled":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},E=S=>new Date(S).toLocaleDateString("en-IN",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),_=S=>`₹${S.toFixed(2)}`,w=S=>({pending:["confirmed","cancelled"],confirmed:["processing","cancelled"],processing:["shipped","cancelled"],shipped:["delivered"],delivered:[],cancelled:[]})[S]||[];return l?e.jsxs("div",{className:"flex items-center justify-center p-8",children:[e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),e.jsx("span",{className:"ml-2",children:"Loading orders..."})]}):e.jsxs("div",{className:"space-y-4 sm:space-y-6",children:[e.jsx("div",{className:"bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-4 sm:p-6 text-white",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl sm:text-2xl font-bold mb-2",children:"Order Management"}),e.jsx("p",{className:"opacity-90 text-sm sm:text-base",children:"Manage customer orders and fulfillment"})]}),e.jsx(Z,{className:"h-8 w-8 sm:h-12 sm:w-12 opacity-80"})]})}),e.jsx("div",{className:"bg-white p-4 rounded-lg shadow-sm border",children:e.jsxs("div",{className:"flex flex-col gap-4",children:[e.jsx("div",{className:"flex-1",children:e.jsxs("div",{className:"relative",children:[e.jsx(ue,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),e.jsx("input",{type:"text",placeholder:"Search orders...",value:i,onChange:S=>c(S.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm sm:text-base"})]})}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-2",children:[e.jsxs("select",{value:o,onChange:S=>d(S.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm sm:text-base",children:[e.jsx("option",{value:"all",children:"All Status"}),e.jsx("option",{value:"pending",children:"Pending"}),e.jsx("option",{value:"confirmed",children:"Confirmed"}),e.jsx("option",{value:"processing",children:"Processing"}),e.jsx("option",{value:"shipped",children:"Shipped"}),e.jsx("option",{value:"delivered",children:"Delivered"}),e.jsx("option",{value:"cancelled",children:"Cancelled"})]}),e.jsxs("button",{onClick:j,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center justify-center gap-2 touch-target",children:[e.jsx(le,{className:"h-4 w-4"}),e.jsx("span",{className:"hidden sm:inline",children:"Refresh"})]})]})]})}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border",children:[e.jsx("div",{className:"p-4 sm:p-6 border-b",children:e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-3",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900",children:["Orders (",t.length,")"]}),e.jsxs("button",{className:"flex items-center gap-2 px-3 py-2 text-gray-600 hover:bg-gray-50 rounded-lg touch-target w-fit",children:[e.jsx(br,{className:"h-4 w-4"}),e.jsx("span",{className:"hidden sm:inline",children:"Export"})]})]})}),e.jsx("div",{className:"divide-y",children:t.length===0?e.jsxs("div",{className:"p-8 text-center text-gray-500",children:[e.jsx(Z,{className:"h-12 w-12 text-gray-300 mx-auto mb-2"}),e.jsx("p",{children:"No orders found"})]}):t.map(S=>e.jsxs("div",{className:"p-4 sm:p-6 hover:bg-gray-50",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between mb-4 gap-3",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3",children:[e.jsx("span",{className:"font-mono text-sm font-medium text-gray-600",children:S.order_number}),e.jsxs("div",{className:"flex items-center gap-2",children:[P(S.status),e.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${A(S.status)} w-fit`,children:S.status.charAt(0).toUpperCase()+S.status.slice(1)})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[S.status==="confirmed"&&e.jsxs("button",{onClick:()=>N(S.id),className:"px-3 py-1 bg-orange-600 text-white text-sm rounded hover:bg-orange-700 touch-target",children:[e.jsx("span",{className:"hidden sm:inline",children:"Create Shipment"}),e.jsx("span",{className:"sm:hidden",children:"Ship"})]}),e.jsx("button",{onClick:()=>{a(S),h(!0)},className:"p-2 text-blue-600 hover:bg-blue-50 rounded touch-target",children:e.jsx(gs,{className:"h-4 w-4"})})]})]}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 text-sm",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(Ee,{className:"h-4 w-4 text-gray-400"}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium",children:S.users?.full_name||"Unknown User"}),e.jsx("p",{className:"text-gray-500",children:S.users?.email||"No email"})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(xs,{className:"h-4 w-4 text-gray-400"}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium",children:_(S.total_amount)}),e.jsx("p",{className:"text-gray-500",children:S.wallet_used>0?"Wallet":"COD"})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(fr,{className:"h-4 w-4 text-gray-400"}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium",children:E(S.created_at)}),e.jsxs("p",{className:"text-gray-500",children:[S.order_items?.length||0," items"]})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(rs,{className:"h-4 w-4 text-gray-400"}),e.jsx("div",{children:e.jsx("p",{className:"font-medium text-xs",children:S.delivery_address})})]})]})]},S.id))})]}),m&&r&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:e.jsxs("div",{className:"bg-white rounded-lg max-w-md w-full p-6",children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Update Order Status"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Current Status: ",r.status]}),e.jsxs("select",{value:p,onChange:S=>x(S.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"",children:"Select new status"}),w(r.status).map(S=>e.jsx("option",{value:S,children:S.charAt(0).toUpperCase()+S.slice(1)},S))]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Notes (optional)"}),e.jsx("textarea",{value:g,onChange:S=>u(S.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Add notes about this status change..."})]})]}),e.jsxs("div",{className:"flex justify-end gap-3 mt-6",children:[e.jsx("button",{onClick:()=>{h(!1),a(null),x(""),u("")},className:"px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg",children:"Cancel"}),e.jsx("button",{onClick:b,disabled:!p,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:"Update Status"})]})]})})]})},bo=()=>{const[t,s]=f.useState([]),[r,a]=f.useState([]),[l,n]=f.useState([]),[i,c]=f.useState(!0),[o,d]=f.useState(""),[m,h]=f.useState("all"),[p,x]=f.useState(!1),[g,u]=f.useState(null),[j,b]=f.useState("add"),[N,P]=f.useState(""),[A,E]=f.useState("");f.useEffect(()=>{_()},[]);const _=async()=>{try{c(!0);const{data:v,error:$}=await y.from("products").select("*").order("name");if($)throw $;let O=[];try{const{data:G,error:ee}=await y.from("inventory_transactions").select(`
            *,
            products!inner(
              name,
              sku
            )
          `).order("created_at",{ascending:!1}).limit(50);if(ee){console.warn("Transactions error:",ee);const{data:Q}=await y.from("inventory_transactions").select("*").order("created_at",{ascending:!1}).limit(10);O=(Q||[]).map(ve=>({...ve,products:{name:"Unknown Product",sku:"N/A"}}))}else O=G||[]}catch(G){console.warn("Error loading transactions:",G),O=[]}let z=[];try{const{data:G,error:ee}=await y.rpc("get_low_stock_products");ee?(console.warn("Low stock RPC error, using manual calculation:",ee.message),z=(v||[]).filter(Q=>Q.stock_quantity<=(Q.low_stock_threshold||5))):z=G||[]}catch{console.warn("RPC function not available, using manual calculation"),z=(v||[]).filter(ee=>ee.stock_quantity<=(ee.low_stock_threshold||5))}s(v||[]),a(O),n(z||[])}catch(v){console.error("Error loading inventory data:",v),B.error("Failed to load inventory data")}finally{c(!1)}},w=async()=>{if(!(!g||!N))try{const v=parseInt(N),$=j==="add"?v:-v,{data:O,error:z}=await y.rpc("process_inventory_transaction",{p_product_id:g.id,p_transaction_type:"adjustment",p_quantity_change:$,p_reference_type:"manual",p_notes:A||`Manual ${j==="add"?"addition":"subtraction"} of ${v} units`});if(z)throw z;O?.success?(B.success(`Stock ${j==="add"?"added":"removed"} successfully`),x(!1),u(null),P(""),E(""),_()):B.error(O?.error||"Failed to adjust stock")}catch(v){console.error("Error adjusting stock:",v),B.error("Failed to adjust stock")}},S=async(v,$)=>{try{const{error:O}=await y.from("products").update({low_stock_threshold:$}).eq("id",v);if(O)throw O;B.success("Low stock threshold updated"),_()}catch(O){console.error("Error updating threshold:",O),B.error("Failed to update threshold")}},C=v=>v.stock_quantity===0?"out_of_stock":v.stock_quantity<=v.low_stock_threshold?"low_stock":"in_stock",T=v=>{switch(v){case"out_of_stock":return"bg-red-100 text-red-800";case"low_stock":return"bg-yellow-100 text-yellow-800";case"in_stock":return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}},F=v=>{switch(v){case"out_of_stock":return e.jsx(Is,{className:"h-4 w-4 text-red-500"});case"low_stock":return e.jsx(Rr,{className:"h-4 w-4 text-yellow-500"});case"in_stock":return e.jsx(Be,{className:"h-4 w-4 text-green-500"});default:return e.jsx(Z,{className:"h-4 w-4 text-gray-500"})}},I=t.filter(v=>{if(!(v.name.toLowerCase().includes(o.toLowerCase())||v.sku.toLowerCase().includes(o.toLowerCase())))return!1;const O=C(v);return m==="all"?!0:O===m});return i?e.jsxs("div",{className:"flex items-center justify-center p-8",children:[e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),e.jsx("span",{className:"ml-2",children:"Loading inventory..."})]}):e.jsxs("div",{className:"space-y-4 sm:space-y-6",children:[e.jsx("div",{className:"bg-gradient-to-r from-green-600 to-blue-600 rounded-lg p-4 sm:p-6 text-white",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl sm:text-2xl font-bold mb-2",children:"Inventory Management"}),e.jsx("p",{className:"opacity-90 text-sm sm:text-base",children:"Monitor stock levels and manage inventory"})]}),e.jsx(Z,{className:"h-8 w-8 sm:h-12 sm:w-12 opacity-80"})]})}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6",children:[e.jsx("div",{className:"bg-white p-4 sm:p-6 rounded-lg shadow-sm border",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Total Products"}),e.jsx("p",{className:"text-xl sm:text-2xl font-bold text-gray-900",children:t.length})]}),e.jsx(Z,{className:"h-6 w-6 sm:h-8 sm:w-8 text-blue-500"})]})}),e.jsx("div",{className:"bg-white p-4 sm:p-6 rounded-lg shadow-sm border",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Low Stock Items"}),e.jsx("p",{className:"text-xl sm:text-2xl font-bold text-yellow-600",children:l.length})]}),e.jsx(Rr,{className:"h-6 w-6 sm:h-8 sm:w-8 text-yellow-500"})]})}),e.jsx("div",{className:"bg-white p-4 sm:p-6 rounded-lg shadow-sm border",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Out of Stock"}),e.jsx("p",{className:"text-xl sm:text-2xl font-bold text-red-600",children:t.filter(v=>v.stock_quantity===0).length})]}),e.jsx(Is,{className:"h-6 w-6 sm:h-8 sm:w-8 text-red-500"})]})}),e.jsx("div",{className:"bg-white p-4 sm:p-6 rounded-lg shadow-sm border",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Total Stock Value"}),e.jsxs("p",{className:"text-lg sm:text-2xl font-bold text-green-600",children:["₹",t.reduce((v,$)=>v+$.stock_quantity*$.price,0).toFixed(0)]})]}),e.jsx(ar,{className:"h-6 w-6 sm:h-8 sm:w-8 text-green-500"})]})})]}),e.jsx("div",{className:"bg-white p-4 rounded-lg shadow-sm border",children:e.jsxs("div",{className:"flex flex-col md:flex-row gap-4",children:[e.jsx("div",{className:"flex-1",children:e.jsxs("div",{className:"relative",children:[e.jsx(ue,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),e.jsx("input",{type:"text",placeholder:"Search products by name or SKU...",value:o,onChange:v=>d(v.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs("select",{value:m,onChange:v=>h(v.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"all",children:"All Stock Levels"}),e.jsx("option",{value:"in_stock",children:"In Stock"}),e.jsx("option",{value:"low_stock",children:"Low Stock"}),e.jsx("option",{value:"out_of_stock",children:"Out of Stock"})]}),e.jsxs("button",{onClick:_,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center gap-2",children:[e.jsx(le,{className:"h-4 w-4"}),"Refresh"]})]})]})}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border",children:[e.jsx("div",{className:"p-6 border-b",children:e.jsxs("h3",{className:"text-lg font-semibold text-gray-900",children:["Products (",I.length,")"]})}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Product"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"SKU"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Stock"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Threshold"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:I.map(v=>{const $=C(v);return e.jsxs("tr",{className:"hover:bg-gray-50",children:[e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("img",{src:v.image_url||"/placeholder-product.jpg",alt:v.name,className:"w-10 h-10 object-cover rounded-lg mr-3"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium text-gray-900",children:v.name}),e.jsxs("div",{className:"text-sm text-gray-500",children:["₹",v.price]})]})]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900",children:v.sku}),e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap",children:[e.jsx("div",{className:"text-sm font-medium text-gray-900",children:v.stock_quantity}),e.jsx("div",{className:"text-sm text-gray-500",children:"units"})]}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{className:"flex items-center gap-2",children:[F($),e.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${T($)}`,children:$.replace("_"," ").toUpperCase()})]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("input",{type:"number",value:v.low_stock_threshold,onChange:O=>S(v.id,parseInt(O.target.value)||0),className:"w-16 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent",min:"0"})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:e.jsxs("button",{onClick:()=>{u(v),x(!0)},className:"text-blue-600 hover:text-blue-900 flex items-center gap-1",children:[e.jsx(gs,{className:"h-4 w-4"}),"Adjust"]})})]},v.id)})})]})})]}),p&&g&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:e.jsxs("div",{className:"bg-white rounded-lg max-w-md w-full p-6",children:[e.jsxs("h3",{className:"text-lg font-semibold mb-4",children:["Adjust Stock - ",g.name]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{children:e.jsxs("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Current Stock: ",g.stock_quantity," units"]})}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Adjustment Type"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs("button",{onClick:()=>b("add"),className:`flex-1 px-3 py-2 rounded-lg border ${j==="add"?"bg-green-50 border-green-200 text-green-800":"bg-gray-50 border-gray-200 text-gray-600"}`,children:[e.jsx(re,{className:"h-4 w-4 inline mr-1"}),"Add Stock"]}),e.jsxs("button",{onClick:()=>b("subtract"),className:`flex-1 px-3 py-2 rounded-lg border ${j==="subtract"?"bg-red-50 border-red-200 text-red-800":"bg-gray-50 border-gray-200 text-gray-600"}`,children:[e.jsx(ws,{className:"h-4 w-4 inline mr-1"}),"Remove Stock"]})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Quantity"}),e.jsx("input",{type:"number",value:N,onChange:v=>P(v.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Enter quantity",min:"1"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Notes (optional)"}),e.jsx("textarea",{value:A,onChange:v=>E(v.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Reason for adjustment..."})]})]}),e.jsxs("div",{className:"flex justify-end gap-3 mt-6",children:[e.jsx("button",{onClick:()=>{x(!1),u(null),P(""),E("")},className:"px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg",children:"Cancel"}),e.jsxs("button",{onClick:w,disabled:!N,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:[j==="add"?"Add":"Remove"," Stock"]})]})]})})]})},yo=()=>{const{createNotification:t,notifications:s,unreadCount:r}=Et(),[a,l]=f.useState("order"),[n,i]=f.useState("medium"),[c,o]=f.useState(""),[d,m]=f.useState(""),h=[{value:"order",label:"Order",description:"New order notifications"},{value:"user",label:"User",description:"User registration notifications"},{value:"kyc",label:"KYC",description:"KYC submission notifications"},{value:"wallet",label:"Wallet",description:"Wallet transaction notifications"},{value:"product",label:"Product",description:"Product-related notifications"},{value:"referral",label:"Referral",description:"Referral system notifications"},{value:"alert",label:"Alert",description:"System alerts and warnings"}],p=[{value:"low",label:"Low",color:"text-gray-600"},{value:"medium",label:"Medium",color:"text-blue-600"},{value:"high",label:"High",color:"text-orange-600"},{value:"urgent",label:"Urgent",color:"text-red-600"}],x={order:[{title:"New Order Received",message:"Order #1234 for ₹2,500 from <EMAIL>"},{title:"Large Order Alert",message:"Order #1235 for ₹15,000 from premium customer"},{title:"Order Cancelled",message:"Order #1236 was cancelled by customer"}],user:[{title:"New User Registration",message:"Sarah Smith just registered"},{title:"Premium Upgrade",message:"John Doe upgraded to premium account"},{title:"User Verification",message:"Mike Johnson completed email verification"}],kyc:[{title:"KYC Submission",message:"Emma Wilson submitted KYC documents for review"},{title:"KYC Approved",message:"David Brown's KYC has been approved"},{title:"KYC Rejected",message:"Lisa Davis's KYC was rejected - missing documents"}],wallet:[{title:"Large Transaction",message:"₹10,000 credit transaction processed"},{title:"Wallet Unlock",message:"User wallet unlocked after 3 referrals"},{title:"Low Balance Alert",message:"System wallet balance is running low"}],product:[{title:"New Product Added",message:"Green Tea Extract added to inventory"},{title:"Product Updated",message:"Turmeric Capsules price updated"},{title:"Product Discontinued",message:"Old formula vitamins discontinued"}],referral:[{title:"New Referral",message:"Alex Johnson referred by premium user"},{title:"Referral Milestone",message:"User reached 10 successful referrals"},{title:"Referral Bonus Paid",message:"₹500 referral bonus distributed"}],alert:[{title:"Low Stock Alert",message:"Vitamin D3 is running low (2 left)"},{title:"System Maintenance",message:"Scheduled maintenance in 1 hour"},{title:"Security Alert",message:"Multiple failed login attempts detected"}]},g=()=>{if(!c.trim()||!d.trim()){alert("Please fill in both title and message");return}t({type:a,title:c,message:d,priority:n,data:{custom:!0,timestamp:new Date().toISOString()}}),o(""),m("")},u=b=>{t({type:a,title:b.title,message:b.message,priority:n,data:{sample:!0,timestamp:new Date().toISOString()}})},j=()=>{[{type:"order",title:"Bulk Test Order",message:"Test order notification 1",priority:"medium"},{type:"user",title:"Bulk Test User",message:"Test user notification 2",priority:"low"},{type:"kyc",title:"Bulk Test KYC",message:"Test KYC notification 3",priority:"high"},{type:"alert",title:"Bulk Test Alert",message:"Test alert notification 4",priority:"urgent"},{type:"wallet",title:"Bulk Test Wallet",message:"Test wallet notification 5",priority:"medium"}].forEach((N,P)=>{setTimeout(()=>{t(N)},P*500)})};return e.jsxs("div",{className:"p-6 max-w-4xl mx-auto",children:[e.jsxs("div",{className:"mb-8",children:[e.jsxs("h1",{className:"text-3xl font-bold text-gray-900 flex items-center gap-3",children:[e.jsx(Zr,{className:"h-8 w-8 text-green-600"}),"Notification Testing Center"]}),e.jsxs("p",{className:"text-gray-600 mt-2",children:["Test and preview admin notifications. Current notifications: ",s.length," (Unread: ",r,")"]})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[e.jsxs("h2",{className:"text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2",children:[e.jsx(re,{className:"h-5 w-5"}),"Create Custom Notification"]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Type"}),e.jsx("select",{value:a,onChange:b=>l(b.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent",children:h.map(b=>e.jsxs("option",{value:b.value,children:[b.label," - ",b.description]},b.value))})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Priority"}),e.jsx("select",{value:n,onChange:b=>i(b.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent",children:p.map(b=>e.jsx("option",{value:b.value,children:b.label},b.value))})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Title"}),e.jsx("input",{type:"text",value:c,onChange:b=>o(b.target.value),placeholder:"Enter notification title",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Message"}),e.jsx("textarea",{value:d,onChange:b=>m(b.target.value),placeholder:"Enter notification message",rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"})]}),e.jsx("button",{onClick:g,className:"w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors font-medium",children:"Create Notification"})]})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[e.jsxs("h2",{className:"text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2",children:[e.jsx(os,{className:"h-5 w-5"}),"Sample Notifications"]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("p",{className:"text-sm text-gray-600",children:["Quick samples for ",h.find(b=>b.value===a)?.label," notifications:"]}),e.jsx("div",{className:"space-y-2",children:x[a]?.map((b,N)=>e.jsxs("div",{className:"border border-gray-200 rounded-lg p-3",children:[e.jsx("h4",{className:"font-medium text-gray-900 text-sm",children:b.title}),e.jsx("p",{className:"text-gray-600 text-sm",children:b.message}),e.jsx("button",{onClick:()=>u(b),className:"mt-2 text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded hover:bg-blue-200 transition-colors",children:"Create This"})]},N))})]})]})]}),e.jsxs("div",{className:"mt-8 bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[e.jsxs("h2",{className:"text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2",children:[e.jsx(Te,{className:"h-5 w-5"}),"Bulk Testing"]}),e.jsxs("div",{className:"flex flex-wrap gap-4",children:[e.jsxs("button",{onClick:j,className:"bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700 transition-colors font-medium flex items-center gap-2",children:[e.jsx(Te,{className:"h-4 w-4"}),"Create 5 Test Notifications"]}),e.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[e.jsx(Is,{className:"h-4 w-4"}),"This will create multiple notifications with different types and priorities"]})]})]}),e.jsxs("div",{className:"mt-8 bg-gray-50 rounded-lg p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Current Statistics"}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("p",{className:"text-2xl font-bold text-blue-600",children:s.length}),e.jsx("p",{className:"text-sm text-gray-600",children:"Total Notifications"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("p",{className:"text-2xl font-bold text-red-600",children:r}),e.jsx("p",{className:"text-sm text-gray-600",children:"Unread"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("p",{className:"text-2xl font-bold text-green-600",children:s.length-r}),e.jsx("p",{className:"text-sm text-gray-600",children:"Read"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("p",{className:"text-2xl font-bold text-purple-600",children:s.filter(b=>b.priority==="urgent").length}),e.jsx("p",{className:"text-sm text-gray-600",children:"Urgent"})]})]})]})]})};class jo{async getReferralTree(s){try{const{data:r,error:a}=await y.from("users").select(`
          id,
          email,
          full_name,
          referral_code,
          is_premium,
          wallet_balance,
          premium_referral_count,
          ewallet_unlocked,
          created_at,
          premium_purchased_at
        `).eq("id",s).single();if(a||!r)throw new Error("User not found");const l=await this.buildTreeNode(r,0),n=this.calculateTreeStats(l);return{tree:l,stats:n}}catch(r){throw console.error("Error getting referral tree:",r),r}}async buildTreeNode(s,r){const{data:a}=await y.from("wallet_transactions").select("amount").eq("user_id",s.id).eq("reference_type","referral"),l=a?.reduce((o,d)=>o+parseFloat(d.amount),0)||0,{data:n}=await y.from("referrals").select(`
        id,
        referral_order,
        referred_user:users!referrals_referred_user_id_fkey(
          id,
          email,
          full_name,
          referral_code,
          is_premium,
          wallet_balance,
          premium_referral_count,
          ewallet_unlocked,
          created_at,
          premium_purchased_at
        )
      `).eq("referrer_id",s.id).order("referral_order"),i=[];if(n){for(const o of n)if(o.referred_user){const d=await this.buildTreeNode(o.referred_user,r+1);d.parent_id=s.id,d.referral_order=o.referral_order,i.push(d)}}const c=i.reduce((o,d)=>o+d.total_network_size+1,0);return{id:s.id,email:s.email,full_name:s.full_name,referral_code:s.referral_code,is_premium:s.is_premium,wallet_balance:parseFloat(s.wallet_balance||"0"),premium_referral_count:s.premium_referral_count||0,ewallet_unlocked:s.ewallet_unlocked||!1,created_at:s.created_at,premium_purchased_at:s.premium_purchased_at,level:r,children:i,parent_id:s.parent_id,referral_order:s.referral_order,total_earnings:l,direct_referrals:i.length,total_network_size:c}}calculateTreeStats(s){let r=1,a=s.is_premium?1:0,l=s.total_earnings,n=s.level,i=s.wallet_balance;const c=o=>{for(const d of o.children)r++,d.is_premium&&a++,l+=d.total_earnings,i+=d.wallet_balance,n=Math.max(n,d.level),c(d)};return c(s),{total_users:r,total_premium_users:a,total_earnings:l,max_depth:n+1,total_network_value:i}}async getRootUsers(){try{const{data:s,error:r}=await y.from("users").select(`
          id,
          email,
          full_name,
          referral_code,
          is_premium,
          wallet_balance,
          premium_referral_count,
          ewallet_unlocked,
          created_at,
          premium_purchased_at
        `).or("referred_by.is.null,referred_by.eq.").order("created_at");if(r)throw r;const a=[];for(const l of s||[]){const n=await this.buildTreeNode(l,0);a.push(n)}return a}catch(s){throw console.error("Error getting root users:",s),s}}async getNetworkOverview(){try{const s=await this.getRootUsers();let r=0,a=0,l=0,n=0;for(const i of s){const c=this.calculateTreeStats(i);r+=c.total_users,a+=c.total_premium_users,l+=c.total_earnings,n=Math.max(n,c.total_users)}return{total_networks:s.length,total_users:r,total_premium_users:a,total_earnings:l,average_network_size:s.length>0?r/s.length:0,largest_network:n}}catch(s){throw console.error("Error getting network overview:",s),s}}async searchInNetwork(s){try{const{data:r,error:a}=await y.from("users").select(`
          id,
          email,
          full_name,
          referral_code,
          is_premium,
          wallet_balance,
          premium_referral_count,
          ewallet_unlocked,
          created_at,
          premium_purchased_at
        `).or(`email.ilike.%${s}%,full_name.ilike.%${s}%,referral_code.ilike.%${s}%`).limit(20);if(a)throw a;const l=[];for(const n of r||[]){const i=await this.buildTreeNode(n,0);l.push(i)}return l}catch(r){throw console.error("Error searching network:",r),r}}}const er=new jo,wo=({tree:t,stats:s,onUserSelect:r})=>{const[a,l]=f.useState(new Set([t.id])),n=h=>{const p=new Set(a);p.has(h)?p.delete(h):p.add(h),l(p)},i=h=>`₹${h.toFixed(2)}`,c=h=>new Date(h).toLocaleDateString("en-IN"),o=h=>h.level===0?"bg-purple-100 border-purple-300 text-purple-900":h.is_premium?"bg-green-100 border-green-300 text-green-900":"bg-blue-100 border-blue-300 text-blue-900",d=h=>h.level===0?e.jsx(L,{className:"w-4 h-4"}):h.is_premium?e.jsx(Je,{className:"w-4 h-4"}):e.jsx(Ee,{className:"w-4 h-4"}),m=(h,p=!1)=>{const x=a.has(h.id),g=h.children.length>0,u=h.level*24;return e.jsxs("div",{className:"relative",children:[h.level>0&&e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"absolute border-l-2 border-gray-300",style:{left:`${u-12}px`,top:"-12px",height:"24px"}}),e.jsx("div",{className:"absolute border-t-2 border-gray-300",style:{left:`${u-12}px`,top:"12px",width:"12px"}})]}),e.jsxs("div",{className:"flex items-center mb-2 relative",style:{marginLeft:`${u}px`},children:[g&&e.jsx("button",{onClick:()=>n(h.id),className:"mr-2 p-1 hover:bg-gray-200 rounded",children:x?e.jsx(ca,{className:"w-4 h-4 text-gray-600"}):e.jsx(da,{className:"w-4 h-4 text-gray-600"})}),!g&&e.jsx("div",{className:"w-6 mr-2"}),e.jsxs("div",{className:`flex-1 p-3 rounded-lg border-2 cursor-pointer hover:shadow-md transition-all ${o(h)}`,onClick:()=>r?.(h.id),children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[d(h),e.jsxs("div",{children:[e.jsx("div",{className:"font-semibold text-sm",children:h.full_name}),e.jsx("div",{className:"text-xs opacity-75",children:h.email}),h.referral_code&&e.jsx("div",{className:"text-xs font-mono bg-white/50 px-2 py-1 rounded mt-1",children:h.referral_code})]})]}),e.jsxs("div",{className:"text-right text-xs space-y-1",children:[e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx(H,{className:"w-3 h-3"}),e.jsx("span",{children:h.direct_referrals})]}),e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx(K,{className:"w-3 h-3"}),e.jsx("span",{children:i(h.wallet_balance)})]}),h.ewallet_unlocked&&e.jsx("div",{className:"text-green-600 font-semibold",children:"🔓 Unlocked"})]})]}),e.jsxs("div",{className:"mt-2 grid grid-cols-2 gap-2 text-xs",children:[e.jsxs("div",{children:[e.jsx("span",{className:"opacity-75",children:"Level:"})," ",h.level+1]}),e.jsxs("div",{children:[e.jsx("span",{className:"opacity-75",children:"Network:"})," ",h.total_network_size+1]}),e.jsxs("div",{children:[e.jsx("span",{className:"opacity-75",children:"Earnings:"})," ",i(h.total_earnings)]}),e.jsxs("div",{children:[e.jsx("span",{className:"opacity-75",children:"Joined:"})," ",c(h.created_at)]})]}),h.referral_order&&e.jsx("div",{className:"mt-1 text-xs",children:e.jsxs("span",{className:"bg-orange-200 text-orange-800 px-2 py-1 rounded",children:["Referral #",h.referral_order]})})]})]}),g&&x&&e.jsx("div",{className:"relative",children:h.children.map((j,b)=>e.jsx("div",{children:m(j,b===h.children.length-1)},j.id))})]},h.id)};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4",children:[e.jsx("div",{className:"bg-white p-4 rounded-lg border",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(H,{className:"w-5 h-5 text-blue-600"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-600",children:"Total Users"}),e.jsx("div",{className:"text-xl font-bold",children:s.total_users})]})]})}),e.jsx("div",{className:"bg-white p-4 rounded-lg border",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(L,{className:"w-5 h-5 text-green-600"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-600",children:"Premium Users"}),e.jsx("div",{className:"text-xl font-bold",children:s.total_premium_users})]})]})}),e.jsx("div",{className:"bg-white p-4 rounded-lg border",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(ze,{className:"w-5 h-5 text-purple-600"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-600",children:"Total Earnings"}),e.jsx("div",{className:"text-xl font-bold",children:i(s.total_earnings)})]})]})}),e.jsx("div",{className:"bg-white p-4 rounded-lg border",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(Be,{className:"w-5 h-5 text-orange-600"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-600",children:"Max Depth"}),e.jsx("div",{className:"text-xl font-bold",children:s.max_depth})]})]})}),e.jsx("div",{className:"bg-white p-4 rounded-lg border",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx($s,{className:"w-5 h-5 text-indigo-600"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-600",children:"Network Value"}),e.jsx("div",{className:"text-xl font-bold",children:i(s.total_network_value)})]})]})})]}),e.jsxs("div",{className:"bg-white rounded-lg border p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Referral Tree"}),e.jsxs("div",{className:"flex space-x-2 text-xs",children:[e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx("div",{className:"w-3 h-3 bg-purple-200 rounded"}),e.jsx("span",{children:"Root User"})]}),e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx("div",{className:"w-3 h-3 bg-green-200 rounded"}),e.jsx("span",{children:"Premium"})]}),e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx("div",{className:"w-3 h-3 bg-blue-200 rounded"}),e.jsx("span",{children:"Standard"})]})]})]}),e.jsx("div",{className:"overflow-x-auto",children:e.jsx("div",{className:"min-w-full",children:m(t)})})]})]})},No=()=>{const[t,s]=f.useState(!0),[r,a]=f.useState(""),[l,n]=f.useState(null),[i,c]=f.useState(null),[o,d]=f.useState(null),[m,h]=f.useState(null),[p,x]=f.useState([]),[g,u]=f.useState("overview");f.useEffect(()=>{j()},[]);const j=async()=>{try{s(!0);const _=await er.getNetworkOverview();h(_)}catch(_){console.error("Error loading network overview:",_),k.error("Failed to load network overview")}finally{s(!1)}},b=async _=>{try{s(!0);const{tree:w,stats:S}=await er.getReferralTree(_);c(w),d(S),n(_),u("tree")}catch(w){console.error("Error loading user tree:",w),k.error("Failed to load referral tree")}finally{s(!1)}},N=async()=>{if(!r.trim()){x([]);return}try{s(!0);const _=await er.searchInNetwork(r);x(_),u("search")}catch(_){console.error("Error searching network:",_),k.error("Failed to search network")}finally{s(!1)}},P=_=>`₹${_.toFixed(2)}`,A=()=>e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4",children:[e.jsx("div",{className:"bg-white p-6 rounded-lg border",children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx($s,{className:"w-8 h-8 text-purple-600"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-600",children:"Total Networks"}),e.jsx("div",{className:"text-2xl font-bold",children:m?.total_networks||0})]})]})}),e.jsx("div",{className:"bg-white p-6 rounded-lg border",children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(H,{className:"w-8 h-8 text-blue-600"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-600",children:"Total Users"}),e.jsx("div",{className:"text-2xl font-bold",children:m?.total_users||0})]})]})}),e.jsx("div",{className:"bg-white p-6 rounded-lg border",children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(L,{className:"w-8 h-8 text-green-600"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-600",children:"Premium Users"}),e.jsx("div",{className:"text-2xl font-bold",children:m?.total_premium_users||0})]})]})}),e.jsx("div",{className:"bg-white p-6 rounded-lg border",children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(Be,{className:"w-8 h-8 text-orange-600"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-600",children:"Total Earnings"}),e.jsx("div",{className:"text-2xl font-bold",children:P(m?.total_earnings||0)})]})]})}),e.jsx("div",{className:"bg-white p-6 rounded-lg border",children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(H,{className:"w-8 h-8 text-indigo-600"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-600",children:"Avg Network Size"}),e.jsx("div",{className:"text-2xl font-bold",children:Math.round(m?.average_network_size||0)})]})]})}),e.jsx("div",{className:"bg-white p-6 rounded-lg border",children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx($s,{className:"w-8 h-8 text-red-600"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-600",children:"Largest Network"}),e.jsx("div",{className:"text-2xl font-bold",children:m?.largest_network||0})]})]})})]}),e.jsxs("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-blue-900 mb-2",children:"How to View Referral Trees"}),e.jsxs("div",{className:"text-blue-800 space-y-2",children:[e.jsx("p",{children:"• Use the search bar above to find specific users by email, name, or referral code"}),e.jsx("p",{children:"• Click on any user from search results to view their complete referral tree"}),e.jsx("p",{children:"• The tree shows all levels of referrals with earnings, status, and network size"}),e.jsx("p",{children:"• Premium users are highlighted in green, standard users in blue"})]})]})]}),E=()=>e.jsxs("div",{className:"space-y-4",children:[e.jsxs("h3",{className:"text-lg font-semibold",children:["Search Results (",p.length,")"]}),p.length===0?e.jsx("div",{className:"text-center py-8 text-gray-500",children:"No users found matching your search criteria."}):e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:p.map(_=>e.jsxs("div",{className:"bg-white p-4 rounded-lg border hover:shadow-md cursor-pointer transition-all",onClick:()=>b(_.id),children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[_.is_premium?e.jsx(L,{className:"w-5 h-5 text-green-600"}):e.jsx(H,{className:"w-5 h-5 text-blue-600"}),e.jsx("span",{className:"font-semibold",children:_.full_name})]}),e.jsx("button",{className:"p-1 hover:bg-gray-100 rounded",children:e.jsx(ne,{className:"w-4 h-4"})})]}),e.jsxs("div",{className:"text-sm text-gray-600 space-y-1",children:[e.jsx("div",{children:_.email}),e.jsx("div",{className:"font-mono text-xs bg-gray-100 px-2 py-1 rounded",children:_.referral_code})]}),e.jsxs("div",{className:"mt-3 grid grid-cols-2 gap-2 text-xs",children:[e.jsxs("div",{children:[e.jsx("span",{className:"text-gray-500",children:"Direct Referrals:"}),e.jsx("span",{className:"font-semibold ml-1",children:_.direct_referrals})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-gray-500",children:"Network Size:"}),e.jsx("span",{className:"font-semibold ml-1",children:_.total_network_size+1})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-gray-500",children:"Earnings:"}),e.jsx("span",{className:"font-semibold ml-1",children:P(_.total_earnings)})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-gray-500",children:"Wallet:"}),e.jsx("span",{className:"font-semibold ml-1",children:P(_.wallet_balance)})]})]}),_.ewallet_unlocked&&e.jsx("div",{className:"mt-2 text-xs text-green-600 font-semibold",children:"🔓 E-wallet Unlocked"})]},_.id))})]});return t&&!i&&!m?e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Referral Tree Visualization"}),e.jsx("p",{className:"text-gray-600",children:"View and analyze the complete referral network hierarchy"})]}),e.jsx("div",{className:"flex space-x-2",children:e.jsxs("button",{onClick:j,className:"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:[e.jsx(le,{className:"w-4 h-4"}),e.jsx("span",{children:"Refresh"})]})})]}),e.jsx("div",{className:"bg-white p-4 rounded-lg border",children:e.jsxs("div",{className:"flex space-x-4",children:[e.jsx("div",{className:"flex-1",children:e.jsxs("div",{className:"relative",children:[e.jsx(ue,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),e.jsx("input",{type:"text",placeholder:"Search by email, name, or referral code...",value:r,onChange:_=>a(_.target.value),onKeyPress:_=>_.key==="Enter"&&N(),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})}),e.jsx("button",{onClick:N,className:"px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700",children:"Search"}),e.jsx("button",{onClick:()=>u("overview"),className:"px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700",children:"Overview"})]})}),t&&e.jsx("div",{className:"flex items-center justify-center py-8",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}),!t&&g==="overview"&&A(),!t&&g==="search"&&E(),!t&&g==="tree"&&i&&o&&e.jsxs("div",{children:[e.jsx("div",{className:"mb-4",children:e.jsx("button",{onClick:()=>u("overview"),className:"text-blue-600 hover:text-blue-800",children:"← Back to Overview"})}),e.jsx(wo,{tree:i,stats:o,onUserSelect:b})]})]})};function vo(){const t=js(),{user:s,logout:r}=J(),[a,l]=f.useState(!1),[n,i]=f.useState({total_revenue:0,total_users:0,premium_users:0,total_orders:0,wallet_transactions_total:0,total_withdrawals:0,referral_bonuses_paid:0}),{notifications:c,unreadCount:o,markAsRead:d,markAllAsRead:m,clearNotification:h,clearAllNotifications:p,createNotification:x}=Et(),[g,u]=f.useState(!1);f.useEffect(()=>{j()},[]);const j=async()=>{try{const[{data:_},{data:w},{data:S},{data:C},{data:T},{data:F}]=await Promise.all([y.from("orders").select("total_amount"),y.from("users").select("id"),y.from("users").select("id").eq("is_premium",!0),y.from("wallet_transactions").select("amount").eq("type","credit"),y.from("wallet_transactions").select("amount").eq("type","debit"),y.from("wallet_transactions").select("amount").eq("reference_type","referral")]),I=_?.reduce((z,G)=>z+G.total_amount,0)||0,v=C?.reduce((z,G)=>z+G.amount,0)||0,$=T?.reduce((z,G)=>z+G.amount,0)||0,O=F?.reduce((z,G)=>z+G.amount,0)||0;i({total_revenue:I,total_users:w?.length||0,premium_users:S?.length||0,total_orders:_?.length||0,wallet_transactions_total:v,total_withdrawals:$,referral_bonuses_paid:O})}catch(_){console.error("Error fetching admin stats:",_)}},b=_=>{const w=new Date,S=new Date(_),C=Math.floor((w.getTime()-S.getTime())/(1e3*60));return C<1?"Just now":C<60?`${C}m ago`:C<1440?`${Math.floor(C/60)}h ago`:`${Math.floor(C/1440)}d ago`},N=_=>{switch(_){case"urgent":return{border:"border-red-500",bg:"bg-red-50"};case"high":return{border:"border-orange-500",bg:"bg-orange-50"};case"medium":return{border:"border-blue-500",bg:"bg-blue-50"};case"low":return{border:"border-gray-500",bg:"bg-gray-50"};default:return{border:"border-gray-300",bg:"bg-gray-50"}}},P=_=>{switch(_){case"order":return"bg-green-100";case"user":return"bg-blue-100";case"kyc":return"bg-purple-100";case"wallet":return"bg-yellow-100";case"product":return"bg-indigo-100";case"referral":return"bg-pink-100";case"alert":return"bg-red-100";default:return"bg-gray-100"}},A=_=>{const w="h-4 w-4";switch(_){case"order":return e.jsx(ge,{className:`${w} text-green-600`});case"user":return e.jsx(H,{className:`${w} text-blue-600`});case"kyc":return e.jsx(ae,{className:`${w} text-purple-600`});case"wallet":return e.jsx(K,{className:`${w} text-yellow-600`});case"product":return e.jsx(Z,{className:`${w} text-indigo-600`});case"referral":return e.jsx(ce,{className:`${w} text-pink-600`});case"alert":return e.jsx(we,{className:`${w} text-red-600`});default:return e.jsx(os,{className:`${w} text-gray-600`})}},E=[{name:"Dashboard",href:"/admin",icon:ar,current:t.pathname==="/admin"},{name:"Users",href:"/admin/users",icon:H,current:t.pathname==="/admin/users"},{name:"Referral Codes",href:"/admin/referral-codes",icon:ce,current:t.pathname==="/admin/referral-codes"},{name:"Referral Tree",href:"/admin/referral-tree",icon:$s,current:t.pathname==="/admin/referral-tree"},{name:"KYC Review",href:"/admin/kyc",icon:ae,current:t.pathname==="/admin/kyc"},{name:"Products",href:"/admin/products",icon:Z,current:t.pathname==="/admin/products"},{name:"Add Product",href:"/admin/products/add",icon:re,current:t.pathname==="/admin/products/add"},{name:"Inventory",href:"/admin/inventory",icon:ma,current:t.pathname==="/admin/inventory"},{name:"Orders",href:"/admin/orders",icon:ge,current:t.pathname==="/admin/orders"},{name:"Order Management",href:"/admin/order-management",icon:De,current:t.pathname==="/admin/order-management"},{name:"Wallet",href:"/admin/wallet",icon:K,current:t.pathname==="/admin/wallet"},{name:"Bonus Settings",href:"/admin/bonus-settings",icon:ze,current:t.pathname==="/admin/bonus-settings"},{name:"Notifications Test",href:"/admin/notifications-test",icon:os,current:t.pathname==="/admin/notifications-test"},{name:"Settings",href:"/admin/settings",icon:pr,current:t.pathname==="/admin/settings"}];return e.jsxs("div",{className:"min-h-screen bg-gray-50",children:[e.jsx("div",{className:"bg-white border-b border-gray-200 sticky top-0 z-30",children:e.jsx("div",{className:"px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"flex items-center justify-between h-16",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("button",{onClick:()=>l(!a),className:"lg:hidden p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors",children:a?e.jsx(oe,{className:"h-6 w-6"}):e.jsx(xr,{className:"h-6 w-6"})}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center",children:e.jsx(ar,{className:"h-5 w-5 text-white"})}),e.jsx("span",{className:"text-lg font-semibold text-gray-900 hidden sm:block",children:"Admin Panel"}),e.jsx("span",{className:"text-lg font-semibold text-gray-900 sm:hidden",children:"Admin"})]})]}),e.jsxs("div",{className:"flex items-center gap-2 sm:gap-4",children:[e.jsxs("div",{className:"relative hidden md:block",children:[e.jsx("input",{type:"text",placeholder:"Search...",className:"w-48 lg:w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"}),e.jsx(ue,{className:"absolute left-3 top-2.5 h-5 w-5 text-gray-400"})]}),e.jsxs("div",{className:"relative",children:[e.jsxs("button",{onClick:()=>u(!g),className:"relative p-2 text-gray-600 hover:text-green-600 transition-colors",children:[e.jsx(os,{className:"h-5 w-5 sm:h-6 sm:w-6"}),o>0&&e.jsx("span",{className:"absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-medium",children:o>9?"9+":o})]}),g&&e.jsxs("div",{className:"absolute right-0 mt-2 w-80 sm:w-96 bg-white rounded-lg shadow-lg border border-gray-200 z-50",children:[e.jsxs("div",{className:"px-4 py-3 border-b border-gray-100 flex items-center justify-between",children:[e.jsxs("h3",{className:"font-medium text-gray-900",children:["Notifications ",o>0&&e.jsxs("span",{className:"text-red-500",children:["(",o,")"]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[o>0&&e.jsx("button",{onClick:m,className:"text-xs text-blue-600 hover:text-blue-700 font-medium",children:"Mark all read"}),c.length>0&&e.jsx("button",{onClick:p,className:"text-xs text-red-600 hover:text-red-700 font-medium",children:"Clear all"})]})]}),e.jsx("div",{className:"max-h-96 overflow-y-auto",children:c.length===0?e.jsxs("div",{className:"px-4 py-8 text-center text-gray-500",children:[e.jsx(os,{className:"h-8 w-8 mx-auto mb-2 text-gray-300"}),e.jsx("p",{className:"text-sm",children:"No notifications"})]}):c.map(_=>{const w=b(_.created_at),S=N(_.priority);return e.jsx("div",{className:`px-4 py-3 hover:bg-gray-50 transition-colors border-l-4 ${_.read?"bg-gray-50":"bg-white"} ${S.border}`,children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:`p-2 rounded-full ${P(_.type)}`,children:A(_.type)}),e.jsx("div",{className:"flex-1 min-w-0",children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:`text-sm font-medium ${_.read?"text-gray-600":"text-gray-900"}`,children:_.title}),e.jsx("p",{className:`text-sm ${_.read?"text-gray-500":"text-gray-700"}`,children:_.message}),e.jsx("p",{className:"text-xs text-gray-400 mt-1",children:w})]}),e.jsxs("div",{className:"flex items-center gap-1 ml-2",children:[!_.read&&e.jsx("button",{onClick:()=>d(_.id),className:"p-1 text-blue-600 hover:text-blue-700",title:"Mark as read",children:e.jsx(V,{className:"h-4 w-4"})}),e.jsx("button",{onClick:()=>h(_.id),className:"p-1 text-red-600 hover:text-red-700",title:"Remove notification",children:e.jsx(gr,{className:"h-4 w-4"})})]})]})})]})},_.id)})}),c.length>0&&e.jsx("div",{className:"px-4 py-2 border-t border-gray-100 text-center",children:e.jsx("button",{onClick:()=>{x({type:"order",title:"Test Notification",message:"This is a test notification to demonstrate the system",priority:"medium"})},className:"text-xs text-green-600 hover:text-green-700 font-medium",children:"Add Test Notification"})})]})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center",children:e.jsx("span",{className:"text-sm font-medium text-green-600",children:s?.email?.charAt(0).toUpperCase()})}),e.jsxs("div",{className:"hidden md:block",children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:s?.email}),e.jsx("p",{className:"text-xs text-gray-500",children:"Administrator"})]})]})]})]})})}),e.jsxs("div",{className:"flex relative",children:[e.jsx("div",{className:`${a?"translate-x-0":"-translate-x-full"} lg:translate-x-0 fixed lg:static inset-y-0 left-0 z-40 w-64 bg-white shadow-sm lg:shadow-none min-h-[calc(100vh-4rem)] transition-transform duration-300 ease-in-out lg:transition-none`,children:e.jsxs("div",{className:"p-4 lg:p-6",children:[e.jsxs("div",{className:"hidden lg:grid grid-cols-2 gap-3 mb-8",children:[e.jsxs("div",{className:"bg-green-50 p-3 rounded-lg",children:[e.jsx("p",{className:"text-xs text-green-600 font-medium",children:"Revenue"}),e.jsxs("p",{className:"text-lg font-bold text-green-700",children:["₹",n.total_revenue]})]}),e.jsxs("div",{className:"bg-blue-50 p-3 rounded-lg",children:[e.jsx("p",{className:"text-xs text-blue-600 font-medium",children:"Users"}),e.jsx("p",{className:"text-lg font-bold text-blue-700",children:n.total_users})]})]}),e.jsxs("div",{className:"hidden lg:block mb-8",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Date Range"}),e.jsxs("div",{className:"relative",children:[e.jsxs("select",{className:"w-full pl-3 pr-10 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent appearance-none",children:[e.jsx("option",{children:"Last 7 days"}),e.jsx("option",{children:"Last 30 days"}),e.jsx("option",{children:"Last 90 days"}),e.jsx("option",{children:"This year"})]}),e.jsx(fr,{className:"absolute right-3 top-2.5 h-5 w-5 text-gray-400"})]})]}),e.jsxs("nav",{className:"space-y-1 lg:space-y-2",children:[E.map(_=>e.jsxs(R,{to:_.href,onClick:()=>l(!1),className:`flex items-center gap-3 px-3 py-3 lg:py-2 rounded-lg text-sm font-medium transition-colors touch-target ${_.current?"bg-green-100 text-green-700":"text-gray-600 hover:bg-gray-100 hover:text-gray-900"}`,children:[e.jsx(_.icon,{className:"h-5 w-5"}),_.name]},_.name)),e.jsxs("button",{onClick:()=>{r(),l(!1)},className:"w-full flex items-center gap-3 px-3 py-3 lg:py-2 rounded-lg text-sm font-medium text-red-600 hover:bg-red-50 transition-colors touch-target",children:[e.jsx(st,{className:"h-5 w-5"}),"Logout"]})]})]})}),a&&e.jsx("div",{className:"lg:hidden fixed inset-0 bg-black bg-opacity-50 z-30",onClick:()=>l(!1)}),e.jsx("div",{className:"flex-1 p-4 sm:p-6 lg:p-8",children:e.jsxs(mr,{children:[e.jsx(M,{path:"/",element:e.jsx(eo,{stats:n})}),e.jsx(M,{path:"/users",element:e.jsx(ao,{})}),e.jsx(M,{path:"/referral-codes",element:e.jsx(mo,{})}),e.jsx(M,{path:"/referral-tree",element:e.jsx(No,{})}),e.jsx(M,{path:"/kyc",element:e.jsx(co,{})}),e.jsx(M,{path:"/products",element:e.jsx(lo,{})}),e.jsx(M,{path:"/products/add",element:e.jsx(go,{})}),e.jsx(M,{path:"/inventory",element:e.jsx(bo,{})}),e.jsx(M,{path:"/orders",element:e.jsx(io,{})}),e.jsx(M,{path:"/order-management",element:e.jsx(po,{})}),e.jsx(M,{path:"/wallet",element:e.jsx(oo,{})}),e.jsx(M,{path:"/bonus-settings",element:e.jsx(fo,{})}),e.jsx(M,{path:"/notifications-test",element:e.jsx(yo,{})}),e.jsx(M,{path:"/settings",element:e.jsx(uo,{})})]})})]})]})}const _o=()=>e.jsx("div",{className:"min-h-screen bg-gray-100 py-16 px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"max-w-3xl mx-auto bg-white p-8 rounded-lg shadow-lg",children:[e.jsx("h1",{className:"text-4xl font-extrabold text-gray-900 mb-6 text-center",children:"About Start Juicce"}),e.jsx("p",{className:"text-lg text-gray-700 mb-4",children:"Welcome to Start Juicce, your trusted source for premium herbal products. We are dedicated to providing you with the finest natural ingredients to support your journey to holistic well-being."}),e.jsx("p",{className:"text-lg text-gray-700 mb-4",children:"Our mission is to empower individuals to live healthier, happier lives through the power of nature. We meticulously source our herbs from certified organic farms and ensure every product meets the highest standards of quality and purity."}),e.jsx("p",{className:"text-lg text-gray-700 mb-4",children:"At Start Juicce, we believe in transparency, sustainability, and the profound benefits of traditional herbal wisdom combined with modern science. Our team of experts is passionate about natural health and committed to guiding you towards the best solutions for your unique needs."}),e.jsx("p",{className:"text-lg text-gray-700 mb-6",children:"Thank you for choosing Start Juicce. We look forward to being a part of your wellness journey."}),e.jsxs("div",{className:"mt-8 text-center",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-800 mb-4",children:"Our Core Values"}),e.jsxs("ul",{className:"list-disc list-inside text-left inline-block text-gray-700",children:[e.jsx("li",{className:"mb-2",children:"Quality: Sourcing the finest natural ingredients."}),e.jsx("li",{className:"mb-2",children:"Purity: Ensuring products are free from harmful additives."}),e.jsx("li",{className:"mb-2",children:"Transparency: Providing clear information about our products."}),e.jsx("li",{className:"mb-2",children:"Sustainability: Practicing environmentally responsible sourcing."}),e.jsx("li",{children:"Customer Well-being: Prioritizing your health and satisfaction."})]})]})]})}),ko=()=>e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-yellow-50 to-yellow-100 py-16 px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"max-w-4xl mx-auto bg-white p-10 rounded-xl shadow-2xl",children:[e.jsxs("h1",{className:"text-5xl font-extrabold text-yellow-800 mb-6 text-center leading-tight",children:["Unlock ",e.jsx("span",{className:"text-yellow-600",children:"Premium"})," Benefits"]}),e.jsx("p",{className:"text-xl text-gray-700 mb-10 text-center",children:"Elevate your wellness journey with exclusive features, deeper insights, and special rewards."}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-10 mb-12",children:[e.jsxs("div",{className:"bg-yellow-50 p-6 rounded-lg shadow-md border border-yellow-200",children:[e.jsxs("div",{className:"flex items-center text-yellow-700 mb-4",children:[e.jsx(V,{className:"h-6 w-6 mr-3"}),e.jsx("h2",{className:"text-2xl font-semibold",children:"Exclusive Products"})]}),e.jsx("p",{className:"text-gray-600",children:"Gain access to limited-edition herbal formulations and unique products available only to premium members."})]}),e.jsxs("div",{className:"bg-yellow-50 p-6 rounded-lg shadow-md border border-yellow-200",children:[e.jsxs("div",{className:"flex items-center text-yellow-700 mb-4",children:[e.jsx(Je,{className:"h-6 w-6 mr-3"}),e.jsx("h2",{className:"text-2xl font-semibold",children:"Special Discounts"})]}),e.jsx("p",{className:"text-gray-600",children:"Enjoy up to 25% off on all purchases, plus access to exclusive member-only sales and promotions."})]}),e.jsxs("div",{className:"bg-yellow-50 p-6 rounded-lg shadow-md border border-yellow-200",children:[e.jsxs("div",{className:"flex items-center text-yellow-700 mb-4",children:[e.jsx(ce,{className:"h-6 w-6 mr-3"}),e.jsx("h2",{className:"text-2xl font-semibold",children:"Referral Rewards"})]}),e.jsx("p",{className:"text-gray-600",children:"Earn significant wallet credits for every friend you refer who joins our premium community."})]}),e.jsxs("div",{className:"bg-yellow-50 p-6 rounded-lg shadow-md border border-yellow-200",children:[e.jsxs("div",{className:"flex items-center text-yellow-700 mb-4",children:[e.jsx(ua,{className:"h-6 w-6 mr-3"}),e.jsx("h2",{className:"text-2xl font-semibold",children:"Priority Support"})]}),e.jsx("p",{className:"text-gray-600",children:"Get expedited customer service and personalized consultations with our herbal experts."})]})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Choose Your Plan"}),e.jsxs("div",{className:"flex flex-col md:flex-row justify-center items-center space-y-6 md:space-y-0 md:space-x-8",children:[e.jsxs("div",{className:"bg-white p-8 rounded-lg shadow-lg border-2 border-yellow-400 transform hover:scale-105 transition-transform duration-300 w-full md:w-auto",children:[e.jsx("h3",{className:"text-3xl font-bold text-yellow-600 mb-4",children:"Monthly"}),e.jsxs("p",{className:"text-4xl font-extrabold text-gray-900 mb-2",children:["₹299",e.jsx("span",{className:"text-xl font-medium text-gray-600",children:"/month"})]}),e.jsx("p",{className:"text-gray-500 mb-6",children:"Billed monthly"}),e.jsx("button",{className:"w-full bg-yellow-500 text-white font-bold py-3 rounded-full hover:bg-yellow-600 transition-colors shadow-md",children:"Subscribe Monthly"})]}),e.jsxs("div",{className:"bg-yellow-500 text-white p-8 rounded-lg shadow-xl transform hover:scale-105 transition-transform duration-300 w-full md:w-auto relative overflow-hidden",children:[e.jsx("span",{className:"absolute top-0 right-0 bg-yellow-700 text-xs font-bold px-3 py-1 rounded-bl-lg",children:"BEST VALUE"}),e.jsx("h3",{className:"text-3xl font-bold mb-4",children:"Yearly"}),e.jsxs("p",{className:"text-4xl font-extrabold mb-2",children:["₹2999",e.jsx("span",{className:"text-xl font-medium",children:"/year"})]}),e.jsx("p",{className:"text-yellow-100 mb-6",children:"Billed annually (Save 17%)"}),e.jsx("button",{className:"w-full bg-white text-yellow-700 font-bold py-3 rounded-full hover:bg-gray-100 transition-colors shadow-md",children:"Subscribe Yearly"})]})]})]}),e.jsx("div",{className:"mt-12 text-center",children:e.jsxs("p",{className:"text-gray-600",children:["Have questions about Premium? Visit our ",e.jsx(R,{to:"/faq",className:"text-yellow-600 hover:underline",children:"FAQ page"}),"."]})})]})}),So=()=>e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-green-50 to-green-100 py-8 px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"max-w-4xl mx-auto",children:[e.jsxs("div",{className:"text-center mb-12",children:[e.jsx("h1",{className:"text-3xl sm:text-4xl font-bold text-gray-900 mb-4",children:"Contact Start Juicce"}),e.jsx("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:"Have questions about our products or need help with your order? We're here to help you on your wellness journey."})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[e.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-6 sm:p-8",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Get in Touch"}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-start space-x-4",children:[e.jsx("div",{className:"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0",children:e.jsx(Ie,{className:"w-6 h-6 text-green-600"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold text-gray-900",children:"Email Us"}),e.jsx("p",{className:"text-gray-600",children:"<EMAIL>"}),e.jsx("p",{className:"text-sm text-gray-500",children:"We'll respond within 24 hours"})]})]}),e.jsxs("div",{className:"flex items-start space-x-4",children:[e.jsx("div",{className:"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0",children:e.jsx(Bs,{className:"w-6 h-6 text-green-600"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold text-gray-900",children:"Call Us"}),e.jsx("p",{className:"text-gray-600",children:"+91 98765 43210"}),e.jsx("p",{className:"text-sm text-gray-500",children:"Mon-Fri, 9 AM - 6 PM IST"})]})]}),e.jsxs("div",{className:"flex items-start space-x-4",children:[e.jsx("div",{className:"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0",children:e.jsx(rs,{className:"w-6 h-6 text-green-600"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold text-gray-900",children:"Visit Us"}),e.jsxs("p",{className:"text-gray-600",children:["123 Wellness Street",e.jsx("br",{}),"Green Valley, Mumbai",e.jsx("br",{}),"Maharashtra 400001, India"]})]})]}),e.jsxs("div",{className:"flex items-start space-x-4",children:[e.jsx("div",{className:"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0",children:e.jsx(Ne,{className:"w-6 h-6 text-green-600"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold text-gray-900",children:"Business Hours"}),e.jsxs("div",{className:"text-gray-600 text-sm",children:[e.jsx("p",{children:"Monday - Friday: 9:00 AM - 6:00 PM"}),e.jsx("p",{children:"Saturday: 10:00 AM - 4:00 PM"}),e.jsx("p",{children:"Sunday: Closed"})]})]})]})]})]}),e.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-6 sm:p-8",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Send us a Message"}),e.jsxs("form",{className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"firstName",className:"block text-sm font-medium text-gray-700 mb-2",children:"First Name *"}),e.jsx("input",{type:"text",id:"firstName",name:"firstName",required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent",placeholder:"Enter your first name"})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"lastName",className:"block text-sm font-medium text-gray-700 mb-2",children:"Last Name *"}),e.jsx("input",{type:"text",id:"lastName",name:"lastName",required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent",placeholder:"Enter your last name"})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address *"}),e.jsx("input",{type:"email",id:"email",name:"email",required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent",placeholder:"Enter your email address"})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"subject",className:"block text-sm font-medium text-gray-700 mb-2",children:"Subject *"}),e.jsxs("select",{id:"subject",name:"subject",required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent",children:[e.jsx("option",{value:"",children:"Select a subject"}),e.jsx("option",{value:"general",children:"General Inquiry"}),e.jsx("option",{value:"order",children:"Order Support"}),e.jsx("option",{value:"product",children:"Product Information"}),e.jsx("option",{value:"kyc",children:"KYC Verification"}),e.jsx("option",{value:"referral",children:"Referral Program"}),e.jsx("option",{value:"technical",children:"Technical Support"})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"message",className:"block text-sm font-medium text-gray-700 mb-2",children:"Message *"}),e.jsx("textarea",{id:"message",name:"message",rows:5,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent resize-none",placeholder:"Tell us how we can help you..."})]}),e.jsxs("button",{type:"submit",className:"w-full bg-green-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-green-700 transition-colors flex items-center justify-center space-x-2",children:[e.jsx(xa,{className:"w-5 h-5"}),e.jsx("span",{children:"Send Message"})]})]})]})]}),e.jsxs("div",{className:"mt-12 bg-white rounded-xl shadow-lg p-6 sm:p-8",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Frequently Asked Questions"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold text-gray-900 mb-2",children:"How do I get a referral code?"}),e.jsx("p",{className:"text-gray-600 text-sm",children:"Referral codes are provided by existing premium members. Contact us if you need assistance finding a referral code."})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold text-gray-900 mb-2",children:"How long does KYC verification take?"}),e.jsx("p",{className:"text-gray-600 text-sm",children:"KYC verification typically takes 1-3 business days. You'll receive an email notification once your verification is complete."})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold text-gray-900 mb-2",children:"What payment methods do you accept?"}),e.jsx("p",{className:"text-gray-600 text-sm",children:"We accept all major credit/debit cards, UPI, net banking, and wallet payments through secure payment gateways."})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold text-gray-900 mb-2",children:"Do you offer international shipping?"}),e.jsx("p",{className:"text-gray-600 text-sm",children:"Currently, we only ship within India. International shipping options are coming soon."})]})]})]})]})});function Co(){const{fetchProducts:t}=Ge();return f.useEffect(()=>{(async()=>{try{await Ll(),t(),gt.startMonitoring(),console.log("Automatic referral monitoring started")}catch(r){console.error("App initialization error:",r),t()}})()},[t]),e.jsxs(Mt,{future:{v7_startTransition:!0,v7_relativeSplatPath:!0},children:[e.jsx(ii,{}),e.jsx(Jl,{children:e.jsxs(mr,{children:[e.jsx(M,{path:"/",element:e.jsx(mi,{})}),e.jsx(M,{path:"/products",element:e.jsx(xi,{})}),e.jsx(M,{path:"/products/:id",element:e.jsx(hi,{})}),e.jsx(M,{path:"/cart",element:e.jsx(gi,{})}),e.jsx(M,{path:"/checkout",element:e.jsx(pi,{})}),e.jsx(M,{path:"/login",element:e.jsx(bi,{})}),e.jsx(M,{path:"/register",element:e.jsx(Oi,{})}),e.jsx(M,{path:"/forgot-password",element:e.jsx(Ui,{})}),e.jsx(M,{path:"/reset-password",element:e.jsx($i,{})}),e.jsx(M,{path:"/dashboard/*",element:e.jsx(Qi,{})}),e.jsx(M,{path:"/admin/*",element:e.jsx(oi,{children:e.jsx(vo,{})})}),e.jsx(M,{path:"/about",element:e.jsx(_o,{})}),e.jsx(M,{path:"/premium",element:e.jsx(ko,{})}),e.jsx(M,{path:"/contact",element:e.jsx(So,{})}),e.jsx(M,{path:"/wallet",element:e.jsx(Ke,{to:"/dashboard/wallet",replace:!0})}),e.jsx(M,{path:"/referrals",element:e.jsx(Ke,{to:"/dashboard/referrals",replace:!0})}),e.jsx(M,{path:"/profile",element:e.jsx(Ke,{to:"/dashboard/profile",replace:!0})}),e.jsx(M,{path:"/orders",element:e.jsx(Ke,{to:"/dashboard/orders",replace:!0})}),e.jsx(M,{path:"/kyc",element:e.jsx(Ke,{to:"/dashboard/kyc",replace:!0})})]})}),e.jsx(Ql,{}),e.jsx(dl,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff"},success:{style:{background:"#10B981"}},error:{style:{background:"#EF4444"}}}})]})}lt(document.getElementById("root")).render(e.jsx(f.StrictMode,{children:e.jsx(Co,{})}));
