{"version": 3, "file": "ScopeManager.js", "sourceRoot": "", "sources": ["../src/ScopeManager.ts"], "names": [], "mappings": ";;;AAEA,qCAAkC;AAElC,mCAkBiB;AACjB,mFAAgF;AAChF,yEAAsE;AAStE;;GAEG;AACH,MAAM,YAAY;IACT,YAAY,CAAe;IAClB,iBAAiB,CAAqC;IACtE;;OAEG;IACI,WAAW,CAAqB;IACvB,WAAW,CAAkC;IACpD,QAAQ,CAAsB;IACvC;;;OAGG;IACa,MAAM,CAAU;IAEhC,IAAW,SAAS;QAClB,MAAM,SAAS,GAAG,IAAI,GAAG,EAAY,CAAC;QACtC,SAAS,OAAO,CAAC,KAAY;YAC3B,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/C,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QACrC,CAAC;QACD,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAC7B,OAAO,CAAC,GAAG,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;IACtD,CAAC;IAED,YAAY,OAA4B;QACtC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,IAAI,CAAC,WAAW,GAAG,IAAI,OAAO,EAAE,CAAC;QACjC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,iBAAiB,GAAG,IAAI,OAAO,EAAE,CAAC;IACzC,CAAC;IAEM,cAAc;QACnB,OAAO,IAAI,CAAC,QAAQ,CAAC,YAAY,KAAK,IAAI,CAAC;IAC7C,CAAC;IAEM,QAAQ;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,UAAU,KAAK,QAAQ,CAAC;IAC/C,CAAC;IAEM,eAAe;QACpB,OAAO,IAAI,CAAC,QAAQ,CAAC,aAAa,KAAK,IAAI,CAAC;IAC9C,CAAC;IAEM,qBAAqB;QAC1B,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,KAAK;QACV,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACI,oBAAoB,CAAC,IAAmB;QAC7C,OAAO,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;IAChD,CAAC;IAED;;;;;;;OAOG;IACI,OAAO,CAAC,IAAmB,EAAE,KAAK,GAAG,KAAK;QAC/C,SAAS,SAAS,CAAC,SAAgB;YACjC,IACE,SAAS,CAAC,IAAI,KAAK,iBAAS,CAAC,QAAQ;gBACrC,SAAS,CAAC,uBAAuB,EACjC,CAAC;gBACD,OAAO,KAAK,CAAC;YACf,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAE1C,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACnC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,uCAAuC;QACvC,2EAA2E;QAC3E,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxB,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC;QAED,IAAI,KAAK,EAAE,CAAC;YACV,KAAK,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC;gBAC5C,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;gBAExB,IAAI,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;oBACrB,OAAO,KAAK,CAAC;gBACf,CAAC;YACH,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC;IACxC,CAAC;IAGS,SAAS,CAAC,KAAY;QAC9B,IAAI,KAAK,YAAY,mBAAW,EAAE,CAAC;YACjC,IAAA,eAAM,EAAC,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,CAAC;YAClC,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QAC3B,CAAC;QACD,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,OAAO,KAAK,CAAC;IACf,CAAC;IAEM,cAAc,CAAC,IAAyB;QAC7C,IAAA,eAAM,EAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC1B,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,kBAAU,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC,CAAC;IACvE,CAAC;IAEM,cAAc,CAAC,IAAyB;QAC7C,IAAA,eAAM,EAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC1B,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,kBAAU,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC,CAAC;IACvE,CAAC;IAEM,cAAc,CAAC,IAAyB;QAC7C,IAAA,eAAM,EAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC1B,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,kBAAU,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC,CAAC;IACvE,CAAC;IAEM,8BAA8B,CACnC,IAAyC;QAEzC,IAAA,eAAM,EAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC1B,OAAO,IAAI,CAAC,SAAS,CACnB,IAAI,uDAA0B,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,CAC9D,CAAC;IACJ,CAAC;IAEM,yBAAyB,CAC9B,IAAoC;QAEpC,IAAA,eAAM,EAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC1B,OAAO,IAAI,CAAC,SAAS,CACnB,IAAI,6CAAqB,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,CACzD,CAAC;IACJ,CAAC;IAEM,wBAAwB,CAC7B,IAAmC;QAEnC,IAAA,eAAM,EAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC1B,OAAO,IAAI,CAAC,SAAS,CACnB,IAAI,4BAAoB,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,CACxD,CAAC;IACJ,CAAC;IAEM,YAAY,CAAC,IAAuB;QACzC,IAAA,eAAM,EAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC1B,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,gBAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC,CAAC;IACrE,CAAC;IAEM,+BAA+B,CACpC,IAA0C;QAE1C,IAAA,eAAM,EAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC1B,OAAO,IAAI,CAAC,SAAS,CACnB,IAAI,mCAA2B,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,CAC/D,CAAC;IACJ,CAAC;IAEM,iBAAiB,CACtB,IAA4B,EAC5B,kBAA2B;QAE3B,IAAA,eAAM,EAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC1B,OAAO,IAAI,CAAC,SAAS,CACnB,IAAI,qBAAa,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,EAAE,kBAAkB,CAAC,CACrE,CAAC;IACJ,CAAC;IAEM,qBAAqB,CAC1B,IAAgC;QAEhC,IAAA,eAAM,EAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC1B,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,yBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC,CAAC;IAC9E,CAAC;IAEM,eAAe,CAAC,IAA0B;QAC/C,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,mBAAW,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;IACrD,CAAC;IAEM,mBAAmB,CAAC,IAA8B;QACvD,IAAA,eAAM,EAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC1B,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,uBAAe,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC,CAAC;IAC5E,CAAC;IAEM,eAAe,CAAC,IAA0B;QAC/C,IAAA,eAAM,EAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC1B,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,mBAAW,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC,CAAC;IACxE,CAAC;IAEM,eAAe,CAAC,IAA0B;QAC/C,IAAA,eAAM,EAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC1B,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,mBAAW,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC,CAAC;IACxE,CAAC;IAEM,eAAe,CAAC,IAA0B;QAC/C,IAAA,eAAM,EAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC1B,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,mBAAW,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC,CAAC;IACxE,CAAC;IAEM,iBAAiB,CAAC,IAA4B;QACnD,IAAA,eAAM,EAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC1B,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,qBAAa,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC,CAAC;IAC1E,CAAC;IAEM,aAAa,CAAC,IAAwB;QAC3C,IAAA,eAAM,EAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC1B,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,iBAAS,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC,CAAC;IACtE,CAAC;IAEM,aAAa,CAAC,IAAwB;QAC3C,IAAA,eAAM,EAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC1B,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,iBAAS,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC,CAAC;IACtE,CAAC;CACF;AAEQ,oCAAY"}