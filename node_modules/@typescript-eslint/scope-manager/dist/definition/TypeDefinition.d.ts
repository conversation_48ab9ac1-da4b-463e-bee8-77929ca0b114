import type { TSESTree } from '@typescript-eslint/types';
import { DefinitionBase } from './DefinitionBase';
import { DefinitionType } from './DefinitionType';
declare class TypeDefinition extends DefinitionBase<DefinitionType.Type, TSESTree.TSInterfaceDeclaration | TSESTree.TSMappedType | TSESTree.TSTypeAliasDeclaration | TSESTree.TSTypeParameter, null, TSESTree.Identifier> {
    constructor(name: TSESTree.Identifier, node: TypeDefinition['node']);
    readonly isTypeDefinition = true;
    readonly isVariableDefinition = false;
}
export { TypeDefinition };
//# sourceMappingURL=TypeDefinition.d.ts.map