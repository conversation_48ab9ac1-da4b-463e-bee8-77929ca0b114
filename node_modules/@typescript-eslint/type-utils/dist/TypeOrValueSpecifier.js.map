{"version": 3, "file": "TypeOrValueSpecifier.js", "sourceRoot": "", "sources": ["../src/TypeOrValueSpecifier.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAmKA,oDAgCC;AAlMD,sDAAwC;AAGxC,uFAAoF;AACpF,mFAAgF;AAChF,iFAA8E;AAC9E,uHAAoH;AA6DvG,QAAA,2BAA2B,GAAG;IACzC,IAAI,EAAE,OAAO;IACb,KAAK,EAAE;QACL,KAAK,EAAE;YACL;gBACE,IAAI,EAAE,QAAQ;aACf;YACD;gBACE,IAAI,EAAE,QAAQ;gBACd,oBAAoB,EAAE,KAAK;gBAC3B,UAAU,EAAE;oBACV,IAAI,EAAE;wBACJ,IAAI,EAAE,QAAQ;wBACd,IAAI,EAAE,CAAC,MAAM,CAAC;qBACf;oBACD,IAAI,EAAE;wBACJ,KAAK,EAAE;4BACL;gCACE,IAAI,EAAE,QAAQ;6BACf;4BACD;gCACE,IAAI,EAAE,OAAO;gCACb,QAAQ,EAAE,CAAC;gCACX,WAAW,EAAE,IAAI;gCACjB,KAAK,EAAE;oCACL,IAAI,EAAE,QAAQ;iCACf;6BACF;yBACF;qBACF;oBACD,IAAI,EAAE;wBACJ,IAAI,EAAE,QAAQ;qBACf;iBACF;gBACD,QAAQ,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;aAC3B;YACD;gBACE,IAAI,EAAE,QAAQ;gBACd,oBAAoB,EAAE,KAAK;gBAC3B,UAAU,EAAE;oBACV,IAAI,EAAE;wBACJ,IAAI,EAAE,QAAQ;wBACd,IAAI,EAAE,CAAC,KAAK,CAAC;qBACd;oBACD,IAAI,EAAE;wBACJ,KAAK,EAAE;4BACL;gCACE,IAAI,EAAE,QAAQ;6BACf;4BACD;gCACE,IAAI,EAAE,OAAO;gCACb,QAAQ,EAAE,CAAC;gCACX,WAAW,EAAE,IAAI;gCACjB,KAAK,EAAE;oCACL,IAAI,EAAE,QAAQ;iCACf;6BACF;yBACF;qBACF;iBACF;gBACD,QAAQ,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;aAC3B;YACD;gBACE,IAAI,EAAE,QAAQ;gBACd,oBAAoB,EAAE,KAAK;gBAC3B,UAAU,EAAE;oBACV,IAAI,EAAE;wBACJ,IAAI,EAAE,QAAQ;wBACd,IAAI,EAAE,CAAC,SAAS,CAAC;qBAClB;oBACD,IAAI,EAAE;wBACJ,KAAK,EAAE;4BACL;gCACE,IAAI,EAAE,QAAQ;6BACf;4BACD;gCACE,IAAI,EAAE,OAAO;gCACb,QAAQ,EAAE,CAAC;gCACX,WAAW,EAAE,IAAI;gCACjB,KAAK,EAAE;oCACL,IAAI,EAAE,QAAQ;iCACf;6BACF;yBACF;qBACF;oBACD,OAAO,EAAE;wBACP,IAAI,EAAE,QAAQ;qBACf;iBACF;gBACD,QAAQ,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,SAAS,CAAC;aACtC;SACF;KACF;CAC6B,CAAC;AAEjC,SAAgB,oBAAoB,CAClC,IAAa,EACb,SAA+B,EAC/B,OAAmB;IAEnB,IAAI,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE,CAAC;QACvC,OAAO,KAAK,CAAC;IACf,CAAC;IACD,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE,CAAC;QAClC,OAAO,IAAA,2CAAoB,EAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IAC/C,CAAC;IACD,IAAI,CAAC,IAAA,2CAAoB,EAAC,IAAI,EAAE,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;QAChD,OAAO,KAAK,CAAC;IACf,CAAC;IACD,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,IAAI,IAAI,CAAC,WAAW,CAAC;IACpD,MAAM,YAAY,GAAG,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,CAAC;IACrD,MAAM,gBAAgB,GAAG,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,CACtD,WAAW,CAAC,aAAa,EAAE,CAC5B,CAAC;IACF,QAAQ,SAAS,CAAC,IAAI,EAAE,CAAC;QACvB,KAAK,MAAM;YACT,OAAO,IAAA,uCAAkB,EAAC,SAAS,CAAC,IAAI,EAAE,gBAAgB,EAAE,OAAO,CAAC,CAAC;QACvE,KAAK,KAAK;YACR,OAAO,IAAA,qCAAiB,EAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;QACtD,KAAK,SAAS;YACZ,OAAO,IAAA,2EAAoC,EACzC,SAAS,CAAC,OAAO,EACjB,YAAY,EACZ,gBAAgB,EAChB,OAAO,CACR,CAAC;IACN,CAAC;AACH,CAAC;AAEM,MAAM,wBAAwB,GAAG,CACtC,IAAa,EACb,aAAqC,EAAE,EACvC,OAAmB,EACV,EAAE,CACX,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,oBAAoB,CAAC,IAAI,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC,CAAC;AALlE,QAAA,wBAAwB,4BAK0C"}