{"version": 3, "file": "RuleCreator.js", "sourceRoot": "", "sources": ["../../src/eslint-utils/RuleCreator.ts"], "names": [], "mappings": ";;AAsDA,kCA0BC;AAxED,iDAA8C;AAwC9C;;;;;GAKG;AACH,SAAgB,WAAW,CACzB,UAAwC;IAExC,oHAAoH;IACpH,2FAA2F;IAC3F,OAAO,SAAS,eAAe,CAG7B,EACA,IAAI,EACJ,IAAI,EACJ,GAAG,IAAI,EAGR;QACC,OAAO,UAAU,CAAkC;YACjD,IAAI,EAAE;gBACJ,GAAG,IAAI;gBACP,IAAI,EAAE;oBACJ,GAAG,IAAI,CAAC,IAAI;oBACZ,GAAG,EAAE,UAAU,CAAC,IAAI,CAAC;iBACtB;aACF;YACD,GAAG,IAAI;SACR,CAAC,CAAC;IACL,CAAC,CAAC;AACJ,CAAC;AAED,SAAS,UAAU,CAIjB,EACA,MAAM,EACN,cAAc,EACd,IAAI,GACoD;IAKxD,OAAO;QACL,MAAM,CAAC,OAAmD;YACxD,MAAM,kBAAkB,GAAG,IAAA,2BAAY,EAAC,cAAc,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;YACzE,OAAO,MAAM,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAAC;QAC7C,CAAC;QACD,cAAc;QACd,IAAI;KACL,CAAC;AACJ,CAAC;AAED;;;;;GAKG;AACH,WAAW,CAAC,WAAW,GAAG,SAAS,WAAW,CAI5C,IAAiD;IAEjD,OAAO,UAAU,CAAC,IAAI,CAAC,CAAC;AAC1B,CAAC,CAAC"}