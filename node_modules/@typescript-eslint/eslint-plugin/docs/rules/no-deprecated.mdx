---
description: 'Disallow using code marked as `@deprecated`.'
---

import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';

> 🛑 This file is source code, not the primary documentation location! 🛑
>
> See **https://typescript-eslint.io/rules/no-deprecated** for documentation.

The [JSDoc `@deprecated` tag](https://jsdoc.app/tags-deprecated) can be used to document some piece of code being deprecated.
It's best to avoid using code marked as deprecated.
This rule reports on any references to code marked as `@deprecated`.

:::note
[TypeScript recognizes the `@deprecated` tag](https://www.typescriptlang.org/docs/handbook/jsdoc-supported-types.html#deprecated) and visualizes deprecated code with a ~strikethrough~.
However, TypeScript doesn't report type errors for deprecated code on its own.
:::

## Examples

<Tabs>
<TabItem value="❌ Incorrect">

```ts
/** @deprecated Use apiV2 instead. */
declare function apiV1(): Promise<string>;

declare function apiV2(): Promise<string>;

await apiV1();
```

```ts
import { parse } from 'node:url';

// 'parse' is deprecated. Use the WHATWG URL API instead.
const url = parse('/foo');
```

</TabItem>
<TabItem value="✅ Correct">

```ts
/** @deprecated Use apiV2 instead. */
declare function apiV1(): Promise<string>;

declare function apiV2(): Promise<string>;

await apiV2();
```

```ts
// Modern Node.js API, uses `new URL()`
const url2 = new URL('/foo', 'http://www.example.com');
```

</TabItem>
</Tabs>

## When Not To Use It

If portions of your project heavily use deprecated APIs and have no plan for moving to non-deprecated ones, you might want to disable this rule in those portions.

## Related To

- [`import/no-deprecated`](https://github.com/import-js/eslint-plugin-import/blob/main/docs/rules/no-deprecated.md) and [`import-x/no-deprecated`](https://github.com/un-ts/eslint-plugin-import-x/blob/master/docs/rules/no-deprecated.md): Does not use type information, but does also support [TomDoc](http://tomdoc.org)
- [`eslint-plugin-deprecation`](https://github.com/gund/eslint-plugin-deprecation) ([`deprecation/deprecation`](https://github.com/gund/eslint-plugin-deprecation?tab=readme-ov-file#rules)): Predecessor to this rule in a separate plugin
