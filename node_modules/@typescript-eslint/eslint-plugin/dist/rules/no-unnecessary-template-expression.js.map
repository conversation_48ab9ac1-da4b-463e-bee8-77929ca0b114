{"version": 3, "file": "no-unnecessary-template-expression.js", "sourceRoot": "", "sources": ["../../src/rules/no-unnecessary-template-expression.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAC1D,+CAAiC;AAEjC,kCAOiB;AACjB,mDAAgD;AAIhD,MAAM,0BAA0B,GAAG,6BAA6B,CAAC;AAEjE,iBAAiB;AACjB,kBAAkB;AAClB,qBAAqB;AACrB,SAAS,2BAA2B,CAAC,GAAW;IAC9C,OAAO,IAAI,MAAM,CAAC,GAAG,MAAM,CAAC,0BAA0B,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CACxE,GAAG,CACJ,CAAC;AACJ,CAAC;AAED,kBAAe,IAAA,iBAAU,EAAgB;IACvC,IAAI,EAAE,oCAAoC;IAC1C,IAAI,EAAE;QACJ,OAAO,EAAE,MAAM;QACf,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EAAE,2CAA2C;YACxD,WAAW,EAAE,QAAQ;YACrB,oBAAoB,EAAE,IAAI;SAC3B;QACD,QAAQ,EAAE;YACR,+BAA+B,EAC7B,mEAAmE;SACtE;QACD,MAAM,EAAE,EAAE;KACX;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ,MAAM,QAAQ,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAE5C,SAAS,sBAAsB,CAC7B,UAA+B;YAE/B,MAAM,IAAI,GAAG,IAAA,mCAA4B,EAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;YAEhE,MAAM,QAAQ,GAAG,CAAC,CAAU,EAAW,EAAE;gBACvC,OAAO,IAAA,oBAAa,EAAC,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;YACnD,CAAC,CAAC;YAEF,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC;gBACnB,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YACpC,CAAC;YAED,IAAI,IAAI,CAAC,cAAc,EAAE,EAAE,CAAC;gBAC1B,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACnC,CAAC;YAED,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC;QACxB,CAAC;QAED,SAAS,SAAS,CAChB,UAA+B;YAE/B,OAAO,UAAU,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO,CAAC;QACpD,CAAC;QAED,SAAS,iBAAiB,CACxB,UAA+B;YAE/B,OAAO,UAAU,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe,CAAC;QAC5D,CAAC;QAED,SAAS,oBAAoB,CAAC,UAA+B;YAC3D,OAAO,CACL,UAAU,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;gBAC7C,UAAU,CAAC,IAAI,KAAK,UAAU,CAC/B,CAAC;QACJ,CAAC;QAED,SAAS,eAAe,CAAC,UAA+B;YACtD,OAAO,CACL,UAAU,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;gBAC7C,UAAU,CAAC,IAAI,KAAK,KAAK,CAC1B,CAAC;QACJ,CAAC;QAED,OAAO;YACL,eAAe,CAAC,IAA8B;gBAC5C,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,wBAAwB,EAAE,CAAC;oBACjE,OAAO;gBACT,CAAC;gBAED,MAAM,uBAAuB,GAC3B,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC;oBACxB,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,KAAK,EAAE;oBAC/B,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,KAAK,EAAE;oBAC/B,IAAI,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC;oBAC7B,sBAAsB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;gBAE9C,IAAI,uBAAuB,EAAE,CAAC;oBAC5B,OAAO,CAAC,MAAM,CAAC;wBACb,GAAG,EAAE,IAAA,uBAAU,EAAC,OAAO,CAAC,UAAU,EAAE;4BAClC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;4BAChC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;yBACjC,CAAC;wBACF,SAAS,EAAE,iCAAiC;wBAC5C,GAAG,CAAC,KAAK;4BACP,MAAM,YAAY,GAAG,IAAA,uBAAgB,EAAC;gCACpC,UAAU,EAAE,OAAO,CAAC,UAAU;gCAC9B,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;gCAC/B,eAAe,EAAE,IAAI;6BACtB,CAAC,CAAC;4BAEH,OAAO,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;wBAC/C,CAAC;qBACF,CAAC,CAAC;oBAEH,OAAO;gBACT,CAAC;gBAED,MAAM,kBAAkB,GAAG,IAAI,CAAC,WAAW;qBACxC,MAAM,CACL,UAAU,CAAC,EAAE,CACX,SAAS,CAAC,UAAU,CAAC;oBACrB,iBAAiB,CAAC,UAAU,CAAC;oBAC7B,IAAA,4BAAqB,EAAC,UAAU,CAAC;oBACjC,oBAAoB,CAAC,UAAU,CAAC;oBAChC,eAAe,CAAC,UAAU,CAAC,CAC9B;qBACA,OAAO,EAAE,CAAC;gBAEb,IAAI,gCAAgC,GAAG,KAAK,CAAC;gBAE7C,KAAK,MAAM,UAAU,IAAI,kBAAkB,EAAE,CAAC;oBAC5C,MAAM,MAAM,GACV,EAAE,CAAC;oBACL,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;oBACnD,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;oBACrC,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;oBAEzC,IAAI,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;wBACrC,gCAAgC;4BAC9B,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;oBACxC,CAAC;oBAED,IAAI,SAAS,CAAC,UAAU,CAAC,EAAE,CAAC;wBAC1B,IAAI,YAAY,GAAG,CACjB,OAAO,UAAU,CAAC,KAAK,KAAK,QAAQ;4BAClC,CAAC,CAAC,2DAA2D;gCAC3D,yBAAyB;gCACzB,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;4BAC7B,CAAC,CAAC,qEAAqE;gCACrE,oDAAoD;gCACpD,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,CACtD;4BACC,4CAA4C;4BAC5C,wEAAwE;4BACxE,EAAE;4BACF,kEAAkE;4BAClE,kEAAkE;4BAClE,oEAAoE;4BACpE,oEAAoE;4BACpE,EAAE;4BACF,kDAAkD;4BAClD,WAAW;4BACX,cAAc;4BACd,aAAa;4BACb,gBAAgB;6BACf,UAAU,CACT,IAAI,MAAM,CACR,GAAG,MAAM,CAAC,0BAA0B,CAAC,MAAM,CAAC,YAAY,EACxD,GAAG,CACJ,EACD,MAAM,CACP,CAAC;wBAEJ,qBAAqB;wBACrB,iBAAiB;wBACjB,IACE,gCAAgC;4BAChC,2BAA2B,CAAC,YAAY,CAAC,EACzC,CAAC;4BACD,YAAY,GAAG,YAAY,CAAC,UAAU,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;wBACxD,CAAC;wBAED,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;4BAC9B,gCAAgC,GAAG,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;wBAClE,CAAC;wBAED,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;oBACtE,CAAC;yBAAM,IAAI,iBAAiB,CAAC,UAAU,CAAC,EAAE,CAAC;wBACzC,0DAA0D;wBAC1D,0DAA0D;wBAC1D,yBAAyB;wBACzB,EAAE;wBACF,gCAAgC;wBAChC,0DAA0D;wBAC1D,0DAA0D;wBAC1D,uDAAuD;wBACvD,uDAAuD;wBACvD,IACE,gCAAgC;4BAChC,2BAA2B,CACzB,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAC1D,EACD,CAAC;4BACD,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;gCACnB,KAAK,CAAC,gBAAgB,CACpB,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAClD,IAAI,CACL;6BACF,CAAC,CAAC;wBACL,CAAC;wBACD,IACE,UAAU,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC;4BAC9B,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,KAAK,CAAC,EAC3C,CAAC;4BACD,gCAAgC;gCAC9B,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;wBACnD,CAAC;wBAED,yDAAyD;wBACzD,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;4BACnB,KAAK,CAAC,WAAW,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;4BACjE,KAAK,CAAC,WAAW,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;yBAClE,CAAC,CAAC;oBACL,CAAC;yBAAM,CAAC;wBACN,gCAAgC,GAAG,KAAK,CAAC;oBAC3C,CAAC;oBAED,uBAAuB;oBACvB,aAAa;oBACb,IACE,gCAAgC;wBAChC,2BAA2B,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,EAChD,CAAC;wBACD,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;4BACnB,KAAK,CAAC,gBAAgB,CACpB,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAChD,KAAK,CACN;yBACF,CAAC,CAAC;oBACL,CAAC;oBAED,MAAM,YAAY,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;oBAC5C,MAAM,UAAU,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;oBAE1C,OAAO,CAAC,MAAM,CAAC;wBACb,GAAG,EAAE,IAAA,uBAAU,EAAC,OAAO,CAAC,UAAU,EAAE,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;wBAC/D,SAAS,EAAE,iCAAiC;wBAC5C,GAAG,CAAC,KAAK;4BACP,OAAO;gCACL,uEAAuE;gCACvE,KAAK,CAAC,WAAW,CAAC,CAAC,YAAY,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;gCACtD,KAAK,CAAC,WAAW,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;gCAEpD,GAAG,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;6BACnC,CAAC;wBACJ,CAAC;qBACF,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}