{"version": 3, "file": "analyzeChain.js", "sourceRoot": "", "sources": ["../../../src/rules/prefer-optional-chain-utils/analyzeChain.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AA+fA,oCAoHC;AA/mBD,oDAA0D;AAO1D,+CAA8C;AAC9C,+CAAiC;AAEjC,qCASoB;AACpB,mEAAgE;AAChE,iDAAoE;AAQpE,SAAS,YAAY,CACnB,cAAiD,EACjD,IAAmB,EACnB,UAAwB;IAExB,MAAM,QAAQ,GAAG,UAAU,GAAG,EAAE,CAAC,SAAS,CAAC,GAAG,GAAG,EAAE,CAAC,SAAS,CAAC,OAAO,CAAC;IACtE,MAAM,KAAK,GAAG,IAAA,6BAAc,EAAC,cAAc,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;IACrE,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;QACzB,IAAI,IAAA,oBAAa,EAAC,IAAI,EAAE,QAAQ,CAAC,EAAE,CAAC;YAClC,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAYD,MAAM,sBAAsB,GAAoB,CAC9C,cAAc,EACd,OAAO,EACP,KAAK,EACL,KAAK,EACL,EAAE;IACF,QAAQ,OAAO,CAAC,cAAc,EAAE,CAAC;QAC/B,kDAAkC,CAAC,CAAC,CAAC;YACnC,MAAM,WAAW,GAAG,KAAK,CAAC,EAAE,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;YACxC,IACE,WAAW,EAAE,cAAc;mFACe;gBAC1C,OAAO,CAAC,YAAY,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,EACvD,CAAC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC;YACD,OAAO,CAAC,OAAO,CAAC,CAAC;QACnB,CAAC;QAED;YACE,OAAO,CAAC,OAAO,CAAC,CAAC;QAEnB,wEAA6C,CAAC,CAAC,CAAC;YAC9C,yCAAyC;YACzC,MAAM,WAAW,GAAG,KAAK,CAAC,EAAE,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;YACxC,IACE,WAAW,EAAE,cAAc;6FACoB;gBAC/C,IAAA,2BAAY,EAAC,OAAO,CAAC,YAAY,EAAE,WAAW,CAAC,YAAY,CAAC;4DAChC,EAC5B,CAAC;gBACD,OAAO,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;YAChC,CAAC;YACD,IACE,YAAY,CACV,cAAc,EACd,OAAO,CAAC,YAAY,EACpB,EAAE,CAAC,SAAS,CAAC,SAAS,CACvB,EACD,CAAC;gBACD,qEAAqE;gBACrE,iEAAiE;gBACjE,qEAAqE;gBACrE,OAAO,IAAI,CAAC;YACd,CAAC;YAED,OAAO,CAAC,OAAO,CAAC,CAAC;QACnB,CAAC;QAED,kFAAkD,CAAC,CAAC,CAAC;YACnD,yCAAyC;YACzC,MAAM,WAAW,GAAG,KAAK,CAAC,EAAE,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;YACxC,IACE,WAAW,EAAE,cAAc;mFACe;gBAC1C,IAAA,2BAAY,EAAC,OAAO,CAAC,YAAY,EAAE,WAAW,CAAC,YAAY,CAAC;4DAChC,EAC5B,CAAC;gBACD,OAAO,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;YAChC,CAAC;YACD,IACE,YAAY,CAAC,cAAc,EAAE,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,EACrE,CAAC;gBACD,+DAA+D;gBAC/D,4DAA4D;gBAC5D,qEAAqE;gBACrE,OAAO,IAAI,CAAC;YACd,CAAC;YAED,OAAO,CAAC,OAAO,CAAC,CAAC;QACnB,CAAC;QAED;YACE,OAAO,IAAI,CAAC;IAChB,CAAC;AACH,CAAC,CAAC;AACF,MAAM,qBAAqB,GAAoB,CAC7C,cAAc,EACd,OAAO,EACP,KAAK,EACL,KAAK,EACL,EAAE;IACF,QAAQ,OAAO,CAAC,cAAc,EAAE,CAAC;QAC/B,yDAAsC;QACtC;YACE,OAAO,CAAC,OAAO,CAAC,CAAC;QAEnB,kEAA0C,CAAC,CAAC,CAAC;YAC3C,yCAAyC;YACzC,MAAM,WAAW,GAAG,KAAK,CAAC,EAAE,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;YACxC,IACE,WAAW,EAAE,cAAc;uFACiB;gBAC5C,IAAA,2BAAY,EAAC,OAAO,CAAC,YAAY,EAAE,WAAW,CAAC,YAAY,CAAC;4DAChC,EAC5B,CAAC;gBACD,OAAO,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;YAChC,CAAC;YACD,IACE,YAAY,CACV,cAAc,EACd,OAAO,CAAC,YAAY,EACpB,EAAE,CAAC,SAAS,CAAC,SAAS,CACvB,EACD,CAAC;gBACD,qEAAqE;gBACrE,iEAAiE;gBACjE,qEAAqE;gBACrE,OAAO,IAAI,CAAC;YACd,CAAC;YAED,OAAO,CAAC,OAAO,CAAC,CAAC;QACnB,CAAC;QAED,4EAA+C,CAAC,CAAC,CAAC;YAChD,yCAAyC;YACzC,MAAM,WAAW,GAAG,KAAK,CAAC,EAAE,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;YACxC,IACE,WAAW,EAAE,cAAc,kEAA0C;gBACrE,IAAA,2BAAY,EAAC,OAAO,CAAC,YAAY,EAAE,WAAW,CAAC,YAAY,CAAC;4DAChC,EAC5B,CAAC;gBACD,OAAO,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;YAChC,CAAC;YACD,IACE,YAAY,CAAC,cAAc,EAAE,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,EACrE,CAAC;gBACD,+DAA+D;gBAC/D,4DAA4D;gBAC5D,qEAAqE;gBACrE,OAAO,IAAI,CAAC;YACd,CAAC;YAED,OAAO,CAAC,OAAO,CAAC,CAAC;QACnB,CAAC;QAED;YACE,OAAO,IAAI,CAAC;IAChB,CAAC;AACH,CAAC,CAAC;AAEF;;;;;;GAMG;AACH,SAAS,cAAc,CACrB,KAAqB,EACrB,QAAwB,EACxB,UAAsB;IAEtB,MAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC/B,MAAM,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC;IAC/C,IAAI,QAAQ,GAAG,IAAA,iBAAU,EACvB,UAAU,CAAC,aAAa,CAAC,QAAQ,CAAC,EAClC,wBAAiB,CAAC,YAAY,CAAC,WAAW,EAAE,QAAQ,CAAC,IAAI,CAAC,CAC3D,CAAC;IACF,IAAI,SAAS,GAAG,IAAA,iBAAU,EACxB,UAAU,CAAC,YAAY,CAAC,SAAS,CAAC,EAClC,wBAAiB,CAAC,YAAY,CAAC,WAAW,EAAE,SAAS,CAAC,IAAI,CAAC,CAC5D,CAAC;IAEF,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC;QACvC,MAAM,KAAK,GAAG,UAAU,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;QAClD,IAAI,CAAC,KAAK,IAAI,CAAC,IAAA,0BAAmB,EAAC,KAAK,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC;YAC1E,MAAM;QACR,CAAC;QACD,QAAQ,GAAG,KAAK,CAAC;IACnB,CAAC;IAED,OAAO,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC;QACxC,MAAM,KAAK,GAAG,UAAU,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;QAClD,IAAI,CAAC,KAAK,IAAI,CAAC,IAAA,0BAAmB,EAAC,KAAK,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC;YAC1E,MAAM;QACR,CAAC;QACD,SAAS,GAAG,KAAK,CAAC;IACpB,CAAC;IAED,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACjD,CAAC;AAED,SAAS,mBAAmB,CAC1B,UAAsB,EACtB,cAAiD,EACjD,IAAmB,EACnB,QAAqB,EACrB,OAAmC,EACnC,KAAqB;IAErB,MAAM,WAAW,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAE5C,IAAI,kBAA2B,CAAC;IAChC,IACE,OAAO,CAAC,kEAAkE;QAC1E,IAAI,EACJ,CAAC;QACD,2CAA2C;QAC3C,kBAAkB,GAAG,KAAK,CAAC;IAC7B,CAAC;IACD,yEAAyE;IACzE,2EAA2E;IAC3E,uEAAuE;IACvE,iDAAiD;SAC5C,IACH,WAAW,CAAC,cAAc,4EAA+C;QACzE,WAAW,CAAC,cAAc;yFACqB;QAC/C,WAAW,CAAC,cAAc,4EAA+C;QACzE,WAAW,CAAC,cAAc;yFACqB;QAC/C,CAAC,QAAQ,KAAK,IAAI;YAChB,WAAW,CAAC,cAAc,wDAAqC,CAAC,EAClE,CAAC;QACD,yEAAyE;QACzE,yEAAyE;QACzE,cAAc;QACd,kBAAkB,GAAG,KAAK,CAAC;IAC7B,CAAC;SAAM,CAAC;QACN,kBAAkB,GAAG,IAAI,CAAC;QAE1B,KAAK,MAAM,OAAO,IAAI,KAAK,EAAE,CAAC;YAC5B,IAAI,YAAY,CAAC,cAAc,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC;gBACvE,kBAAkB,GAAG,KAAK,CAAC;gBAC3B,MAAM;YACR,CAAC;QACH,CAAC;QAED,0EAA0E;QAC1E,wEAAwE;QACxE,yDAAyD;QACzD,uEAAuE;QACvE,6DAA6D;QAC7D,EAAE;QACF,oEAAoE;QACpE,qEAAqE;QACrE,sBAAsB;IACxB,CAAC;IAED,8EAA8E;IAC9E,2EAA2E;IAC3E,4EAA4E;IAC5E,+DAA+D;IAC/D,EAAE;IACF,8EAA8E;IAC9E,0EAA0E;IAC1E,6EAA6E;IAC7E,2EAA2E;IAC3E,wEAAwE;IACxE,WAAW;IACX,EAAE;IACF,qEAAqE;IACrE,8EAA8E;IAC9E,iBAAiB;IACjB,EAAE;IACF,KAAK;IACL,4DAA4D;IAC5D,WAAW;IACX,qCAAqC;IACrC,yBAAyB;IACzB,uDAAuD;IACvD,mCAAmC;IACnC,0DAA0D;IAC1D,uCAAuC;IAEvC,MAAM,KAAK,GAAG,EAAE,CAAC;IACjB,KAAK,MAAM,OAAO,IAAI,KAAK,EAAE,CAAC;QAC5B,MAAM,WAAW,GAAG,sBAAsB,CACxC,UAAU,EACV,OAAO,CAAC,YAAY,CACrB,CAAC;QACF,MAAM,IAAI,GAAG,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QAC7C,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACpB,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACrB,2EAA2E;gBAC3E,uBAAuB;gBACvB,yBAAyB;gBACzB,cAAc;gBACd,wBAAwB;gBACxB,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC;YAC1B,CAAC;YACD,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;QACtB,CAAC;IACH,CAAC;IAED,IAAI,OAAO,GAAG,KAAK;SAChB,GAAG,CAAC,IAAI,CAAC,EAAE;QACV,IAAI,GAAG,GAAG,EAAE,CAAC;QACb,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,GAAG,IAAI,IAAI,CAAC;QACd,CAAC;aAAM,CAAC;YACN,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;gBACjB,GAAG,IAAI,GAAG,CAAC;YACb,CAAC;YACD,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;gBACrB,GAAG,IAAI,GAAG,CAAC;YACb,CAAC;QACH,CAAC;QACD,IACE,IAAI,CAAC,UAAU,KAAK,yBAAkB,CAAC,OAAO;YAC9C,IAAI,CAAC,UAAU,GAAG,yBAAkB,CAAC,MAAM,EAC3C,CAAC;YACD,GAAG,IAAI,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC;QAC1B,CAAC;aAAM,CAAC;YACN,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC;QACnB,CAAC;QACD,OAAO,GAAG,CAAC;IACb,CAAC,CAAC;SACD,IAAI,CAAC,EAAE,CAAC,CAAC;IAEZ,IAAI,WAAW,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB,EAAE,CAAC;QAC9D,8CAA8C;QAC9C,mBAAmB;QACnB,kCAAkC;QAClC,MAAM,QAAQ,GAAG,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC;QAC3C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,CAAC,GAAG,EAAE;YAC5B,IAAI,WAAW,CAAC,MAAM,EAAE,CAAC;gBACvB,MAAM,aAAa,GACjB,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;oBAC5D,CAAC,CAAC,GAAG,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;oBACvC,CAAC,CAAC,EAAE,CAAC;gBAET,OAAO;oBACL,IAAI,EAAE,UAAU,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC;oBAC/C,KAAK,EAAE,aAAa,GAAG,OAAO;iBAC/B,CAAC;YACJ,CAAC;YACD,MAAM,aAAa,GACjB,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;gBAC3D,CAAC,CAAC,GAAG,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG;gBACtC,CAAC,CAAC,EAAE,CAAC;YACT,OAAO;gBACL,IAAI,EAAE,aAAa,GAAG,OAAO;gBAC7B,KAAK,EAAE,UAAU,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC;aAClD,CAAC;QACJ,CAAC,CAAC,EAAE,CAAC;QAEL,OAAO,GAAG,GAAG,IAAI,IAAI,QAAQ,IAAI,KAAK,EAAE,CAAC;IAC3C,CAAC;SAAM,IAAI,WAAW,CAAC,cAAc,wDAAqC,EAAE,CAAC;QAC3E,OAAO,GAAG,IAAI,OAAO,EAAE,CAAC;IAC1B,CAAC;IAED,MAAM,WAAW,GAAG,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;IAElE,MAAM,GAAG,GAAsB,KAAK,CAAC,EAAE,CACrC,KAAK,CAAC,gBAAgB,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;IAE/C,OAAO;QACL,SAAS,EAAE,qBAAqB;QAChC,GAAG,EAAE;YACH,KAAK,EAAE,UAAU,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;YACjD,GAAG,EAAE,UAAU,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;SAChD;QACD,GAAG,IAAA,sBAAe,EAAC;YACjB,MAAM,EAAE,CAAC,kBAAkB;YAC3B,UAAU,EAAE;gBACV,SAAS,EAAE,sBAAsB;gBACjC,GAAG;aACJ;SACF,CAAC;KACH,CAAC;IASF,SAAS,sBAAsB,CAC7B,UAAsB,EACtB,IAAmB;QAEnB,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;YAClB,KAAK,sBAAc,CAAC,eAAe;gBACjC,OAAO,sBAAsB,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;YAE7D,KAAK,sBAAc,CAAC,cAAc,CAAC,CAAC,CAAC;gBACnC,MAAM,aAAa,GAAG,CAAC,GAAG,EAAE;oBAC1B,MAAM,iBAAiB,GAAG,IAAA,iBAAU,EAClC,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,EAC7B,wBAAiB,CAAC,YAAY,CAAC,qBAAqB,EAAE,IAAI,CAAC,IAAI,CAAC,CACjE,CAAC;oBACF,MAAM,iBAAiB,GAAG,IAAA,iBAAU,EAClC,UAAU,CAAC,oBAAoB,CAC7B,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,MAAM,EACjC,iBAAiB,EACjB,0BAAmB,CACpB,EACD,wBAAiB,CAAC,YAAY,CAAC,qBAAqB,EAAE,IAAI,CAAC,IAAI,CAAC,CACjE,CAAC;oBACF,OAAO,UAAU,CAAC,IAAI,CAAC,SAAS,CAC9B,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC,EAC1B,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC,CAC3B,CAAC;gBACJ,CAAC,CAAC,EAAE,CAAC;gBAEL,MAAM,iBAAiB,GAAG,CAAC,GAAG,EAAE;oBAC9B,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,EAAE,CAAC;wBAC/B,OAAO,EAAE,CAAC;oBACZ,CAAC;oBAED,OAAO,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBAChD,CAAC,CAAC,EAAE,CAAC;gBAEL,OAAO;oBACL,GAAG,sBAAsB,CAAC,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC;oBAClD;wBACE,OAAO,EAAE,KAAK;wBACd,QAAQ,EAAE,IAAI,CAAC,QAAQ;wBACvB,yBAAyB;wBACzB,UAAU,EAAE,yBAAkB,CAAC,OAAO;wBACtC,WAAW,EAAE,KAAK;wBAClB,IAAI,EAAE,iBAAiB,GAAG,aAAa;qBACxC;iBACF,CAAC;YACJ,CAAC;YAED,KAAK,sBAAc,CAAC,gBAAgB,CAAC,CAAC,CAAC;gBACrC,MAAM,YAAY,GAAG,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACvD,OAAO;oBACL,GAAG,sBAAsB,CAAC,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC;oBAClD;wBACE,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,mBAAmB;wBAChE,QAAQ,EAAE,IAAI,CAAC,QAAQ;wBACvB,UAAU,EAAE,IAAI,CAAC,QAAQ;4BACvB,CAAC,CAAC,qEAAqE;gCACrE,yBAAkB,CAAC,OAAO;4BAC5B,CAAC,CAAC,IAAA,mCAA4B,EAAC,IAAI,CAAC,QAAQ,CAAC;wBAC/C,WAAW,EAAE,CAAC,IAAI,CAAC,QAAQ;wBAC3B,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,YAAY,GAAG,CAAC,CAAC,CAAC,YAAY;qBACzD;iBACF,CAAC;YACJ,CAAC;YAED,KAAK,sBAAc,CAAC,mBAAmB;gBACrC,OAAO,sBAAsB,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;YAE7D;gBACE,OAAO;oBACL;wBACE,OAAO,EAAE,KAAK;wBACd,QAAQ,EAAE,KAAK;wBACf,UAAU,EAAE,IAAA,mCAA4B,EAAC,IAAI,CAAC;wBAC9C,WAAW,EAAE,KAAK;wBAClB,IAAI,EAAE,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC;qBAC/B;iBACF,CAAC;QACN,CAAC;IACH,CAAC;AACH,CAAC;AAED,SAAgB,YAAY,CAC1B,OAGC,EACD,cAAiD,EACjD,OAAmC,EACnC,IAAmB,EACnB,QAAgD,EAChD,KAAqB;IAErB,2DAA2D;IAC3D,IACE,KAAK,CAAC,MAAM,IAAI,CAAC;QACjB,yGAAyG;QACzG,QAAQ,KAAK,IAAI,EACjB,CAAC;QACD,OAAO;IACT,CAAC;IAED,MAAM,cAAc,GAAG,CAAC,GAAG,EAAE;QAC3B,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,IAAI;gBACP,OAAO,sBAAsB,CAAC;YAEhC,KAAK,IAAI;gBACP,OAAO,qBAAqB,CAAC;QACjC,CAAC;IACH,CAAC,CAAC,EAAE,CAAC;IAEL,yEAAyE;IACzE,4DAA4D;IAC5D,IAAI,QAAQ,GAA+C,EAAE,CAAC;IAC9D,MAAM,oBAAoB,GAAG,CAC3B,YAAyD,EACnD,EAAE;QACR,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxB,MAAM,YAAY,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;YACrC,IAAA,6CAAqB,EACnB,OAAO,EACP,cAAc,EACd,OAAO,EACP,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,EACjD,mBAAmB,CACjB,OAAO,CAAC,UAAU,EAClB,cAAc,EACd,IAAI,EACJ,QAAQ,EACR,OAAO,EACP,YAAY,CACb,CACF,CAAC;QACJ,CAAC;QAED,0DAA0D;QAC1D,iEAAiE;QACjE,EAAE;QACF,4EAA4E;QAC5E,yEAAyE;QACzE,2BAA2B;QAC3B,EAAE;QACF,yCAAyC;QACzC,2DAA2D;QAC3D,qDAAqD;QACrD,oDAAoD;QACpD,uEAAuE;QACvE,QAAQ,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAChD,CAAC,CAAC;IAEF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;QACzC,MAAM,WAAW,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3C,MAAM,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAEzB,MAAM,iBAAiB,GAAG,cAAc,CAAC,cAAc,EAAE,OAAO,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;QAC5E,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACvB,eAAe;YACf,mEAAmE;YACnE,0EAA0E;YAC1E,aAAa;YACb,8BAA8B;YAC9B,iCAAiC;YACjC,yEAAyE;YACzE,gEAAgE;YAEhE,oBAAoB,EAAE,CAAC;YACvB,SAAS;QACX,CAAC;QACD,uFAAuF;QACvF,CAAC,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAAC;QAElC,MAAM,cAAc,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC;QAC5C,IAAI,WAAW,EAAE,CAAC;YAChB,MAAM,gBAAgB,GAAG,IAAA,2BAAY,EACnC,WAAW,CAAC,YAAY;YACxB,sFAAsF;YACtF,wDAAwD;YACxD,oCAAoC;YACpC,iBAAiB,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,YAAY,CAC7D,CAAC;YACF,IAAI,gBAAgB,+CAAgC,EAAE,CAAC;gBACrD,4DAA4D;gBAC5D,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAChC,CAAC;iBAAM,IAAI,gBAAgB,iDAAiC,EAAE,CAAC;gBAC7D,oBAAoB,CAAC,iBAAiB,CAAC,CAAC;YAC1C,CAAC;iBAAM,CAAC;gBACN,oEAAoE;gBACpE,qDAAqD;gBACrD,aAAa;YACf,CAAC;QACH,CAAC;aAAM,CAAC;YACN,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAChC,CAAC;IACH,CAAC;IAED,sBAAsB;IACtB,oBAAoB,EAAE,CAAC;AACzB,CAAC"}