{"version": 3, "file": "prefer-promise-reject-errors.js", "sourceRoot": "", "sources": ["../../src/rules/prefer-promise-reject-errors.ts"], "names": [], "mappings": ";;AACA,oDAA0D;AAE1D,kCAUiB;AAUjB,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,8BAA8B;IACpC,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EAAE,0DAA0D;YACvE,WAAW,EAAE,aAAa;YAC1B,eAAe,EAAE,IAAI;YACrB,oBAAoB,EAAE,IAAI;SAC3B;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,gBAAgB,EAAE;wBAChB,WAAW,EACT,iEAAiE;wBACnE,IAAI,EAAE,SAAS;qBAChB;iBACF;gBACD,oBAAoB,EAAE,KAAK;aAC5B;SACF;QACD,QAAQ,EAAE;YACR,aAAa,EAAE,uDAAuD;SACvE;KACF;IACD,cAAc,EAAE;QACd;YACE,gBAAgB,EAAE,KAAK;SACxB;KACF;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC;QACvB,MAAM,QAAQ,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAE5C,SAAS,eAAe,CAAC,cAAuC;YAC9D,MAAM,QAAQ,GAAG,cAAc,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAChD,IAAI,QAAQ,EAAE,CAAC;gBACb,MAAM,IAAI,GAAG,QAAQ,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;gBAClD,IACE,IAAA,kBAAW,EAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC;oBACnC,IAAA,0BAAmB,EAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,EAC3C,CAAC;oBACD,OAAO;gBACT,CAAC;YACH,CAAC;iBAAM,IAAI,OAAO,CAAC,gBAAgB,EAAE,CAAC;gBACpC,OAAO;YACT,CAAC;YAED,OAAO,CAAC,MAAM,CAAC;gBACb,IAAI,EAAE,cAAc;gBACpB,SAAS,EAAE,eAAe;aAC3B,CAAC,CAAC;QACL,CAAC;QAED,SAAS,mBAAmB,CAC1B,IAAO;YAEP,OAAO,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;gBACjD,CAAC,CAAC,IAAI,CAAC,UAAU;gBACjB,CAAC,CAAC,IAAI,CAAC;QACX,CAAC;QAED,SAAS,2BAA2B,CAAC,IAAmB;YACtD,MAAM,IAAI,GAAG,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAC9C,OAAO,CACL,IAAA,+BAAwB,EAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC;gBAChD,IAAA,oBAAa,EAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,CACtC,CAAC;QACJ,CAAC;QAED,OAAO;YACL,cAAc,CAAC,IAAI;gBACjB,MAAM,MAAM,GAAG,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAEhD,IAAI,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB,EAAE,CAAC;oBACpD,OAAO;gBACT,CAAC;gBAED,IACE,CAAC,IAAA,kCAA2B,EAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAC;oBACvD,CAAC,2BAA2B,CAAC,MAAM,CAAC,MAAM,CAAC,EAC3C,CAAC;oBACD,OAAO;gBACT,CAAC;gBAED,eAAe,CAAC,IAAI,CAAC,CAAC;YACxB,CAAC;YACD,aAAa,CAAC,IAAI;gBAChB,MAAM,MAAM,GAAG,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAChD,IACE,CAAC,IAAA,+BAAwB,EACvB,QAAQ,CAAC,OAAO,EAChB,QAAQ,CAAC,iBAAiB,CAAC,MAAM,CAAC,CACnC,EACD,CAAC;oBACD,OAAO;gBACT,CAAC;gBAED,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBACtC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAA,iBAAU,EAAC,QAAQ,CAAC,EAAE,CAAC;oBACvC,OAAO;gBACT,CAAC;gBACD,MAAM,eAAe,GAAG,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBAC9C,IAAI,CAAC,eAAe,IAAI,CAAC,IAAA,mBAAY,EAAC,eAAe,CAAC,EAAE,CAAC;oBACvD,OAAO;gBACT,CAAC;gBAED,mEAAmE;gBACnE,oEAAoE;gBACpE,MAAM,cAAc,GAAG,OAAO,CAAC,UAAU;qBACtC,oBAAoB,CAAC,QAAQ,CAAC;qBAC9B,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAE,CAAC;gBAErE,cAAc,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;oBACtC,IACE,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc;wBAC5D,GAAG,CAAC,UAAU,KAAK,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,EAC/C,CAAC;wBACD,OAAO;oBACT,CAAC;oBAED,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;gBACzC,CAAC,CAAC,CAAC;YACL,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}