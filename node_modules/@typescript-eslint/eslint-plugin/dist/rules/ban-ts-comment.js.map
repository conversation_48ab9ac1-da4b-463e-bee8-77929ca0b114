{"version": 3, "file": "ban-ts-comment.js", "sourceRoot": "", "sources": ["../../src/rules/ban-ts-comment.ts"], "names": [], "mappings": ";;AACA,oDAA2D;AAE3D,kCAAkE;AAelE,MAAM,+BAA+B,GAAG,CAAC,CAAC;AAc1C,kBAAe,IAAA,iBAAU,EAAwB;IAC/C,IAAI,EAAE,gBAAgB;IACtB,IAAI,EAAE;QACJ,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EACT,8EAA8E;YAChF,WAAW,EAAE;gBACX,WAAW,EAAE,IAAI;gBACjB,MAAM,EAAE,CAAC,EAAE,wBAAwB,EAAE,EAAE,EAAE,CAAC;aAC3C;SACF;QACD,QAAQ,EAAE;YACR,kBAAkB,EAChB,sEAAsE;YACxE,4BAA4B,EAC1B,sHAAsH;YACxH,qCAAqC,EACnC,6LAA6L;YAC/L,4CAA4C,EAC1C,yFAAyF;YAC3F,gCAAgC,EAC9B,+CAA+C;SAClD;QACD,cAAc,EAAE,IAAI;QACpB,MAAM,EAAE;YACN;gBACE,KAAK,EAAE;oBACL,qBAAqB,EAAE;wBACrB,KAAK,EAAE;4BACL;gCACE,IAAI,EAAE,SAAS;gCACf,OAAO,EAAE,IAAI;6BACd;4BACD;gCACE,IAAI,EAAE,QAAQ;gCACd,IAAI,EAAE,CAAC,wBAAwB,CAAC;6BACjC;4BACD;gCACE,IAAI,EAAE,QAAQ;gCACd,oBAAoB,EAAE,KAAK;gCAC3B,UAAU,EAAE;oCACV,iBAAiB,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;iCACtC;6BACF;yBACF;qBACF;iBACF;gBACD,UAAU,EAAE;oBACV,iBAAiB,EAAE,EAAE,IAAI,EAAE,uCAAuC,EAAE;oBACpE,WAAW,EAAE,EAAE,IAAI,EAAE,uCAAuC,EAAE;oBAC9D,YAAY,EAAE,EAAE,IAAI,EAAE,uCAAuC,EAAE;oBAC/D,UAAU,EAAE,EAAE,IAAI,EAAE,uCAAuC,EAAE;oBAC7D,wBAAwB,EAAE;wBACxB,WAAW,EACT,uFAAuF;wBACzF,IAAI,EAAE,QAAQ;wBACd,OAAO,EAAE,+BAA+B;qBACzC;iBACF;gBACD,IAAI,EAAE,QAAQ;gBACd,oBAAoB,EAAE,KAAK;aAC5B;SACF;KACF;IACD,cAAc,EAAE;QACd;YACE,iBAAiB,EAAE,wBAAwB;YAC3C,WAAW,EAAE,IAAI;YACjB,YAAY,EAAE,IAAI;YAClB,UAAU,EAAE,KAAK;YACjB,wBAAwB,EAAE,+BAA+B;SAC1D;KACF;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC;QACvB,sHAAsH;QACtH,MAAM,qBAAqB,GACzB,+DAA+D,CAAC;QAElE;;;UAGE;QACF,MAAM,+BAA+B,GACnC,gEAAgE,CAAC;QACnE,MAAM,8BAA8B,GAClC,0EAA0E,CAAC;QAE7E,MAAM,kBAAkB,GAAG,IAAI,GAAG,EAAkB,CAAC;QACrD,KAAK,MAAM,SAAS,IAAI;YACtB,iBAAiB;YACjB,WAAW;YACX,YAAY;YACZ,UAAU;SACF,EAAE,CAAC;YACX,MAAM,MAAM,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;YAClC,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,MAAM,CAAC,iBAAiB,EAAE,CAAC;gBAC3D,kBAAkB,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,CAAC;YAC1E,CAAC;QACH,CAAC;QAED,SAAS,kBAAkB,CACzB,KAAa,EACb,GAAW;YAEX,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC9B,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,OAAO,IAAI,CAAC;YACd,CAAC;YAED,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,IAAA,iBAAU,EAC3C,KAAK,CAAC,MAAM,EACZ,8BAA8B,CAC/B,CAAC;YACF,OAAO;gBACL,SAAS,EAAE,IAAA,iBAAU,EACnB,SAAS,EACT,yCAAyC,CAC1C;gBACD,WAAW,EAAE,IAAA,iBAAU,EACrB,WAAW,EACX,2CAA2C,CAC5C;aACF,CAAC;QACJ,CAAC;QAED,SAAS,sBAAsB,CAC7B,OAAyB;YAEzB,IAAI,OAAO,CAAC,IAAI,KAAK,uBAAe,CAAC,IAAI,EAAE,CAAC;gBAC1C,MAAM,aAAa,GAAG,kBAAkB,CACtC,qBAAqB,EACrB,KAAK,OAAO,CAAC,KAAK,EAAE,CACrB,CAAC;gBACF,IAAI,aAAa,EAAE,CAAC;oBAClB,OAAO,aAAa,CAAC;gBACvB,CAAC;gBAED,OAAO,kBAAkB,CACvB,+BAA+B,EAC/B,OAAO,CAAC,KAAK,CACd,CAAC;YACJ,CAAC;YAED,MAAM,YAAY,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAC/C,OAAO,kBAAkB,CACvB,8BAA8B,EAC9B,YAAY,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CACtC,CAAC;QACJ,CAAC;QAED,OAAO;YACL,OAAO,CAAC,IAAI;gBACV,MAAM,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBAEvC,MAAM,QAAQ,GAAG,OAAO,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC;gBAErD,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;oBACzB,MAAM,KAAK,GAAG,sBAAsB,CAAC,OAAO,CAAC,CAAC;oBAC9C,IAAI,CAAC,KAAK,EAAE,CAAC;wBACX,OAAO;oBACT,CAAC;oBACD,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,KAAK,CAAC;oBAEzC,IACE,SAAS,KAAK,SAAS;wBACvB,cAAc;wBACd,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,IAAI,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EACvD,CAAC;wBACD,OAAO;oBACT,CAAC;oBAED,MAAM,aAAa,GAAG,MAAM,SAAS,EAAmB,CAAC;oBAEzD,MAAM,MAAM,GAAG,OAAO,CAAC,aAAa,CAAC,CAAC;oBACtC,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;wBACpB,IAAI,SAAS,KAAK,QAAQ,EAAE,CAAC;4BAC3B,iEAAiE;4BACjE,OAAO,CAAC,MAAM,CAAC;gCACb,IAAI,EAAE,OAAO;gCACb,SAAS,EAAE,8BAA8B;gCACzC,OAAO,EAAE;oCACP;wCACE,SAAS,EAAE,kCAAkC;wCAC7C,GAAG,CAAC,KAAK;4CACP,MAAM,WAAW,GAAG,OAAO,CAAC,KAAK,CAAC,OAAO,CACvC,YAAY,EACZ,kBAAkB,CACnB,CAAC;4CACF,OAAO,KAAK,CAAC,WAAW,CACtB,OAAO,EACP,OAAO,CAAC,IAAI,KAAK,uBAAe,CAAC,IAAI;gDACnC,CAAC,CAAC,KAAK,WAAW,EAAE;gDACpB,CAAC,CAAC,KAAK,WAAW,IAAI,CACzB,CAAC;wCACJ,CAAC;qCACF;iCACF;6BACF,CAAC,CAAC;wBACL,CAAC;6BAAM,CAAC;4BACN,OAAO,CAAC,MAAM,CAAC;gCACb,IAAI,EAAE,EAAE,SAAS,EAAE;gCACnB,IAAI,EAAE,OAAO;gCACb,SAAS,EAAE,oBAAoB;6BAChC,CAAC,CAAC;wBACL,CAAC;oBACH,CAAC;oBAED,IACE,MAAM,KAAK,wBAAwB;wBACnC,CAAC,OAAO,MAAM,KAAK,QAAQ,IAAI,MAAM,CAAC,iBAAiB,CAAC,EACxD,CAAC;wBACD,MAAM,EAAE,wBAAwB,EAAE,GAAG,OAAO,CAAC;wBAC7C,MAAM,MAAM,GAAG,kBAAkB,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;wBACrD,IACE,IAAA,sBAAe,EAAC,WAAW,CAAC,IAAI,EAAE,CAAC;4BACnC,IAAA,iBAAU,EACR,wBAAwB,EACxB,6CAA6C,CAC9C,EACD,CAAC;4BACD,OAAO,CAAC,MAAM,CAAC;gCACb,IAAI,EAAE,EAAE,SAAS,EAAE,wBAAwB,EAAE;gCAC7C,IAAI,EAAE,OAAO;gCACb,SAAS,EAAE,uCAAuC;6BACnD,CAAC,CAAC;wBACL,CAAC;6BAAM,IAAI,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC;4BAC/C,OAAO,CAAC,MAAM,CAAC;gCACb,IAAI,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE;gCAC1C,IAAI,EAAE,OAAO;gCACb,SAAS,EAAE,8CAA8C;6BAC1D,CAAC,CAAC;wBACL,CAAC;oBACH,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}