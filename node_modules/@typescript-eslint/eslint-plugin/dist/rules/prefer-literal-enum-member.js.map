{"version": 3, "file": "prefer-literal-enum-member.js", "sourceRoot": "", "sources": ["../../src/rules/prefer-literal-enum-member.ts"], "names": [], "mappings": ";;AACA,oDAA0D;AAE1D,kCAA2D;AAE3D,kBAAe,IAAA,iBAAU,EAAC;IACxB,IAAI,EAAE,4BAA4B;IAClC,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EAAE,+CAA+C;YAC5D,WAAW,EAAE,QAAQ;YACrB,oBAAoB,EAAE,KAAK;SAC5B;QACD,QAAQ,EAAE;YACR,UAAU,EAAE,sEAAsE;SACnF;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,uBAAuB,EAAE;wBACvB,WAAW,EACT,kEAAkE;wBACpE,IAAI,EAAE,SAAS;qBAChB;iBACF;gBACD,oBAAoB,EAAE,KAAK;aAC5B;SACF;KACF;IACD,cAAc,EAAE;QACd;YACE,uBAAuB,EAAE,KAAK;SAC/B;KACF;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,EAAE,uBAAuB,EAAE,CAAC;QAC3C,SAAS,oBAAoB,CAAC,IAAmB,EAAE,IAAY;YAC7D,OAAO,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC;QACvE,CAAC;QAED,SAAS,aAAa,CACpB,IAAgC,EAChC,IAAY;YAEZ,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAC3B,MAAM,CAAC,EAAE,CACP,oBAAoB,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC;gBACrC,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO;oBACxC,IAAA,2BAAoB,EAAC,MAAM,CAAC,EAAE,CAAC,KAAK,IAAI,CAAC,CAC9C,CAAC;QACJ,CAAC;QAED,SAAS,gBAAgB,CACvB,IAAgC,EAChC,IAAmB;YAEnB,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,EAAE,CAAC;gBAC5C,OAAO,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;YACxC,CAAC;YAED,IACE,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;gBAC7C,oBAAoB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAC/C,CAAC;gBACD,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,EAAE,CAAC;oBACrD,OAAO,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBACjD,CAAC;gBAED,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;oBAClB,MAAM,YAAY,GAAG,IAAA,2BAAoB,EAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;oBACzD,IAAI,YAAY,EAAE,CAAC;wBACjB,OAAO,aAAa,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;oBAC3C,CAAC;gBACH,CAAC;YACH,CAAC;YACD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO;YACL,YAAY,CAAC,IAAI;gBACf,wFAAwF;gBACxF,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,EAAE,CAAC;oBAC7B,OAAO;gBACT,CAAC;gBACD,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;gBAEvC,SAAS,uCAAuC,CAC9C,IAAsD,EACtD,wBAAiC;oBAEjC,8EAA8E;oBAC9E,2DAA2D;oBAC3D,IAAI,wBAAwB,IAAI,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,EAAE,CAAC;wBACpE,OAAO,IAAI,CAAC;oBACd,CAAC;oBAED,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;wBAClB,kBAAkB;wBAClB,KAAK,sBAAc,CAAC,OAAO;4BACzB,OAAO,IAAI,CAAC;wBAEd,sCAAsC;wBACtC,KAAK,sBAAc,CAAC,eAAe;4BACjC,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,CAAC;wBAEvC,KAAK,sBAAc,CAAC,eAAe;4BACjC,mBAAmB;4BACnB,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;gCACvC,OAAO,uCAAuC,CAC5C,IAAI,CAAC,QAAQ,EACb,wBAAwB,CACzB,CAAC;4BACJ,CAAC;4BAED,IAAI,uBAAuB,EAAE,CAAC;gCAC5B,OAAO,CACL,IAAI,CAAC,QAAQ,KAAK,GAAG;oCACrB,uCAAuC,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAC7D,CAAC;4BACJ,CAAC;4BACD,OAAO,KAAK,CAAC;wBAEf,KAAK,sBAAc,CAAC,gBAAgB;4BAClC,IAAI,uBAAuB,EAAE,CAAC;gCAC5B,OAAO,CACL,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC;oCAC1D,uCAAuC,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC;oCACxD,uCAAuC,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAC1D,CAAC;4BACJ,CAAC;4BACD,OAAO,KAAK,CAAC;wBAEf;4BACE,OAAO,KAAK,CAAC;oBACjB,CAAC;gBACH,CAAC;gBAED,IAAI,uCAAuC,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,CAAC,EAAE,CAAC;oBACrE,OAAO;gBACT,CAAC;gBAED,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,IAAI,CAAC,EAAE;oBACb,SAAS,EAAE,YAAY;iBACxB,CAAC,CAAC;YACL,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}