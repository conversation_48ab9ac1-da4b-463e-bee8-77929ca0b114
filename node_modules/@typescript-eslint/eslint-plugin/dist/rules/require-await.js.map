{"version": 3, "file": "require-await.js", "sourceRoot": "", "sources": ["../../src/rules/require-await.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA2E;AAE3E,sDAAwC;AAGxC,kCASiB;AAcjB,kBAAe,IAAA,iBAAU,EAAC;IACxB,IAAI,EAAE,eAAe;IACrB,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EACT,sFAAsF;YACxF,WAAW,EAAE,aAAa;YAC1B,oBAAoB,EAAE,IAAI;YAC1B,eAAe,EAAE,IAAI;SACtB;QACD,MAAM,EAAE,EAAE;QACV,QAAQ,EAAE;YACR,YAAY,EAAE,qCAAqC;YACnD,WAAW,EAAE,iBAAiB;SAC/B;QACD,cAAc,EAAE,IAAI;KACrB;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ,MAAM,QAAQ,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAC5C,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAElD,IAAI,SAAS,GAAqB,IAAI,CAAC;QAEvC;;WAEG;QACH,SAAS,aAAa,CAAC,IAAkB;YACvC,SAAS,GAAG;gBACV,KAAK,EAAE,SAAS;gBAChB,QAAQ,EAAE,KAAK;gBACf,QAAQ,EAAE,IAAI,CAAC,KAAK;gBACpB,KAAK,EAAE,IAAI,CAAC,SAAS,IAAI,KAAK;gBAC9B,YAAY,EAAE,KAAK;aACpB,CAAC;QACJ,CAAC;QAED;;;WAGG;QACH,SAAS,YAAY,CAAC,IAAkB;YACtC,wBAAwB,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;gBACxC,8EAA8E;gBAC9E,OAAO;YACT,CAAC;YAED,IACE,IAAI,CAAC,KAAK;gBACV,CAAC,SAAS,CAAC,QAAQ;gBACnB,CAAC,eAAe,CAAC,IAAI,CAAC;gBACtB,CAAC,CAAC,SAAS,CAAC,KAAK,IAAI,SAAS,CAAC,YAAY,CAAC,EAC5C,CAAC;gBACD,oDAAoD;gBACpD,0DAA0D;gBAC1D,4DAA4D;gBAC5D,MAAM,oBAAoB,GACxB,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;oBACnD,IAAI,CAAC,MAAM,CAAC,KAAK,KAAK,IAAI,CAAC;oBAC7B,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,QAAQ;wBAC3C,IAAI,CAAC,MAAM,CAAC,MAAM;wBAClB,IAAI,CAAC,MAAM,CAAC,KAAK,KAAK,IAAI,CAAC;oBAC3B,CAAC,CAAC,IAAI,CAAC,MAAM;oBACb,CAAC,CAAC,IAAI,CAAC;gBAEX,MAAM,UAAU,GAAG,IAAA,iBAAU,EAC3B,OAAO,CAAC,UAAU,CAAC,aAAa,CAC9B,oBAAoB,EACpB,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,KAAK,OAAO,CACjC,EACD,kEAAkE,CACnE,CAAC;gBAEF,MAAM,UAAU,GAAwB;oBACtC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;oBACnB,IAAA,iBAAU,EACR,OAAO,CAAC,UAAU,CAAC,aAAa,CAAC,UAAU,EAAE;wBAC3C,eAAe,EAAE,IAAI;qBACtB,CAAC,EACF,yDAAyD,CAC1D,CAAC,KAAK,CAAC,CAAC,CAAC;iBACF,CAAC;gBAEX,+DAA+D;gBAC/D,iEAAiE;gBACjE,6DAA6D;gBAC7D,oCAAoC;gBACpC,MAAM,SAAS,GAAG,IAAA,iBAAU,EAC1B,OAAO,CAAC,UAAU,CAAC,aAAa,CAAC,UAAU,CAAC,EAC5C,yDAAyD,CAC1D,CAAC;gBACF,MAAM,YAAY,GAChB,SAAS,CAAC,IAAI,KAAK,uBAAe,CAAC,UAAU;oBAC7C,CAAC,SAAS,CAAC,KAAK,KAAK,GAAG,IAAI,SAAS,CAAC,KAAK,KAAK,GAAG,CAAC;oBACpD,CAAC,oBAAoB,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;wBAC5D,IAAA,mCAA4B,EAAC,oBAAoB,CAAC,CAAC;oBACrD,IAAA,8BAAuB,EAAC,OAAO,CAAC,UAAU,EAAE,oBAAoB,CAAC,CAAC;gBAEpE,MAAM,OAAO,GAAG;oBACd,EAAE,KAAK,EAAE,UAAU,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,EAAE;iBACnE,CAAC;gBAEF,iDAAiD;gBACjD,mDAAmD;gBACnD,oDAAoD;gBACpD,oDAAoD;gBACpD,qDAAqD;gBACrD,2CAA2C;gBAC3C,IACE,IAAI,CAAC,UAAU,EAAE,cAAc,CAAC,IAAI;oBACpC,sBAAc,CAAC,eAAe,EAC9B,CAAC;oBACD,IAAI,SAAS,CAAC,KAAK,EAAE,CAAC;wBACpB,IAAI,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,gBAAgB,CAAC,EAAE,CAAC;4BAClE,OAAO,CAAC,IAAI,CAAC;gCACX,KAAK,EAAE,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,QAAQ,CAAC,KAAK;gCACpD,WAAW,EAAE,WAAW;6BACzB,CAAC,CAAC;wBACL,CAAC;oBACH,CAAC;yBAAM,IACL,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,SAAS,CAAC;wBACtD,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,aAAa,IAAI,IAAI,EACpD,CAAC;wBACD,MAAM,SAAS,GAAG,IAAA,iBAAU,EAC1B,OAAO,CAAC,UAAU,CAAC,aAAa,CAC9B,IAAI,CAAC,UAAU,CAAC,cAAc,EAC9B,KAAK,CAAC,EAAE,CACN,KAAK,CAAC,IAAI,KAAK,uBAAe,CAAC,UAAU;4BACzC,KAAK,CAAC,KAAK,KAAK,GAAG,CACtB,EACD,4DAA4D,CAC7D,CAAC;wBACF,MAAM,UAAU,GAAG,IAAA,iBAAU,EAC3B,OAAO,CAAC,UAAU,CAAC,YAAY,CAC7B,IAAI,CAAC,UAAU,CAAC,cAAc,EAC9B,KAAK,CAAC,EAAE,CACN,KAAK,CAAC,IAAI,KAAK,uBAAe,CAAC,UAAU;4BACzC,KAAK,CAAC,KAAK,KAAK,GAAG,CACtB,EACD,4DAA4D,CAC7D,CAAC;wBACF,OAAO,CAAC,IAAI;wBACV,qCAAqC;wBACrC,EAAE,KAAK,EAAE,UAAU,CAAC,KAAK,EAAE,WAAW,EAAE,SAAS,EAAE;wBACnD,kCAAkC;wBAClC,kCAAkC;wBAClC;4BACE,KAAK,EAAE;gCACL,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;gCAChD,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;6BACnB;4BACD,WAAW,EAAE,SAAS;yBACvB,CACF,CAAC;oBACJ,CAAC;gBACH,CAAC;gBAED,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,GAAG,EAAE,IAAA,yBAAkB,EAAC,IAAI,EAAE,OAAO,CAAC,UAAU,CAAC;oBACjD,SAAS,EAAE,cAAc;oBACzB,IAAI,EAAE;wBACJ,IAAI,EAAE,IAAA,qBAAc,EAAC,IAAA,8BAAuB,EAAC,IAAI,CAAC,CAAC;qBACpD;oBACD,OAAO,EAAE;wBACP;4BACE,SAAS,EAAE,aAAa;4BACxB,GAAG,EAAE,CAAC,KAAK,EAAa,EAAE,CACxB,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CACnB,MAAM,CAAC,WAAW,KAAK,SAAS;gCAC9B,CAAC,CAAC,KAAK,CAAC,gBAAgB,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,WAAW,CAAC;gCAC1D,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,CACpC;yBACJ;qBACF;iBACF,CAAC,CAAC;YACL,CAAC;YAED,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC;QAC9B,CAAC;QAED;;WAEG;QACH,SAAS,cAAc,CAAC,IAAa;YACnC,MAAM,IAAI,GAAG,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAE7C,OAAO,OAAO,CAAC,cAAc,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QACrD,CAAC;QAED;;WAEG;QACH,SAAS,cAAc;YACrB,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,OAAO;YACT,CAAC;YACD,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAC;QAC5B,CAAC;QAED;;;;;WAKG;QACH,SAAS,oBAAoB,CAAC,IAA8B;YAC1D,IAAI,CAAC,SAAS,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACxC,OAAO;YACT,CAAC;YAED,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO,EAAE,CAAC;gBAClD,sEAAsE;gBACtE,0CAA0C;gBAC1C,OAAO;YACT,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACnB,IAAI,cAAc,CAAC,QAAQ,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC;oBACtE,SAAS,CAAC,YAAY,GAAG,IAAI,CAAC;gBAChC,CAAC;gBACD,OAAO;YACT,CAAC;YAED,MAAM,IAAI,GAAG,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACvD,MAAM,YAAY,GAAG,6BAA6B,CAAC,IAAI,CAAC,CAAC;YACzD,KAAK,MAAM,IAAI,IAAI,YAAY,EAAE,CAAC;gBAChC,MAAM,aAAa,GAAG,OAAO,CAAC,gCAAgC,CAC5D,IAAI,EACJ,eAAe,EACf,OAAO,CACR,CAAC;gBACF,IAAI,aAAa,KAAK,SAAS,EAAE,CAAC;oBAChC,SAAS,CAAC,YAAY,GAAG,IAAI,CAAC;oBAC9B,MAAM;gBACR,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO;YACL,mBAAmB,EAAE,aAAa;YAClC,kBAAkB,EAAE,aAAa;YACjC,uBAAuB,EAAE,aAAa;YACtC,0BAA0B,EAAE,YAAY;YACxC,yBAAyB,EAAE,YAAY;YACvC,8BAA8B,EAAE,YAAY;YAE5C,eAAe,EAAE,cAAc;YAC/B,2CAA2C,EAAE,cAAc;YAC3D,8BAA8B,EAAE,cAAc;YAC9C,eAAe,EAAE,oBAAoB;YAErC,wCAAwC;YACxC,gEAAgE;YAChE,+EAA+E,CAC7E,IAGC;gBAED,MAAM,UAAU,GAAG,QAAQ,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBAC5D,IAAI,cAAc,CAAC,UAAU,CAAC,EAAE,CAAC;oBAC/B,cAAc,EAAE,CAAC;gBACnB,CAAC;YACH,CAAC;YACD,eAAe,CAAC,IAAI;gBAClB,uDAAuD;gBACvD,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,QAAQ,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;oBAC5D,OAAO;gBACT,CAAC;gBAED,MAAM,EAAE,UAAU,EAAE,GAAG,QAAQ,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBAChE,IAAI,UAAU,IAAI,cAAc,CAAC,UAAU,CAAC,EAAE,CAAC;oBAC7C,cAAc,EAAE,CAAC;gBACnB,CAAC;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC;AAEH,SAAS,eAAe,CAAC,IAAkB;IACzC,OAAO,CACL,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc;QAChD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,CAC5B,CAAC;AACJ,CAAC;AAED,SAAS,6BAA6B,CAAC,IAAa;IAClD,IAAI,IAAI,CAAC,qBAAqB,EAAE,EAAE,CAAC;QACjC,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,6BAA6B,CAAC,CAAC;IAC3D,CAAC;IACD,OAAO,CAAC,IAAI,CAAC,CAAC;AAChB,CAAC;AAED,SAAS,WAAW,CAClB,aAAuC,EACvC,QAAgB;IAEhB,OAAO,CACL,aAAa,CAAC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;QACzD,aAAa,CAAC,QAAQ,CAAC,IAAI,KAAK,QAAQ,CACzC,CAAC;AACJ,CAAC"}