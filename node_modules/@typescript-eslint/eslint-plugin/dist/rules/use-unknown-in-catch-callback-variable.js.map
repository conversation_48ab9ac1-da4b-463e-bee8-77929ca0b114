{"version": 3, "file": "use-unknown-in-catch-callback-variable.js", "sourceRoot": "", "sources": ["../../src/rules/use-unknown-in-catch-callback-variable.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAE1D,sDAAwC;AAGxC,kCAOiB;AAWjB,MAAM,qBAAqB,GACzB,6EAA6E,CAAC;AAEhF,kBAAe,IAAA,iBAAU,EAAiB;IACxC,IAAI,EAAE,wCAAwC;IAC9C,IAAI,EAAE;QACJ,IAAI,EAAE;YACJ,WAAW,EACT,sEAAsE;YACxE,oBAAoB,EAAE,IAAI;YAC1B,WAAW,EAAE,QAAQ;SACtB;QACD,IAAI,EAAE,YAAY;QAClB,QAAQ,EAAE;YACR,UAAU,EAAE,qBAAqB;YACjC,mCAAmC,EAAE,GAAG,qBAAqB,wCAAwC;YACrG,oCAAoC,EAAE,GACpC,qBACF,wEAAwE;YACxE,kCAAkC,EAChC,iFAAiF;YACnF,sCAAsC,EACpC,wFAAwF;YAC1F,6BAA6B,EAC3B,iDAAiD;YACnD,iCAAiC,EAC/B,mDAAmD;SACtD;QACD,OAAO,EAAE,MAAM;QACf,MAAM,EAAE,EAAE;QACV,cAAc,EAAE,IAAI;KACrB;IAED,cAAc,EAAE,EAAE;IAElB,MAAM,CAAC,OAAO;QACZ,MAAM,EAAE,OAAO,EAAE,qBAAqB,EAAE,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QACtE,MAAM,OAAO,GAAG,OAAO,CAAC,cAAc,EAAE,CAAC;QAEzC,SAAS,sBAAsB,CAAC,IAAa;YAC3C,KAAK,MAAM,SAAS,IAAI,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC;gBACrD,MAAM,cAAc,GAAG,OAAO,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC;gBAClE,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAChC,qFAAqF;oBACrF,SAAS;gBACX,CAAC;gBAED,KAAK,MAAM,aAAa,IAAI,cAAc,EAAE,CAAC;oBAC3C,MAAM,UAAU,GAAG,aAAa,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;oBAClD,IAAI,CAAC,UAAU,EAAE,CAAC;wBAChB,yDAAyD;wBACzD,SAAS;oBACX,CAAC;oBAED,IAAI,cAAc,GAAG,OAAO,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;oBAEzD,MAAM,IAAI,GAAG,UAAU,CAAC,gBAAgB,CAAC;oBACzC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAA,iCAA0B,EAAC,IAAI,CAAC,EAAE,CAAC;wBACrD,IAAI,OAAO,CAAC,WAAW,CAAC,cAAc,CAAC,EAAE,CAAC;4BACxC,cAAc,GAAG,OAAO,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC/D,CAAC;6BAAM,IAAI,OAAO,CAAC,WAAW,CAAC,cAAc,CAAC,EAAE,CAAC;4BAC/C,cAAc,GAAG,OAAO,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC/D,CAAC;6BAAM,CAAC;4BACN,wEAAwE;4BACxE,OAAO,IAAI,CAAC;wBACd,CAAC;oBACH,CAAC;oBAED,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC,cAAc,CAAC,EAAE,CAAC;wBACpD,OAAO,IAAI,CAAC;oBACd,CAAC;gBACH,CAAC;YACH,CAAC;YAED,OAAO,KAAK,CAAC;QACf,CAAC;QAED,SAAS,kBAAkB,CAAC,IAAyB;YACnD,MAAM,QAAQ,GAAG,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YACjD,MAAM,cAAc,GAAG,OAAO,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YAC3D,OAAO,sBAAsB,CAAC,cAAc,CAAC,CAAC;QAChD,CAAC;QAED;;;;;;WAMG;QACH,SAAS,sBAAsB,CAC7B,QAA6B;YAE7B,uEAAuE;YACvE,IACE,CAAC,CACC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,uBAAuB;gBACxD,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB,CACpD,EACD,CAAC;gBACD,OAAO,SAAS,CAAC;YACnB,CAAC;YAED,MAAM,oCAAoC,GAAG,IAAA,iBAAU,EACrD,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,EACrB,6EAA6E,CAC9E,CAAC;YAEF,0FAA0F;YAC1F,MAAM,kBAAkB,GACtB,oCAGC,CAAC;YACJ,MAAM,kBAAkB,GACtB,kBAAkB,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB;gBAC1D,CAAC,CAAC,kBAAkB,CAAC,IAAI;gBACzB,CAAC,CAAC,kBAAkB,CAAC;YAEzB,QAAQ,kBAAkB,CAAC,IAAI,EAAE,CAAC;gBAChC,KAAK,sBAAc,CAAC,UAAU,CAAC,CAAC,CAAC;oBAC/B,MAAM,2BAA2B,GAAG,kBAAkB,CAAC,cAAc,CAAC;oBACtE,IAAI,2BAA2B,IAAI,IAAI,EAAE,CAAC;wBACxC,OAAO;4BACL,IAAI,EAAE,kBAAkB;4BACxB,OAAO,EAAE;gCACP;oCACE,SAAS,EAAE,oCAAoC;oCAC/C,GAAG,EAAE,CAAC,KAAyB,EAAsB,EAAE;wCACrD,IACE,QAAQ,CAAC,IAAI;4CACX,sBAAc,CAAC,uBAAuB;4CACxC,IAAA,+BAAwB,EAAC,QAAQ,EAAE,OAAO,CAAC,UAAU,CAAC,EACtD,CAAC;4CACD,OAAO;gDACL,KAAK,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,GAAG,CAAC;gDAC/C,KAAK,CAAC,eAAe,CAAC,kBAAkB,EAAE,YAAY,CAAC;6CACxD,CAAC;wCACJ,CAAC;wCAED,OAAO;4CACL,KAAK,CAAC,eAAe,CAAC,kBAAkB,EAAE,WAAW,CAAC;yCACvD,CAAC;oCACJ,CAAC;iCACF;6BACF;yBACF,CAAC;oBACJ,CAAC;oBAED,OAAO;wBACL,IAAI,EAAE,kBAAkB;wBACxB,OAAO,EAAE;4BACP;gCACE,SAAS,EAAE,+BAA+B;gCAC1C,GAAG,EAAE,CAAC,KAAyB,EAAoB,EAAE,CACnD,KAAK,CAAC,WAAW,CAAC,2BAA2B,EAAE,WAAW,CAAC;6BAC9D;yBACF;qBACF,CAAC;gBACJ,CAAC;gBACD,KAAK,sBAAc,CAAC,YAAY,CAAC,CAAC,CAAC;oBACjC,OAAO;wBACL,IAAI,EAAE,kBAAkB;wBACxB,SAAS,EAAE,qCAAqC;qBACjD,CAAC;gBACJ,CAAC;gBACD,KAAK,sBAAc,CAAC,aAAa,CAAC,CAAC,CAAC;oBAClC,OAAO;wBACL,IAAI,EAAE,kBAAkB;wBACxB,SAAS,EAAE,sCAAsC;qBAClD,CAAC;gBACJ,CAAC;gBACD,KAAK,sBAAc,CAAC,WAAW,CAAC,CAAC,CAAC;oBAChC,MAAM,2BAA2B,GAAG,kBAAkB,CAAC,cAAc,CAAC;oBACtE,IAAI,2BAA2B,IAAI,IAAI,EAAE,CAAC;wBACxC,OAAO;4BACL,IAAI,EAAE,kBAAkB;4BACxB,OAAO,EAAE;gCACP;oCACE,SAAS,EAAE,wCAAwC;oCACnD,GAAG,EAAE,CAAC,KAAK,EAAoB,EAAE,CAC/B,KAAK,CAAC,eAAe,CAAC,kBAAkB,EAAE,aAAa,CAAC;iCAC3D;6BACF;yBACF,CAAC;oBACJ,CAAC;oBACD,OAAO;wBACL,IAAI,EAAE,kBAAkB;wBACxB,OAAO,EAAE;4BACP;gCACE,SAAS,EAAE,mCAAmC;gCAC9C,GAAG,EAAE,CAAC,KAAK,EAAoB,EAAE,CAC/B,KAAK,CAAC,WAAW,CAAC,2BAA2B,EAAE,aAAa,CAAC;6BAChE;yBACF;qBACF,CAAC;gBACJ,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO;YACL,cAAc,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE;gBACxC,IAAI,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB,EAAE,CAAC;oBACpD,OAAO;gBACT,CAAC;gBAED,MAAM,qBAAqB,GAAG,IAAA,iCAA0B,EACtD,MAAM,EACN,OAAO,CACR,CAAC;gBACF,IAAI,CAAC,qBAAqB,EAAE,CAAC;oBAC3B,OAAO;gBACT,CAAC;gBAED,MAAM,iBAAiB,GACrB;oBACE,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE,EAAE,eAAe,EAAE,CAAC,EAAE;oBACnD,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,eAAe,EAAE,CAAC,EAAE;iBAM/D,CAAC,IAAI,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,qBAAqB,KAAK,MAAM,CAAC,CAAC;gBACzD,IAAI,CAAC,iBAAiB,EAAE,CAAC;oBACvB,OAAO;gBACT,CAAC;gBAED,kCAAkC;gBAClC,MAAM,EAAE,eAAe,EAAE,GAAG,IAAI,EAAE,GAAG,iBAAiB,CAAC;gBACvD,IAAI,IAAI,CAAC,MAAM,GAAG,eAAe,GAAG,CAAC,EAAE,CAAC;oBACtC,OAAO;gBACT,CAAC;gBAED,0GAA0G;gBAC1G,yFAAyF;gBACzF,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,eAAe,GAAG,CAAC,CAAC,CAAC;gBACvD,IACE,WAAW,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,IAAI,KAAK,sBAAc,CAAC,aAAa,CAAC,EACrE,CAAC;oBACD,OAAO;gBACT,CAAC;gBAED,IACE,CAAC,OAAO,CAAC,cAAc,CACrB,OAAO,EACP,qBAAqB,CAAC,GAAG,CAAC,MAAM,CAAC,EACjC,OAAO,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CACpE,EACD,CAAC;oBACD,OAAO;gBACT,CAAC;gBAED,iGAAiG;gBACjG,MAAM,IAAI,GAAG,WAAW,CAAC,eAAe,CAGvC,CAAC;gBACF,IAAI,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC7B,mEAAmE;oBACnE,yDAAyD;oBACzD,MAAM,SAAS,GAAG,sBAAsB,CAAC,IAAI,CAAC,CAAC;oBAC/C,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI;wBACJ,SAAS,EAAE,YAAY;wBACvB,IAAI;wBACJ,GAAG,SAAS;qBACb,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}