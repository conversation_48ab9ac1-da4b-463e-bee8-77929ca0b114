{"version": 3, "file": "array-type.js", "sourceRoot": "", "sources": ["../../src/rules/array-type.ts"], "names": [], "mappings": ";;AACA,oDAA0D;AAE1D,kCAAsD;AAEtD;;;GAGG;AACH,SAAS,YAAY,CAAC,IAAmB;IACvC,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;QAClB,KAAK,sBAAc,CAAC,UAAU,CAAC;QAC/B,KAAK,sBAAc,CAAC,YAAY,CAAC;QACjC,KAAK,sBAAc,CAAC,gBAAgB,CAAC;QACrC,KAAK,sBAAc,CAAC,cAAc,CAAC;QACnC,KAAK,sBAAc,CAAC,eAAe,CAAC;QACpC,KAAK,sBAAc,CAAC,eAAe,CAAC;QACpC,KAAK,sBAAc,CAAC,eAAe,CAAC;QACpC,KAAK,sBAAc,CAAC,eAAe,CAAC;QACpC,KAAK,sBAAc,CAAC,eAAe,CAAC;QACpC,KAAK,sBAAc,CAAC,gBAAgB,CAAC;QACrC,KAAK,sBAAc,CAAC,aAAa,CAAC;QAClC,KAAK,sBAAc,CAAC,aAAa,CAAC;QAClC,KAAK,sBAAc,CAAC,WAAW,CAAC;QAChC,KAAK,sBAAc,CAAC,kBAAkB,CAAC;QACvC,KAAK,sBAAc,CAAC,UAAU,CAAC;QAC/B,KAAK,sBAAc,CAAC,eAAe;YACjC,OAAO,IAAI,CAAC;QACd,KAAK,sBAAc,CAAC,eAAe;YACjC,IACE,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;gBAChD,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,OAAO,EAC9B,CAAC;gBACD,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;oBACxB,OAAO,IAAI,CAAC;gBACd,CAAC;gBACD,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAC3C,OAAO,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpD,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;oBACvB,OAAO,KAAK,CAAC;gBACf,CAAC;gBACD,OAAO,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACrC,CAAC;YACD,OAAO,KAAK,CAAC;QACf;YACE,OAAO,KAAK,CAAC;IACjB,CAAC;AACH,CAAC;AAED;;;GAGG;AACH,SAAS,oBAAoB,CAAC,IAAmB;IAC/C,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;QAClB,KAAK,sBAAc,CAAC,eAAe;YACjC,OAAO,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC7C,KAAK,sBAAc,CAAC,WAAW,CAAC;QAChC,KAAK,sBAAc,CAAC,cAAc,CAAC;QACnC,KAAK,sBAAc,CAAC,kBAAkB,CAAC;QACvC,KAAK,sBAAc,CAAC,cAAc,CAAC;QACnC,KAAK,sBAAc,CAAC,WAAW,CAAC;QAChC,KAAK,sBAAc,CAAC,iBAAiB;YACnC,OAAO,IAAI,CAAC;QACd,KAAK,sBAAc,CAAC,UAAU;YAC5B,OAAO,IAAI,CAAC,IAAI,KAAK,eAAe,CAAC;QACvC;YACE,OAAO,KAAK,CAAC;IACjB,CAAC;AACH,CAAC;AAiBD,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,YAAY;IAClB,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EACT,kEAAkE;YACpE,WAAW,EAAE,WAAW;SACzB;QACD,OAAO,EAAE,MAAM;QACf,QAAQ,EAAE;YACR,kBAAkB,EAChB,sGAAsG;YACxG,gBAAgB,EACd,sGAAsG;YACxG,wBAAwB,EACtB,oGAAoG;YACtG,sBAAsB,EACpB,uHAAuH;YACzH,wBAAwB,EACtB,2HAA2H;YAC7H,8BAA8B,EAC5B,qHAAqH;SACxH;QACD,MAAM,EAAE;YACN;gBACE,KAAK,EAAE;oBACL,WAAW,EAAE;wBACX,IAAI,EAAE,QAAQ;wBACd,IAAI,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,cAAc,CAAC;qBAC3C;iBACF;gBACD,oBAAoB,EAAE,KAAK;gBAC3B,UAAU,EAAE;oBACV,OAAO,EAAE;wBACP,IAAI,EAAE,6BAA6B;wBACnC,WAAW,EAAE,4CAA4C;qBAC1D;oBACD,QAAQ,EAAE;wBACR,IAAI,EAAE,6BAA6B;wBACnC,WAAW,EACT,+FAA+F;qBAClG;iBACF;gBACD,IAAI,EAAE,QAAQ;aACf;SACF;KACF;IACD,cAAc,EAAE;QACd;YACE,OAAO,EAAE,OAAO;SACjB;KACF;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC;QACvB,MAAM,aAAa,GAAG,OAAO,CAAC,OAAO,CAAC;QACtC,MAAM,cAAc,GAAG,OAAO,CAAC,QAAQ,IAAI,aAAa,CAAC;QAEzD;;WAEG;QACH,SAAS,cAAc,CAAC,IAAmB;YACzC,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC;gBACvB,OAAO,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAC1C,CAAC;YACD,OAAO,GAAG,CAAC;QACb,CAAC;QAED,OAAO;YACL,WAAW,CAAC,IAAI;gBACd,MAAM,UAAU,GACd,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc;oBAClD,IAAI,CAAC,MAAM,CAAC,QAAQ,KAAK,UAAU,CAAC;gBAEtC,MAAM,aAAa,GAAG,UAAU,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,aAAa,CAAC;gBAElE,IACE,aAAa,KAAK,OAAO;oBACzB,CAAC,aAAa,KAAK,cAAc,IAAI,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,EACpE,CAAC;oBACD,OAAO;gBACT,CAAC;gBAED,MAAM,SAAS,GACb,aAAa,KAAK,SAAS;oBACzB,CAAC,CAAC,oBAAoB;oBACtB,CAAC,CAAC,0BAA0B,CAAC;gBACjC,MAAM,SAAS,GAAG,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC;gBAElD,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,SAAS;oBACf,SAAS;oBACT,IAAI,EAAE;wBACJ,SAAS,EAAE,UAAU,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,OAAO;wBACjD,cAAc,EAAE,UAAU,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE;wBAC7C,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC;qBACvC;oBACD,GAAG,CAAC,KAAK;wBACP,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC;wBAClC,MAAM,SAAS,GAAG,UAAU,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,OAAO,CAAC;wBAEzD,OAAO;4BACL,KAAK,CAAC,gBAAgB,CACpB,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EACvC,GAAG,SAAS,GAAG,CAChB;4BACD,KAAK,CAAC,gBAAgB,CACpB,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EACvC,GAAG,CACJ;yBACF,CAAC;oBACJ,CAAC;iBACF,CAAC,CAAC;YACL,CAAC;YAED,eAAe,CAAC,IAAI;gBAClB,IACE,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;oBAChD,CAAC,CACC,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,OAAO;wBAC9B,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,eAAe;wBACtC,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,UAAU,CAClC;oBACD,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,UAAU;wBAChC,IAAI,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,sBAAc,CAAC,WAAW,CAAC,EACpE,CAAC;oBACD,OAAO;gBACT,CAAC;gBAED,MAAM,8BAA8B,GAClC,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,UAAU;oBACjC,IAAI,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,sBAAc,CAAC,WAAW,CAAC;gBACpE,MAAM,mBAAmB,GACvB,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,eAAe;oBACtC,8BAA8B,CAAC;gBAEjC,MAAM,aAAa,GAAG,mBAAmB;oBACvC,CAAC,CAAC,cAAc;oBAChB,CAAC,CAAC,aAAa,CAAC;gBAElB,IAAI,aAAa,KAAK,SAAS,EAAE,CAAC;oBAChC,OAAO;gBACT,CAAC;gBAED,MAAM,cAAc,GAAG,mBAAmB,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC9D,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE,MAAM,CAAC;gBAC9C,MAAM,SAAS,GACb,aAAa,KAAK,OAAO;oBACvB,CAAC,CAAC,8BAA8B;wBAC9B,CAAC,CAAC,0BAA0B;wBAC5B,CAAC,CAAC,kBAAkB;oBACtB,CAAC,CAAC,mBAAmB,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,eAAe;wBAC7D,CAAC,CAAC,gCAAgC;wBAClC,CAAC,CAAC,wBAAwB,CAAC;gBAEjC,IAAI,CAAC,UAAU,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAC3C,wBAAwB;oBACxB,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI;wBACJ,SAAS;wBACT,IAAI,EAAE;4BACJ,SAAS,EAAE,mBAAmB,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,OAAO;4BAC1D,cAAc;4BACd,IAAI,EAAE,KAAK;yBACZ;wBACD,GAAG,CAAC,KAAK;4BACP,OAAO,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,GAAG,cAAc,OAAO,CAAC,CAAC;wBAC3D,CAAC;qBACF,CAAC,CAAC;oBAEH,OAAO;gBACT,CAAC;gBAED,IACE,UAAU,CAAC,MAAM,KAAK,CAAC;oBACvB,CAAC,aAAa,KAAK,cAAc,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,EAClE,CAAC;oBACD,OAAO;gBACT,CAAC;gBAED,MAAM,IAAI,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;gBAC3B,MAAM,UAAU,GAAG,oBAAoB,CAAC,IAAI,CAAC,CAAC;gBAC9C,MAAM,YAAY,GAChB,cAAc;oBACd,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,WAAW;oBAC/C,CAAC,IAAA,sBAAe,EAAC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;gBAEhE,MAAM,KAAK,GAAG,GAAG,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,cAAc,GACvD,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EACrB,EAAE,CAAC;gBACH,MAAM,GAAG,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,8BAA8B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,GAAG,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;gBAC9G,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,SAAS;oBACT,IAAI,EAAE;wBACJ,SAAS,EAAE,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO;wBAC7D,cAAc;wBACd,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC;qBAC3B;oBACD,GAAG,CAAC,KAAK;wBACP,OAAO;4BACL,KAAK,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC;4BAC7D,KAAK,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC;yBAC5D,CAAC;oBACJ,CAAC;iBACF,CAAC,CAAC;YACL,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}