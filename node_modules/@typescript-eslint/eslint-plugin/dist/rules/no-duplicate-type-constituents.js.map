{"version": 3, "file": "no-duplicate-type-constituents.js", "sourceRoot": "", "sources": ["../../src/rules/no-duplicate-type-constituents.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAC1D,sDAAwC;AAExC,+CAAiC;AAEjC,kCAMiB;AAWjB,MAAM,aAAa,GAAG,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC;AAE1D,MAAM,aAAa,GAAG,CAAC,UAAmB,EAAE,YAAqB,EAAW,EAAE;IAC5E,IAAI,UAAU,KAAK,YAAY,EAAE,CAAC;QAChC,OAAO,IAAI,CAAC;IACd,CAAC;IACD,IACE,UAAU;QACV,YAAY;QACZ,OAAO,UAAU,KAAK,QAAQ;QAC9B,OAAO,YAAY,KAAK,QAAQ,EAChC,CAAC;QACD,IAAI,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC;YAC7D,IAAI,UAAU,CAAC,MAAM,KAAK,YAAY,CAAC,MAAM,EAAE,CAAC;gBAC9C,OAAO,KAAK,CAAC;YACf,CAAC;YACD,OAAO,CAAC,UAAU,CAAC,IAAI,CACrB,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,aAAa,CAAC,OAAO,EAAE,YAAY,CAAC,KAAK,CAAC,CAAC,CACjE,CAAC;QACJ,CAAC;QACD,MAAM,cAAc,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,CACnD,GAAG,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,CAC/B,CAAC;QACF,MAAM,gBAAgB,GAAG,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,MAAM,CACvD,GAAG,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,CAC/B,CAAC;QACF,IAAI,cAAc,CAAC,MAAM,KAAK,gBAAgB,CAAC,MAAM,EAAE,CAAC;YACtD,OAAO,KAAK,CAAC;QACf,CAAC;QACD,IACE,cAAc,CAAC,IAAI,CACjB,aAAa,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,YAAY,EAAE,aAAa,CAAC,CAC7D,EACD,CAAC;YACD,OAAO,KAAK,CAAC;QACf,CAAC;QACD,IACE,cAAc,CAAC,IAAI,CACjB,aAAa,CAAC,EAAE,CACd,CAAC,aAAa,CACZ,UAAU,CAAC,aAAwC,CAAC,EACpD,YAAY,CAAC,aAA0C,CAAC,CACzD,CACJ,EACD,CAAC;YACD,OAAO,KAAK,CAAC;QACf,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC,CAAC;AAEF,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,gCAAgC;IACtC,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EACT,gEAAgE;YAClE,WAAW,EAAE,aAAa;YAC1B,oBAAoB,EAAE,IAAI;SAC3B;QACD,OAAO,EAAE,MAAM;QACf,QAAQ,EAAE;YACR,SAAS,EAAE,4DAA4D;YACvE,WAAW,EACT,6DAA6D;SAChE;QACD,MAAM,EAAE;YACN;gBACE,oBAAoB,EAAE,KAAK;gBAC3B,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,mBAAmB,EAAE;wBACnB,WAAW,EAAE,sCAAsC;wBACnD,IAAI,EAAE,SAAS;qBAChB;oBACD,YAAY,EAAE;wBACZ,WAAW,EAAE,+BAA+B;wBAC5C,IAAI,EAAE,SAAS;qBAChB;iBACF;aACF;SACF;KACF;IACD,cAAc,EAAE;QACd;YACE,mBAAmB,EAAE,KAAK;YAC1B,YAAY,EAAE,KAAK;SACpB;KACF;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,EAAE,mBAAmB,EAAE,YAAY,EAAE,CAAC;QACrD,MAAM,cAAc,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAClD,MAAM,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;QAE/B,SAAS,cAAc,CACrB,IAAwD,EACxD,eAGS;YAET,MAAM,aAAa,GAAG,IAAI,GAAG,EAA2B,CAAC;YACzD,IAAI,CAAC,KAAK,CAAC,MAAM,CACf,CAAC,kBAAkB,EAAE,eAAe,EAAE,EAAE;gBACtC,MAAM,mBAAmB,GACvB,cAAc,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;gBACpD,IAAI,OAAO,CAAC,oBAAoB,CAAC,mBAAmB,CAAC,EAAE,CAAC;oBACtD,OAAO,kBAAkB,CAAC;gBAC5B,CAAC;gBAED,MAAM,MAAM,GAAG,CACb,SAAqB,EACrB,IAA8B,EACxB,EAAE;oBACR,MAAM,2BAA2B,GAAG,CAClC,KAAyB,EACzB,EAAU,EACkB,EAAE,CAC9B,UAAU,CAAC,YAAY,KAAK,EAAE,CAAC,CAAC,eAAe,EAAE;wBAC/C,MAAM,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC;qBAClD,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;oBAEZ,MAAM,8BAA8B,GAAG,2BAA2B,CAChE,QAAQ,EACR,CAAC,CAAC,CACH,CAAC;oBACF,IAAI,6BAAyD,CAAC;oBAC9D,IAAI,mBAAmB,CAAC;oBACxB,IAAI,kBAAkB,CAAC;oBACvB,IAAI,8BAA8B,EAAE,CAAC;wBACnC,mBAAmB,GAAG,UAAU,CAAC,gBAAgB,CAC/C,8BAA8B,EAC9B,eAAe,CAChB,CAAC;wBACF,kBAAkB,GAAG,UAAU,CAAC,cAAc,CAAC,eAAe,EAAE;4BAC9D,KAAK,EAAE,mBAAmB,CAAC,MAAM;yBAClC,CAAC,CAAC;oBACL,CAAC;yBAAM,CAAC;wBACN,6BAA6B,GAAG,IAAA,iBAAU,EACxC,2BAA2B,CAAC,OAAO,EAAE,CAAC,CAAC,EACvC,wBAAiB,CAAC,YAAY,CAC5B,6BAA6B,EAC7B,4BAA4B,CAC7B,CACF,CAAC;wBACF,kBAAkB,GAAG,UAAU,CAAC,gBAAgB,CAC9C,eAAe,EACf,6BAA6B,CAC9B,CAAC;wBACF,mBAAmB,GAAG,UAAU,CAAC,eAAe,CAC9C,eAAe,EACf;4BACE,KAAK,EAAE,kBAAkB,CAAC,MAAM;yBACjC,CACF,CAAC;oBACJ,CAAC;oBACD,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI;wBACJ,SAAS;wBACT,IAAI,EAAE,eAAe;wBACrB,GAAG,EAAE;4BACH,KAAK,EAAE,eAAe,CAAC,GAAG,CAAC,KAAK;4BAChC,GAAG,EAAE,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,eAAe,CAAC,CAAC,GAAG,CAAC,GAAG;yBAC5D;wBACD,GAAG,EAAE,KAAK,CAAC,EAAE,CACX;4BACE,8BAA8B;4BAC9B,GAAG,mBAAmB;4BACtB,eAAe;4BACf,GAAG,kBAAkB;4BACrB,6BAA6B;yBAC9B,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;qBACzD,CAAC,CAAC;gBACL,CAAC,CAAC;gBACF,MAAM,iBAAiB,GACrB,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAC5B,aAAa,CAAC,GAAG,EAAE,eAAe,CAAC,CACpC,IAAI,aAAa,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;gBAC9C,IAAI,iBAAiB,EAAE,CAAC;oBACtB,MAAM,CAAC,WAAW,EAAE;wBAClB,IAAI,EACF,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB;4BAC7C,CAAC,CAAC,cAAc;4BAChB,CAAC,CAAC,OAAO;wBACb,QAAQ,EAAE,UAAU,CAAC,OAAO,CAAC,iBAAiB,CAAC;qBAChD,CAAC,CAAC;oBACH,OAAO,kBAAkB,CAAC;gBAC5B,CAAC;gBACD,eAAe,EAAE,CAAC,mBAAmB,EAAE,MAAM,CAAC,CAAC;gBAC/C,aAAa,CAAC,GAAG,CAAC,mBAAmB,EAAE,eAAe,CAAC,CAAC;gBACxD,OAAO,CAAC,GAAG,kBAAkB,EAAE,eAAe,CAAC,CAAC;YAClD,CAAC,EACD,EAAE,CACH,CAAC;QACJ,CAAC;QAED,OAAO;YACL,GAAG,CAAC,CAAC,mBAAmB,IAAI;gBAC1B,kBAAkB,EAAE,cAAc;aACnC,CAAC;YACF,GAAG,CAAC,CAAC,YAAY,IAAI;gBACnB,WAAW,EAAE,CAAC,IAAI,EAAQ,EAAE,CAC1B,cAAc,CAAC,IAAI,EAAE,CAAC,mBAAmB,EAAE,MAAM,EAAE,EAAE;oBACnD,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM,CAAC;oBACxC,IAAI,mBAAmB,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB,EAAE,CAAC;wBACjE,MAAM,eAAe,GAAG,mBAAmB,CAAC,MAAM,CAAC;wBACnD,IACE,eAAe,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;4BAClD,eAAe,CAAC,QAAQ,EACxB,CAAC;4BACD,MAAM,aAAa,GAAG,eAAe,CAAC,MAAM,CAAC;4BAC7C,IACE,IAAA,+BAAwB,EAAC,aAAa,CAAC;gCACvC,aAAa,CAAC,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC;gCAC9C,OAAO,CAAC,aAAa,CACnB,mBAAmB,EACnB,EAAE,CAAC,SAAS,CAAC,SAAS,CACvB,EACD,CAAC;gCACD,MAAM,CAAC,aAAa,CAAC,CAAC;4BACxB,CAAC;wBACH,CAAC;oBACH,CAAC;gBACH,CAAC,CAAC;aACL,CAAC;SACH,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}