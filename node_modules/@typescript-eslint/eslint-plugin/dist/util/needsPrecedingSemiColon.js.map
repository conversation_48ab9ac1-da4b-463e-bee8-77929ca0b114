{"version": 3, "file": "needsPrecedingSemiColon.js", "sourceRoot": "", "sources": ["../../src/util/needsPrecedingSemiColon.ts"], "names": [], "mappings": ";;AAqEA,0DAuDC;AA3HD,oDAA2E;AAC3E,kEAG4C;AAG5C,wDAAwD;AACxD,0HAA0H;AAC1H,2FAA2F;AAE3F,MAAM,iBAAiB,GAAG,IAAI,GAAG,CAAC;IAChC,sBAAc,CAAC,cAAc;IAC7B,sBAAc,CAAC,iBAAiB;CACjC,CAAC,CAAC;AAEH,wEAAwE;AACxE,MAAM,YAAY,GAAG,IAAI,GAAG,CAAC;IAC3B,sBAAc,CAAC,oBAAoB;IACnC,sBAAc,CAAC,sBAAsB;IACrC,sBAAc,CAAC,iBAAiB;CACjC,CAAC,CAAC;AAEH,MAAM,qBAAqB,GAAG,IAAI,GAAG,CAAC;IACpC,sBAAc,CAAC,UAAU;IACzB,uBAAe,CAAC,OAAO;CACxB,CAAC,CAAC;AAEH,qGAAqG;AACrG,MAAM,qBAAqB,GAAmD;IAC5E,SAAS,EAAE,IAAI;IACf,KAAK,EAAE,sBAAc,CAAC,cAAc;IACpC,QAAQ,EAAE,sBAAc,CAAC,iBAAiB;IAC1C,QAAQ,EAAE,sBAAc,CAAC,iBAAiB;IAC1C,EAAE,EAAE,sBAAc,CAAC,gBAAgB;IACnC,IAAI,EAAE,sBAAc,CAAC,WAAW;IAChC,MAAM,EAAE,sBAAc,CAAC,eAAe;IACtC,KAAK,EAAE,sBAAc,CAAC,eAAe;CACtC,CAAC;AAEF;;;GAGG;AACH,MAAM,WAAW,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;AAE/D;;;GAGG;AACH,MAAM,UAAU,GAAG,IAAI,GAAG,CAAC;IACzB,sBAAc,CAAC,gBAAgB;IAC/B,sBAAc,CAAC,cAAc;IAC7B,sBAAc,CAAC,cAAc;IAC7B,sBAAc,CAAC,YAAY;IAC3B,sBAAc,CAAC,WAAW;IAC1B,sBAAc,CAAC,cAAc;IAC7B,sBAAc,CAAC,aAAa;CAC7B,CAAC,CAAC;AAEH;;;;;;;GAOG;AACH,SAAgB,uBAAuB,CACrC,UAAsB,EACtB,IAAmB;IAEnB,MAAM,SAAS,GAAG,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IAElD,IACE,CAAC,SAAS;QACV,CAAC,SAAS,CAAC,IAAI,KAAK,uBAAe,CAAC,UAAU;YAC5C,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EACnC,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,MAAM,QAAQ,GAAG,UAAU,CAAC,mBAAmB,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAEpE,IAAI,CAAC,QAAQ,EAAE,CAAC;QACd,OAAO,KAAK,CAAC;IACf,CAAC;IAED,IAAI,IAAA,+BAAmB,EAAC,SAAS,CAAC,EAAE,CAAC;QACnC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IACxC,CAAC;IAED,IAAI,IAAA,+BAAmB,EAAC,SAAS,CAAC,EAAE,CAAC;QACnC,OAAO,CACL,CAAC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc;YAC9C,QAAQ,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB;YAC1D,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB,CAAC;YAClE,CAAC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,SAAS;gBACzC,QAAQ,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe,CAAC;YAC1D,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB,CAClD,CAAC;IACJ,CAAC;IAED,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;QACrB,OAAO,KAAK,CAAC;IACf,CAAC;IAED,IAAI,qBAAqB,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;QAC9C,IAAI,iBAAiB,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;YAChD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,OAAO,GAAG,SAAS,CAAC,KAAK,CAAC;QAChC,MAAM,QAAQ,GAAG,qBAAqB,CAAC,OAAO,CAAC,CAAC;QAEhD,OAAO,QAAQ,CAAC,IAAI,KAAK,QAAQ,CAAC;IACpC,CAAC;IAED,IAAI,SAAS,CAAC,IAAI,KAAK,uBAAe,CAAC,MAAM,EAAE,CAAC;QAC9C,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACjD,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC"}